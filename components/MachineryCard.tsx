import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { useRouter } from 'expo-router';
import { useTheme } from '@/context/theme-context';
import { useLanguage } from '@/context/language-context';
import { Machinery } from '@/services/machinery-service';
import { AlertTriangle, Wrench, Droplet, Calendar, Clock, Edit, Trash2, Send, Ban } from 'lucide-react-native';

interface MachineryCardProps {
  machinery: Machinery;
  userRole: "owner" | "admin" | "caretaker";
  onRequestPress?: () => void;
  onEditPress?: () => void;
  onDeletePress?: () => void;
}

const MachineryCard: React.FC<MachineryCardProps> = ({ 
  machinery, 
  userRole,
  onRequestPress,
  onEditPress,
  onDeletePress
}) => {
  const router = useRouter();
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  // Determine if user can edit/delete (owner or admin)
  const canEditDelete = userRole === "owner" || userRole === "admin";
  
  // Only caretakers can request machinery use, and only if it's available
  const canRequest = userRole === "caretaker" && machinery.status === 'working';
  
  // Check if machinery is in use
  const isInUse = machinery.status === 'in_use' || machinery.status === 'in_use_other_farm';

  const getStatusColor = () => {
    switch (machinery.status) {
      case 'working':
        return '#4CAF50';
      case 'maintenance':
        return '#FF9800';
      case 'malfunction':
        return '#F44336';
      case 'in_use':
      case 'in_use_other_farm':
        return '#2196F3';
      default:
        return '#757575';
    }
  };

  const getStatusText = () => {
    switch (machinery.status) {
      case 'working':
        return t('machinery.available');
      case 'maintenance':
        return t('machinery.maintenanceStatus');
      case 'malfunction':
        return t('machinery.malfunction');
      case 'in_use':
      case 'in_use_other_farm':
        return t('machinery.inUse');
      default:
        return t('common.unknown');
    }
  };

  const getStatusIcon = () => {
    switch (machinery.status) {
      case 'working':
        return null; // No icon for working status
      case 'maintenance':
        return <Wrench size={14} color="#FF9800" style={styles.statusIcon} />;
      case 'malfunction':
        return <AlertTriangle size={14} color="#F44336" style={styles.statusIcon} />;
      case 'in_use':
      case 'in_use_other_farm':
        return <Ban size={14} color="#2196F3" style={styles.statusIcon} />;
      default:
        return null;
    }
  };

  const handleCardPress = () => {
    router.push(`/(app)/machinery/${machinery.id}`);
  };

  const handleRequestPress = (e: any) => {
    e.stopPropagation();
    
    if (onRequestPress) {
      onRequestPress();
    } else {
      // Navigate to machinery request screen with pre-filled data
      router.push({
        pathname: '/(app)/machinery/request',
        params: {
          machineryId: machinery.id,
          machineryName: machinery.name,
          machineryType: machinery.type,
          machineryModel: machinery.model,
          type: 'use'
        }
      });
    }
  };

  const handleEditPress = (e: any) => {
    e.stopPropagation();
    
    if (onEditPress) {
      onEditPress();
    } else {
      router.push(`/(app)/machinery/edit?id=${machinery.id}`);
    }
  };
  
  const handleDeletePress = (e: any) => {
    e.stopPropagation();
    
    if (onDeletePress) {
      onDeletePress();
    }
  };

  const getMaintenanceInfo = () => {
    if (!machinery.nextMaintenanceDate) return null;
    
    const nextMaintenance = new Date(machinery.nextMaintenanceDate);
    const today = new Date();
    const diffTime = nextMaintenance.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays <= 7) {
      return {
        color: '#F44336',
        text: diffDays <= 0 
          ? t('common.maintenanceOverdue') 
          : `${diffDays} ${t('common.days')} ${t('common.remaining')}`
      };
    }
    
    return null;
  };

  const maintenanceInfo = getMaintenanceInfo();
  const statusColor = getStatusColor();
  const statusText = getStatusText();
  
  // Default image URL if no image is provided
  const defaultImageUrl = `https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=300&h=200&fit=crop&crop=center`;
  const imageUrl = machinery.imageUrl || defaultImageUrl;
  
  return (
    <TouchableOpacity 
      style={[
        styles.card, 
        { 
          backgroundColor: colors.surface,
          borderColor: colors.border,
        }
      ]} 
      onPress={handleCardPress}
      activeOpacity={0.7}
    >
      {/* Status indicator */}
      <View style={[styles.statusIndicator, { backgroundColor: statusColor }]} />
      
      {/* Card content */}
      <View style={styles.cardContent}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Text style={[styles.title, { color: colors.text }]} numberOfLines={1}>
              {machinery.name}
            </Text>
            <Text style={[styles.model, { color: colors.textSecondary }]} numberOfLines={1}>
              {machinery.model} • {machinery.year}
            </Text>
          </View>
          
          {/* Single Status Badge */}
          <View style={[styles.statusBadge, { backgroundColor: `${statusColor}20`, borderColor: statusColor }]}>
            {getStatusIcon()}
            <Text style={[styles.statusText, { color: statusColor }]}>
              {statusText}
            </Text>
          </View>
        </View>
        
        {/* Image and details */}
        <View style={styles.contentRow}>
          {/* Machinery image */}
          <View style={[styles.imageContainer, { borderColor: colors.border }]}>
            <Image 
              source={{ uri: imageUrl }} 
              style={styles.image} 
              resizeMode="cover"
            />
            {/* In Use overlay on image */}
            {isInUse && (
              <View style={styles.inUseOverlay}>
                <Ban size={16} color="#fff" />
                <Text style={styles.inUseOverlayText}>IN USE</Text>
              </View>
            )}
          </View>
          
          {/* Details */}
          <View style={styles.details}>
            {/* Fuel level */}
            {machinery.currentFuelLevel !== undefined && machinery.fuelCapacity !== undefined && (
              <View style={styles.detailRow}>
                <Droplet size={16} color={colors.textSecondary} style={styles.icon} />
                <Text style={[styles.detailText, { color: colors.textSecondary }]}>
                  {t('common.fuel')}: {machinery.currentFuelLevel}/{machinery.fuelCapacity} L
                </Text>
              </View>
            )}
            
            {/* Next maintenance */}
            {machinery.nextMaintenanceDate && (
              <View style={styles.detailRow}>
                <Calendar size={16} color={maintenanceInfo ? maintenanceInfo.color : colors.textSecondary} style={styles.icon} />
                <Text 
                  style={[
                    styles.detailText, 
                    { 
                      color: maintenanceInfo ? maintenanceInfo.color : colors.textSecondary,
                      fontWeight: maintenanceInfo ? '600' : 'normal'
                    }
                  ]}
                >
                  {t('common.nextMaintenance')}: {new Date(machinery.nextMaintenanceDate).toLocaleDateString()}
                </Text>
              </View>
            )}
            
            {/* Odometer */}
            {machinery.odometerReading !== undefined && (
              <View style={styles.detailRow}>
                <Clock size={16} color={colors.textSecondary} style={styles.icon} />
                <Text style={[styles.detailText, { color: colors.textSecondary }]}>
                  {t('common.odometer')}: {machinery.odometerReading} {t('common.hours')}
                </Text>
              </View>
            )}
          </View>
        </View>
        
        {/* Action buttons */}
        <View style={styles.actions}>
          {/* Request button for caretakers only - only show if machinery is available */}
          {userRole === "caretaker" && (
            <>
              {canRequest ? (
                <TouchableOpacity 
                  style={[styles.requestButton, { backgroundColor: colors.primary }]}
                  onPress={handleRequestPress}
                >
                  <Send size={16} color="#fff" style={styles.buttonIcon} />
                  <Text style={styles.requestButtonText}>
                    {t('common.requestUse')}
                  </Text>
                </TouchableOpacity>
              ) : (
                <View style={[styles.disabledButton, { backgroundColor: colors.textSecondary + '40' }]}>
                  <Ban size={16} color={colors.textSecondary} style={styles.buttonIcon} />
                  <Text style={[styles.disabledButtonText, { color: colors.textSecondary }]}>
                    {isInUse ? t('machinery.inUse') : 
                     machinery.status === 'maintenance' ? t('machinery.maintenanceStatus') :
                     machinery.status === 'malfunction' ? t('machinery.malfunction') :
                     t('machinery.unavailable')}
                  </Text>
                </View>
              )}
            </>
          )}
          
          {/* Edit and Delete buttons for owners and admins */}
          {canEditDelete && (
            <View style={styles.adminActions}>
              <TouchableOpacity 
                style={[styles.iconButton, { backgroundColor: colors.primary }]}
                onPress={handleEditPress}
              >
                <Edit size={16} color="#fff" />
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.iconButton, { backgroundColor: '#F44336' }]}
                onPress={handleDeletePress}
              >
                <Trash2 size={16} color="#fff" />
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    overflow: 'hidden',
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statusIndicator: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: 4,
  },
  cardContent: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  titleContainer: {
    flex: 1,
    marginRight: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 2,
  },
  model: {
    fontSize: 14,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    gap: 6,
  },
  statusIcon: {
    marginRight: 2,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  contentRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  imageContainer: {
    width: 100,
    height: 80,
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  inUseOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(33, 150, 243, 0.9)',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  inUseOverlayText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  details: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'center',
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  icon: {
    marginRight: 6,
  },
  detailText: {
    fontSize: 14,
    flex: 1,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  requestButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  disabledButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  buttonIcon: {
    marginRight: 6,
  },
  requestButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: '600',
  },
  disabledButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  adminActions: {
    flexDirection: 'row',
    gap: 8,
  },
  iconButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default MachineryCard;
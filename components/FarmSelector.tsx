import React, { useState } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Modal, FlatList, Dimensions, TouchableWithoutFeedback, Platform, ActivityIndicator } from 'react-native';
import { ChevronDown, MapPin, X, Plus } from 'lucide-react-native';
import { useTheme } from '@/context/theme-context';
import { useLanguage } from '@/context/language-context';
import { useFarm } from '@/context/farm-context';
import { useAuth } from '@/context/auth-context';
import { router } from 'expo-router';
import { getLocationDisplay } from '@/services/farm-service';

interface FarmSelectorProps {
  onSelectFarm?: (farm: any) => void;
  isLoading?: boolean;
}

const { height } = Dimensions.get('window');

const FarmSelector: React.FC<FarmSelectorProps> = ({ 
  onSelectFarm,
  isLoading = false
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const { theme, colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const { farms, selectedFarm, setSelectedFarm, isLoading: farmsLoading } = useFarm();
  const { user } = useAuth();
  
  const isDarkMode = theme === 'dark';

  const backgroundColor = isDarkMode ? '#1e1e1e' : '#ffffff';
  const textColor = isDarkMode ? '#ffffff' : '#333333';
  const secondaryTextColor = isDarkMode ? '#aaaaaa' : '#666666';
  const borderColor = isDarkMode ? '#333333' : '#e0e0e0';
  const modalBackgroundColor = isDarkMode ? 'rgba(0, 0, 0, 0.7)' : 'rgba(0, 0, 0, 0.5)';
  
  const openModal = () => {
    if (farms && farms.length > 0) {
      setModalVisible(true);
    }
  };

  const closeModal = () => {
    setModalVisible(false);
  };

  const handleSelectFarm = async (farm: any) => {
    console.log("FarmSelector: Selecting farm:", farm.name);
    
    // Close the modal first
    closeModal();
    
    // Update the farm context
    await setSelectedFarm(farm);
    
    // Call the optional callback
    if (onSelectFarm) {
      onSelectFarm(farm);
    }
    
    console.log("FarmSelector: Farm selection completed");
  };

  const handleCreateFarm = () => {
    closeModal();
    if (user?.role === 'owner') {
      router.push('/(app)/(owner)/farms/create');
    }
  };

  const loading = isLoading || farmsLoading;

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.selector,
          { backgroundColor, borderColor },
          selectedFarm ? styles.selectorWithFarm : styles.selectorEmpty,
          isRTL && styles.rtlSelector
        ]}
        onPress={openModal}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator size="small" color={colors.primary} />
        ) : selectedFarm ? (
          <>
            <MapPin size={18} color={colors.primary} style={[styles.icon, isRTL && styles.rtlIcon]} />
            <Text style={[styles.farmName, { color: textColor }]} numberOfLines={1}>
              {selectedFarm.name}
            </Text>
            <ChevronDown size={18} color={secondaryTextColor} />
          </>
        ) : farms && farms.length > 0 ? (
          <>
            <MapPin size={18} color={secondaryTextColor} style={[styles.icon, isRTL && styles.rtlIcon]} />
            <Text style={[styles.placeholder, { color: secondaryTextColor }]}>
              {t('farm.selectFarm')}
            </Text>
            <ChevronDown size={18} color={secondaryTextColor} />
          </>
        ) : (
          <>
            <Plus size={18} color={secondaryTextColor} style={[styles.icon, isRTL && styles.rtlIcon]} />
            <Text style={[styles.placeholder, { color: secondaryTextColor }]}>
              {t('farm.createFarm')}
            </Text>
          </>
        )}
      </TouchableOpacity>

      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={closeModal}
      >
        <TouchableWithoutFeedback onPress={closeModal}>
          <View style={[styles.modalOverlay, { backgroundColor: modalBackgroundColor }]}>
            <TouchableWithoutFeedback>
              <View style={[styles.modalContent, { backgroundColor, borderColor }]}>
                <View style={[styles.modalHeader, isRTL && styles.rtlModalHeader]}>
                  <Text style={[styles.modalTitle, { color: textColor }]}>{t('farm.selectFarm')}</Text>
                  <TouchableOpacity onPress={closeModal} style={styles.closeButton}>
                    <X size={20} color={textColor} />
                  </TouchableOpacity>
                </View>
                
                {farms && farms.length > 0 ? (
                  <>
                    <FlatList
                      data={farms}
                      keyExtractor={(item) => item.id}
                      style={styles.farmList}
                      contentContainerStyle={styles.farmListContent}
                      renderItem={({ item }) => (
                        <TouchableOpacity
                          style={[
                            styles.farmItem,
                            selectedFarm?.id === item.id && [styles.selectedFarmItem, { borderColor: colors.primary }],
                            isRTL && styles.rtlFarmItem
                          ]}
                          onPress={() => handleSelectFarm(item)}
                        >
                          <MapPin 
                            size={20} 
                            color={selectedFarm?.id === item.id ? colors.primary : secondaryTextColor} 
                            style={[styles.farmItemIcon, isRTL && styles.rtlFarmItemIcon]} 
                          />
                          <View style={styles.farmItemContent}>
                            <Text 
                              style={[
                                styles.farmItemName, 
                                { color: textColor, textAlign: isRTL ? 'right' : 'left' },
                                selectedFarm?.id === item.id && { color: colors.primary, fontWeight: '600' }
                              ]}
                            >
                              {item.name}
                            </Text>
                            <Text style={[styles.farmItemLocation, { color: secondaryTextColor, textAlign: isRTL ? 'right' : 'left' }]}>
                              {getLocationDisplay(item)}
                            </Text>
                          </View>
                        </TouchableOpacity>
                      )}
                    />
                    
                    {user?.role === 'owner' && (
                      <TouchableOpacity
                        style={[styles.createFarmButton, { backgroundColor: colors.primary }]}
                        onPress={handleCreateFarm}
                      >
                        <Plus size={20} color="#fff" />
                        <Text style={styles.createFarmButtonText}>{t('farm.createFarm')}</Text>
                      </TouchableOpacity>
                    )}
                  </>
                ) : (
                  <View style={styles.emptyContainer}>
                    <MapPin size={48} color={secondaryTextColor} />
                    <Text style={[styles.emptyTitle, { color: textColor }]}>
                      {t('farm.noFarms')}
                    </Text>
                    <Text style={[styles.emptyText, { color: secondaryTextColor, textAlign: 'center' }]}>
                      {user?.role === 'owner' 
                        ? t('farm.addFirstFarm')
                        : t('farm.noAssignedFarms')
                      }
                    </Text>
                    {user?.role === 'owner' && (
                      <TouchableOpacity
                        style={[styles.createFarmButton, { backgroundColor: colors.primary }]}
                        onPress={handleCreateFarm}
                      >
                        <Plus size={20} color="#fff" />
                        <Text style={styles.createFarmButtonText}>{t('farm.createFarm')}</Text>
                      </TouchableOpacity>
                    )}
                  </View>
                )}
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    zIndex: 1000,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  rtlSelector: {
    flexDirection: 'row-reverse',
  },
  selectorWithFarm: {
    borderWidth: 1,
  },
  selectorEmpty: {
    borderWidth: 1,
    borderStyle: 'dashed',
  },
  icon: {
    marginRight: 8,
  },
  rtlIcon: {
    marginRight: 0,
    marginLeft: 8,
  },
  farmName: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
  placeholder: {
    flex: 1,
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      web: {
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      },
      default: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      },
    }),
  },
  modalContent: {
    width: '90%',
    maxHeight: height * 0.7,
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  rtlModalHeader: {
    flexDirection: 'row-reverse',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  farmList: {
    maxHeight: height * 0.4,
  },
  farmListContent: {
    padding: 8,
  },
  farmItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginVertical: 4,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  rtlFarmItem: {
    flexDirection: 'row-reverse',
  },
  selectedFarmItem: {
    backgroundColor: 'rgba(46, 125, 50, 0.1)',
    borderWidth: 1,
  },
  farmItemIcon: {
    marginRight: 12,
  },
  rtlFarmItemIcon: {
    marginRight: 0,
    marginLeft: 12,
  },
  farmItemContent: {
    flex: 1,
  },
  farmItemName: {
    fontSize: 16,
    marginBottom: 4,
  },
  farmItemLocation: {
    fontSize: 14,
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    marginBottom: 24,
    lineHeight: 20,
  },
  createFarmButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    margin: 16,
  },
  createFarmButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default FarmSelector;
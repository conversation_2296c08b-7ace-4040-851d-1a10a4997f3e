import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Image } from 'expo-image';
import { Truck, Fuel, Settings, Calendar, MapPin } from 'lucide-react-native';
import { useTheme } from '@/context/theme-context';
import { useLanguage } from '@/context/language-context';
import { useLookupStore } from '@/services/lookup_service';

const { width: screenWidth } = Dimensions.get('window');

export interface MachineryItem {
  id: string;
  name: string;
  type: string;
  status: string;
  fuelLevel?: number;
  location?: string;
  imageUrl?: string;
  model?: string;
  year?: number;
}

export interface MachinerySelectionMessageProps {
  machinery: MachineryItem[];
  requestType: 'use' | 'maintenance' | 'fuel';
  onMachinerySelect: (machinery: MachineryItem) => void;
  onCancel: () => void;
}

export const MachinerySelectionMessage: React.FC<MachinerySelectionMessageProps> = ({
  machinery,
  requestType,
  onMachinerySelect,
  onCancel,
}) => {
  const { colors } = useTheme();
  const { isRTL, t } = useLanguage();
  const { getLookupsByCategory } = useLookupStore();

  const getRequestTypeIcon = () => {
    switch (requestType) {
      case 'use':
        return <Truck size={16} color={colors.primary} />;
      case 'maintenance':
        return <Settings size={16} color={colors.primary} />;
      case 'fuel':
        return <Fuel size={16} color={colors.primary} />;
      default:
        return <Truck size={16} color={colors.primary} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'available':
      case 'working':
        return '#4CAF50';
      case 'in_use':
        return '#FF9800';
      case 'maintenance':
        return '#F44336';
      default:
        return colors.textSecondary;
    }
  };

  const getMachineryTypeTitle = (typeId: string) => {
    const machineryTypes = getLookupsByCategory('machineryType');
    const typeItem = machineryTypes.find(item => item.id === typeId);
    if (typeItem) {
      const title = typeItem.title.toLowerCase();
      return t(`machinery.${title}`) || t(`machinery.types.${title}`) || typeItem.title;
    }
    return typeId.charAt(0).toUpperCase() + typeId.slice(1);
  };

  const renderMachineryCard = (item: MachineryItem) => (
    <TouchableOpacity
      key={item.id}
      style={[styles.machineryCard, { borderColor: colors.border }]}
      onPress={() => onMachinerySelect(item)}
      activeOpacity={0.7}
    >
      <View style={[styles.cardContent, isRTL && styles.rtlCardContent]}>
        {/* Image */}
        <View style={styles.imageContainer}>
          {item.imageUrl ? (
            <Image
              source={{ uri: item.imageUrl }}
              style={styles.machineryImage}
              contentFit="cover"
            />
          ) : (
            <View style={[styles.placeholderImage, { backgroundColor: colors.surface }]}>
              <Truck size={24} color={colors.textSecondary} />
            </View>
          )}
        </View>

        {/* Info */}
        <View style={styles.infoContainer}>
          <View style={[styles.headerRow, isRTL && styles.rtlHeaderRow]}>
            <Text style={[styles.machineryName, { color: colors.text }]} numberOfLines={1}>
              {item.name}
            </Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) + '20' }]}>
              <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
                {(() => {
                  switch (item.status) {
                    case 'maintenance':
                      return t('machinery.maintenanceStatus') || t('machinery.maintenance') || t('common.maintenance');
                    case 'working':
                      return t('machinery.working') || t('common.working');
                    case 'malfunction':
                      return t('machinery.malfunction') || t('common.malfunction');
                    case 'in_use':
                    case 'in_use_other_farm':
                      return t('machinery.inUse') || t('common.inUse');
                    default:
                      return t(`machinery.${item.status}`) || item.status;
                  }
                })()}
              </Text>
            </View>
          </View>

          <Text style={[styles.machineryType, { color: colors.textSecondary }]}>
            {getMachineryTypeTitle(item.type)}
            {item.model && ` • ${item.model}`}
            {item.year && ` • ${item.year}`}
          </Text>

          {/* Additional Info */}
          <View style={[styles.additionalInfo, isRTL && styles.rtlAdditionalInfo]}>
            {item.fuelLevel !== undefined && (
              <View style={[styles.infoItem, isRTL && styles.rtlInfoItem]}>
                <Fuel size={12} color={colors.textSecondary} />
                <Text style={[styles.infoText, { color: colors.textSecondary }]}>
                  {item.fuelLevel}%
                </Text>
              </View>
            )}
            
            {item.location && (
              <View style={[styles.infoItem, isRTL && styles.rtlInfoItem]}>
                <MapPin size={12} color={colors.textSecondary} />
                <Text style={[styles.infoText, { color: colors.textSecondary }]} numberOfLines={1}>
                  {item.location}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Arrow */}
        <View style={styles.arrowContainer}>
          <Text style={[styles.arrow, { color: colors.primary }]}>
            {isRTL ? '←' : '→'}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, isRTL && styles.rtlContainer]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <View style={[styles.headerContent, isRTL && styles.rtlHeaderContent]}>
          {getRequestTypeIcon()}
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {t(`chat.select${requestType.charAt(0).toUpperCase() + requestType.slice(1)}Machinery`)}
          </Text>
        </View>
        <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
          {t('chat.selectMachineryFromList')}
        </Text>
      </View>

      {/* Machinery List */}
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        nestedScrollEnabled={true}
      >
        {machinery.map(renderMachineryCard)}
      </ScrollView>

      {/* Cancel Button */}
      <View style={[styles.actionContainer, isRTL && styles.rtlActionContainer]}>
        <TouchableOpacity
          style={[styles.cancelButton, { borderColor: colors.border }]}
          onPress={onCancel}
        >
          <Text style={[styles.cancelButtonText, { color: colors.textSecondary }]}>
            {t('common.cancel')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: screenWidth * 0.9,
    backgroundColor: '#fff',
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    marginVertical: 8,
  },
  rtlContainer: {
    alignSelf: 'flex-end',
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 8,
  },
  rtlHeaderContent: {
    flexDirection: 'row-reverse',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  scrollView: {
    maxHeight: 300,
    padding: 12,
  },
  machineryCard: {
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: '#fff',
  },
  cardContent: {
    flexDirection: 'row',
    padding: 12,
    alignItems: 'center',
  },
  rtlCardContent: {
    flexDirection: 'row-reverse',
  },
  imageContainer: {
    marginRight: 12,
  },
  machineryImage: {
    width: 50,
    height: 50,
    borderRadius: 8,
  },
  placeholderImage: {
    width: 50,
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoContainer: {
    flex: 1,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  rtlHeaderRow: {
    flexDirection: 'row-reverse',
  },
  machineryName: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    marginLeft: 8,
  },
  statusText: {
    fontSize: 11,
    fontWeight: '500',
  },
  machineryType: {
    fontSize: 14,
    marginBottom: 6,
  },
  additionalInfo: {
    flexDirection: 'row',
    gap: 12,
  },
  rtlAdditionalInfo: {
    flexDirection: 'row-reverse',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  rtlInfoItem: {
    flexDirection: 'row-reverse',
  },
  infoText: {
    fontSize: 12,
  },
  arrowContainer: {
    marginLeft: 8,
  },
  arrow: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  actionContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  rtlActionContainer: {
    flexDirection: 'row-reverse',
  },
  cancelButton: {
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Platform,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Calendar } from 'lucide-react-native';
import { useTheme } from '@/context/theme-context';
import { useLanguage } from '@/context/language-context';
import { RequestField } from '@/services/conversational-request-service';

const { width: screenWidth } = Dimensions.get('window');

export interface ConversationalFieldMessageProps {
  field: RequestField;
  onSuggestionPress?: (suggestion: string) => void;
  onDateSelect?: (date: string) => void;
}

export const ConversationalFieldMessage: React.FC<ConversationalFieldMessageProps> = ({
  field,
  onSuggestionPress,
  onDateSelect,
}) => {
  const { colors } = useTheme();
  const { isRTL, t } = useLanguage();
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showPicker, setShowPicker] = useState(false);

  const handleDateChange = (event: any, date?: Date) => {
    if (Platform.OS === 'android') {
      setShowPicker(false);
    }

    if (date && onDateSelect) {
      setSelectedDate(date);
      const dateString = date.toISOString().split('T')[0]; // YYYY-MM-DD format
      onDateSelect(dateString);
    }
  };

  const formatDisplayDate = (date: Date) => {
    return date.toLocaleDateString(isRTL ? 'ur-PK' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <View style={[styles.container, isRTL && styles.rtlContainer]}>
      {/* Question */}
      <View style={styles.questionContainer}>
        <Text style={[styles.questionText, { color: colors.text }]}>
          {field.label}
          {field.required && <Text style={styles.required}> *</Text>}
        </Text>
        {!field.required && (
          <Text style={[styles.optionalText, { color: colors.textSecondary }]}>
            {t('common.optional')}
          </Text>
        )}
      </View>

      {/* Show date picker for date fields */}
      {field.type === 'date' && onDateSelect && (
        <View style={styles.datePickerContainer}>
          <TouchableOpacity
            style={[
              styles.dateButton,
              {
                backgroundColor: colors.surface,
                borderColor: colors.border,
              },
            ]}
            onPress={() => setShowPicker(true)}
            activeOpacity={0.7}
          >
            <Calendar size={20} color={colors.primary} />
            <Text
              style={[
                styles.dateText,
                {
                  color: selectedDate ? colors.text : colors.textSecondary,
                },
              ]}
            >
              {selectedDate ? formatDisplayDate(selectedDate) : field.placeholder || t('common.selectDate')}
            </Text>
          </TouchableOpacity>

          {/* Date Picker */}
          {showPicker && (
            <DateTimePicker
              value={selectedDate || new Date()}
              mode="date"
              display={Platform.OS === 'ios' ? 'spinner' : 'default'}
              onChange={handleDateChange}
              minimumDate={new Date()}
            />
          )}
        </View>
      )}

      {/* Show suggestions for select fields */}
      {field.type === 'select' && field.options && onSuggestionPress && (
        <View style={styles.suggestionsContainer}>
          {field.options.map((option, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.suggestionButton, { borderColor: colors.border }]}
              onPress={() => onSuggestionPress(option)}
              activeOpacity={0.7}
            >
              <Text style={[styles.suggestionText, { color: colors.primary }]}>
                {t(`common.${option.toLowerCase()}`) || t(`request.${option.toLowerCase()}`) || option}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* No action buttons - user responds via main chat input */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: screenWidth * 0.9,
    backgroundColor: '#fff',
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    marginVertical: 8,
    padding: 16,
  },
  rtlContainer: {
    alignSelf: 'flex-end',
  },
  questionContainer: {
    marginBottom: 16,
  },
  questionText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  required: {
    color: '#F44336',
  },
  optionalText: {
    fontSize: 12,
    fontStyle: 'italic',
  },

  datePickerContainer: {
    marginTop: 8,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1.5,
    marginBottom: 8,
  },
  dateText: {
    fontSize: 16,
    marginLeft: 12,
    flex: 1,
  },
  suggestionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  suggestionButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1.5,
    backgroundColor: '#F8F9FA',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  suggestionText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#F8F9FA',
  },
  inputPrefix: {
    fontSize: 14,
    marginRight: 8,
    fontWeight: '500',
  },
  textInput: {
    flex: 1,
    fontSize: 14,
    paddingVertical: 4,
  },
  dateInput: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    gap: 8,
  },
  dateText: {
    fontSize: 14,
  },
  selectContainer: {
    gap: 8,
  },
  selectOption: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  selectOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  actionContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  rtlActionContainer: {
    flexDirection: 'row-reverse',
  },
  skipButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  skipButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  submitButton: {
    flex: 2,
    borderRadius: 8,
    overflow: 'hidden',
  },
  submitButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    gap: 6,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
});

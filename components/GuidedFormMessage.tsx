import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Image } from 'expo-image';
import { CheckCircle, ArrowRight, Calendar, DollarSign, Package, Truck } from 'lucide-react-native';
import { useTheme } from '@/context/theme-context';
import { useLanguage } from '@/context/language-context';

const { width: screenWidth } = Dimensions.get('window');

export interface FormField {
  id: string;
  label: string;
  type: 'text' | 'number' | 'select' | 'date';
  required: boolean;
  value: string;
  options?: string[];
  placeholder?: string;
  prefix?: string;
}

export interface GuidedFormData {
  type: 'inventory' | 'machinery';
  imageUri: string;
  analysisResult: any;
  currentStep: number;
  fields: FormField[];
  isComplete: boolean;
}

export interface GuidedFormMessageProps {
  formData: GuidedFormData;
  onFieldUpdate: (fieldId: string, value: string) => void;
  onNext: () => void;
  onSubmit: () => void;
  onCancel: () => void;
}

export const GuidedFormMessage: React.FC<GuidedFormMessageProps> = ({
  formData,
  onFieldUpdate,
  onNext,
  onSubmit,
  onCancel,
}) => {
  const { colors } = useTheme();
  const { isRTL, t } = useLanguage();
  const [errors, setErrors] = useState<Record<string, string>>({});

  const currentField = formData.fields[formData.currentStep];
  const isLastStep = formData.currentStep === formData.fields.length - 1;
  const progress = ((formData.currentStep + 1) / formData.fields.length) * 100;

  const validateField = (field: FormField): string | null => {
    if (field.required && !field.value.trim()) {
      return t('common.fieldRequired');
    }
    
    if (field.type === 'number' && field.value && isNaN(Number(field.value))) {
      return t('common.invalidNumber');
    }
    
    return null;
  };

  const handleNext = () => {
    if (currentField) {
      const error = validateField(currentField);
      if (error) {
        setErrors({ [currentField.id]: error });
        return;
      }
      setErrors({});
    }
    
    if (isLastStep) {
      onSubmit();
    } else {
      onNext();
    }
  };

  const renderField = (field: FormField) => {
    switch (field.type) {
      case 'select':
        return (
          <View style={styles.selectContainer}>
            {field.options?.map((option, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.selectOption,
                  {
                    backgroundColor: field.value === option ? colors.primary : colors.surface,
                    borderColor: colors.border,
                  },
                ]}
                onPress={() => onFieldUpdate(field.id, option)}
              >
                <Text
                  style={[
                    styles.selectOptionText,
                    {
                      color: field.value === option ? '#fff' : colors.text,
                    },
                  ]}
                >
                  {t(`${formData.type}.${option}`) || option}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        );

      case 'number':
        return (
          <View style={[styles.inputContainer, { borderColor: colors.border }]}>
            {field.prefix && (
              <Text style={[styles.inputPrefix, { color: colors.textSecondary }]}>
                {field.prefix}
              </Text>
            )}
            <TextInput
              style={[
                styles.textInput,
                { color: colors.text, textAlign: isRTL ? 'right' : 'left' },
              ]}
              value={field.value}
              onChangeText={(value) => onFieldUpdate(field.id, value)}
              placeholder={field.placeholder || field.label}
              placeholderTextColor={colors.textSecondary}
              keyboardType="numeric"
            />
          </View>
        );

      default:
        return (
          <View style={[styles.inputContainer, { borderColor: colors.border }]}>
            <TextInput
              style={[
                styles.textInput,
                { color: colors.text, textAlign: isRTL ? 'right' : 'left' },
              ]}
              value={field.value}
              onChangeText={(value) => onFieldUpdate(field.id, value)}
              placeholder={field.placeholder || field.label}
              placeholderTextColor={colors.textSecondary}
            />
          </View>
        );
    }
  };

  const getStepIcon = () => {
    if (formData.type === 'inventory') {
      return <Package size={20} color={colors.primary} />;
    } else {
      return <Truck size={20} color={colors.primary} />;
    }
  };

  return (
    <View style={[styles.container, isRTL && styles.rtlContainer]}>
      {/* Header with Image and Progress */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <View style={[styles.headerContent, isRTL && styles.rtlHeaderContent]}>
          <Image
            source={{ uri: formData.imageUri }}
            style={styles.headerImage}
            contentFit="cover"
          />
          <View style={styles.headerInfo}>
            <View style={[styles.headerTitle, isRTL && styles.rtlHeaderTitle]}>
              {getStepIcon()}
              <Text style={[styles.headerTitleText, { color: colors.text }]}>
                {t(`chat.adding${formData.type.charAt(0).toUpperCase() + formData.type.slice(1)}`)}
              </Text>
            </View>
            <View style={styles.progressContainer}>
              <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
                <View
                  style={[
                    styles.progressFill,
                    { backgroundColor: colors.primary, width: `${progress}%` },
                  ]}
                />
              </View>
              <Text style={[styles.progressText, { color: colors.textSecondary }]}>
                {formData.currentStep + 1} / {formData.fields.length}
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Current Step */}
      {currentField && (
        <View style={styles.stepContainer}>
          <Text style={[styles.stepLabel, { color: colors.text }]}>
            {currentField.label}
            {currentField.required && <Text style={styles.required}> *</Text>}
          </Text>
          
          {renderField(currentField)}
          
          {errors[currentField.id] && (
            <Text style={[styles.errorText, { color: colors.error || '#F44336' }]}>
              {errors[currentField.id]}
            </Text>
          )}
        </View>
      )}

      {/* Action Buttons */}
      <View style={[styles.actionContainer, isRTL && styles.rtlActionContainer]}>
        <TouchableOpacity
          style={[styles.cancelButton, { borderColor: colors.border }]}
          onPress={onCancel}
        >
          <Text style={[styles.cancelButtonText, { color: colors.textSecondary }]}>
            {t('common.cancel')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.nextButton,
            { opacity: currentField?.required && !currentField.value ? 0.5 : 1 },
          ]}
          onPress={handleNext}
          disabled={currentField?.required && !currentField.value}
        >
          <LinearGradient
            colors={[colors.primary, colors.primaryDark]}
            style={styles.nextButtonGradient}
          >
            <Text style={styles.nextButtonText}>
              {isLastStep ? t('common.submit') : t('common.next')}
            </Text>
            {!isLastStep ? (
              <ArrowRight size={16} color="#fff" />
            ) : (
              <CheckCircle size={16} color="#fff" />
            )}
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: screenWidth * 0.9,
    backgroundColor: '#fff',
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    marginVertical: 8,
  },
  rtlContainer: {
    alignSelf: 'flex-end',
  },
  header: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rtlHeaderContent: {
    flexDirection: 'row-reverse',
  },
  headerImage: {
    width: 60,
    height: 45,
    borderRadius: 8,
    marginRight: 12,
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 6,
  },
  rtlHeaderTitle: {
    flexDirection: 'row-reverse',
  },
  headerTitleText: {
    fontSize: 14,
    fontWeight: '600',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  progressBar: {
    flex: 1,
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 11,
    fontWeight: '500',
  },
  stepContainer: {
    padding: 16,
  },
  stepLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  required: {
    color: '#F44336',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#F8F9FA',
  },
  inputPrefix: {
    fontSize: 14,
    marginRight: 8,
    fontWeight: '500',
  },
  textInput: {
    flex: 1,
    fontSize: 14,
    paddingVertical: 4,
  },
  selectContainer: {
    gap: 8,
  },
  selectOption: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  selectOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  actionContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  rtlActionContainer: {
    flexDirection: 'row-reverse',
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  nextButton: {
    flex: 2,
    borderRadius: 8,
    overflow: 'hidden',
  },
  nextButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    gap: 6,
  },
  nextButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
});

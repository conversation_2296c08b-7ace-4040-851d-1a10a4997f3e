import React, { useState, useImperativeHandle, forwardRef } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Alert
} from 'react-native';
import { Send, Mic, Camera, Image as ImageIcon } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { useTheme } from '@/context/theme-context';
import { useLanguage } from '@/context/language-context';

const { width: screenWidth } = Dimensions.get('window');

export interface ChatInputProps {
  onSendMessage: (message: string) => void;
  onSendImage?: (imageUri: string, message?: string) => void;
  onVoicePress?: () => void;
  disabled?: boolean;
  placeholder?: string;
}

export interface ChatInputRef {
  setText: (text: string) => void;
  getText: () => string;
}

export const ChatInput = forwardRef<ChatInputRef, ChatInputProps>(({
  onSendMessage,
  onSendImage,
  onVoicePress,
  disabled = false,
  placeholder,
}, ref) => {
  const { colors } = useTheme();
  const { isRTL, t } = useLanguage();
  const [message, setMessage] = useState('');

  useImperativeHandle(ref, () => ({
    setText: (text: string) => setMessage(text),
    getText: () => message,
  }));

  const handleSend = () => {
    if (message.trim() && !disabled) {
      onSendMessage(message.trim());
      setMessage('');
    }
  };

  const handleImagePicker = () => {
    Alert.alert(
      t('chat.selectImageSource'),
      t('chat.selectImageSourceMessage'),
      [
        {
          text: t('chat.camera'),
          onPress: () => openCamera(),
        },
        {
          text: t('chat.gallery'),
          onPress: () => openGallery(),
        },
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
      ]
    );
  };

  const openCamera = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(t('common.error'), t('chat.cameraPermissionRequired'));
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        onSendImage?.(result.assets[0].uri, message.trim() || undefined);
        setMessage('');
      }
    } catch (error) {
      console.error('Error opening camera:', error);
      Alert.alert(t('common.error'), t('chat.errorOpeningCamera'));
    }
  };

  const openGallery = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(t('common.error'), t('chat.galleryPermissionRequired'));
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        onSendImage?.(result.assets[0].uri, message.trim() || undefined);
        setMessage('');
      }
    } catch (error) {
      console.error('Error opening gallery:', error);
      Alert.alert(t('common.error'), t('chat.errorOpeningGallery'));
    }
  };

  const canSend = message.trim().length > 0 && !disabled;

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.keyboardAvoidingView}
    >
      <View style={[
        styles.container,
        { 
          backgroundColor: colors.background,
          borderTopColor: colors.border
        }
      ]}>
        <View style={[
          styles.inputContainer,
          { 
            backgroundColor: colors.surface,
            borderColor: colors.border
          },
          isRTL && styles.rtlInputContainer
        ]}>
          <TextInput
            style={[
              styles.textInput,
              { 
                color: colors.text,
                textAlign: isRTL ? 'right' : 'left'
              }
            ]}
            value={message}
            onChangeText={setMessage}
            placeholder={placeholder || t('chat.typeMessage')}
            placeholderTextColor={colors.textSecondary}
            multiline
            maxLength={500}
            editable={!disabled}
            textAlignVertical="center"
            returnKeyType="send"
            onSubmitEditing={handleSend}
            blurOnSubmit={false}
          />

          {/* Image Upload Button */}
          {onSendImage && (
            <TouchableOpacity
              style={[
                styles.actionButton,
                styles.imageButton,
                { backgroundColor: colors.surface }
              ]}
              onPress={handleImagePicker}
              disabled={disabled}
            >
              <Camera
                size={20}
                color={disabled ? colors.textSecondary : colors.primary}
              />
            </TouchableOpacity>
          )}

          {/* Voice Button */}
          {onVoicePress && (
            <TouchableOpacity
              style={[
                styles.actionButton,
                styles.voiceButton,
                { backgroundColor: colors.surface }
              ]}
              onPress={onVoicePress}
              disabled={disabled}
            >
              <Mic
                size={20}
                color={disabled ? colors.textSecondary : colors.primary}
              />
            </TouchableOpacity>
          )}

          {/* Send Button */}
          <TouchableOpacity
            style={[
              styles.actionButton,
              styles.sendButton,
              { 
                backgroundColor: canSend ? colors.primary : colors.surface,
                opacity: canSend ? 1 : 0.5
              }
            ]}
            onPress={handleSend}
            disabled={!canSend}
          >
            <Send 
              size={20} 
              color={canSend ? '#fff' : colors.textSecondary} 
            />
          </TouchableOpacity>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
});

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    // KeyboardAvoidingView styles
  },
  container: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    borderRadius: 24,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
    minHeight: 48,
    maxHeight: 120,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  rtlInputContainer: {
    flexDirection: 'row-reverse',
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    lineHeight: 20,
    paddingVertical: 8,
    paddingHorizontal: 4,
    maxHeight: 80,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  imageButton: {
    // Image button specific styles
  },
  voiceButton: {
    // Voice button specific styles
  },
  sendButton: {
    // Send button specific styles
  },
});

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Image } from 'expo-image';
import { Bo<PERSON>, User, CheckCircle, XCircle, HelpCircle, Camera, Zap, ArrowRight } from 'lucide-react-native';
import { MachinerySelectionMessage, MachineryItem } from './MachinerySelectionMessage';
import { ConversationalFieldMessage } from './ConversationalFieldMessage';
import { BrowseItemsMessage } from './BrowseItemsMessage';
import { ConversationContext, RequestField } from '@/services/conversational-request-service';
import { BrowseResult } from '@/services/browse-service';
import { useTheme } from '@/context/theme-context';
import { useLanguage } from '@/context/language-context';

const { width: screenWidth } = Dimensions.get('window');

export interface ChatMessageProps {
  id: string;
  message: string;
  isUser: boolean;
  timestamp: Date;
  success?: boolean;
  suggestions?: string[];
  onSuggestionPress?: (suggestion: string) => void;
  imageUri?: string;
  imageAnalysis?: any; // ImageAnalysisResult type
  // Conversational request props
  machineryList?: MachineryItem[];
  conversationContext?: ConversationContext;
  currentField?: RequestField;
  onMachinerySelect?: (machinery: MachineryItem) => void;
  onRequestCancel?: () => void;
  onDateSelect?: (date: string) => void;
  // Browse props
  browseResult?: BrowseResult;
  onItemSelect?: (item: any, itemType: 'machinery' | 'inventory') => void;
  // Request options props (for admin users)
  requestOptions?: {
    requestType: string;
    options: Array<{
      id: string;
      label: string;
      description: string;
    }>;
  };
  // Requests list props
  requestsList?: {
    requests: any[];
    totalCount: number;
    requestType: string;
    userType: string;
  };
  // Navigation callback for request items
  onRequestPress?: (requestId: string, farmId: string) => void;
}

// Type alias for convenience
export type ChatMessage = ChatMessageProps;

export const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  isUser,
  timestamp,
  success,
  suggestions,
  onSuggestionPress,
  imageUri,
  imageAnalysis,
  machineryList,
  conversationContext,
  currentField,
  onMachinerySelect,
  onFieldSubmit,
  onFieldSkip,
  onRequestCancel,
  onDateSelect,
  browseResult,
  onItemSelect,
  requestOptions,
  requestsList,
  onRequestPress,
}) => {
  const { colors } = useTheme();
  const { isRTL, t } = useLanguage();

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getStatusIcon = () => {
    if (isUser) return null;
    
    if (success === true) {
      return <CheckCircle size={16} color="#4CAF50" />;
    } else if (success === false) {
      return <XCircle size={16} color="#F44336" />;
    } else {
      return <HelpCircle size={16} color={colors.textSecondary} />;
    }
  };

  return (
    <View style={[
      styles.messageContainer,
      isUser ? styles.userMessageContainer : styles.aiMessageContainer,
      isRTL && styles.rtlContainer
    ]}>
      {/* Message Bubble */}
      <View style={[
        styles.messageBubble,
        isUser ? styles.userBubble : styles.aiBubble,
        isRTL && (isUser ? styles.rtlUserBubble : styles.rtlAiBubble)
      ]}>
        {isUser ? (
          <LinearGradient
            colors={[colors.primary, colors.primaryDark]}
            style={styles.userGradient}
          >
            <View style={styles.messageContent}>
              <View style={[styles.messageHeader, isRTL && styles.rtlMessageHeader]}>
                <User size={16} color="#fff" />
                <Text style={[styles.senderText, { color: '#fff' }]}>
                  {t('chat.you')}
                </Text>
              </View>
              <Text style={[styles.messageText, { color: '#fff' }]}>
                {message}
              </Text>

              {/* User Image */}
              {imageUri && (
                <View style={styles.imageContainer}>
                  <Image
                    source={{ uri: imageUri }}
                    style={styles.messageImage}
                    contentFit="cover"
                  />
                  <View style={styles.imageOverlay}>
                    <Camera size={16} color="#fff" />
                  </View>
                </View>
              )}
            </View>
          </LinearGradient>
        ) : (
          <View style={[styles.aiContent, { backgroundColor: colors.surface }]}>
            <View style={styles.messageContent}>
              <View style={[styles.messageHeader, isRTL && styles.rtlMessageHeader]}>
                <Bot size={16} color={colors.primary} />
                <Text style={[styles.senderText, { color: colors.primary }]}>
                  {t('chat.assistant')}
                </Text>
                {getStatusIcon()}
              </View>
              <Text style={[styles.messageText, { color: colors.text }]}>
                {message}
              </Text>

              {/* AI Image Analysis */}
              {imageAnalysis && (
                <View style={styles.analysisContainer}>
                  <View style={[styles.analysisHeader, isRTL && styles.rtlAnalysisHeader]}>
                    <Zap size={14} color={colors.primary} />
                    <Text style={[styles.analysisTitle, { color: colors.primary }]}>
                      {t('chat.imageAnalysis')}
                    </Text>
                  </View>

                  <View style={styles.analysisContent}>
                    <Text style={[styles.analysisType, { color: colors.text }]}>
                      {t(`chat.detected${imageAnalysis.type.charAt(0).toUpperCase() + imageAnalysis.type.slice(1)}`)}
                    </Text>
                    <Text style={[styles.analysisConfidence, { color: colors.textSecondary }]}>
                      {t('chat.confidence')}: {Math.round(imageAnalysis.confidence * 100)}%
                    </Text>

                    {imageAnalysis.extractedData && (
                      <View style={styles.extractedDataContainer}>
                        <Text style={[styles.extractedDataTitle, { color: colors.text }]}>
                          {t('chat.extractedData')}:
                        </Text>
                        {imageAnalysis.type === 'inventory' && (
                          <>
                            <Text style={[styles.extractedDataItem, { color: colors.textSecondary }]}>
                              {t('inventory.name')}: {imageAnalysis.extractedData.name}
                            </Text>
                            <Text style={[styles.extractedDataItem, { color: colors.textSecondary }]}>
                              {t('inventory.category')}: {imageAnalysis.extractedData.category}
                            </Text>
                            {imageAnalysis.extractedData.estimatedQuantity && (
                              <Text style={[styles.extractedDataItem, { color: colors.textSecondary }]}>
                                {t('inventory.quantity')}: {imageAnalysis.extractedData.estimatedQuantity} {imageAnalysis.extractedData.unit}
                              </Text>
                            )}
                          </>
                        )}
                        {imageAnalysis.type === 'machinery' && (
                          <>
                            <Text style={[styles.extractedDataItem, { color: colors.textSecondary }]}>
                              {t('machinery.name')}: {imageAnalysis.extractedData.name}
                            </Text>
                            <Text style={[styles.extractedDataItem, { color: colors.textSecondary }]}>
                              {t('machinery.type')}: {imageAnalysis.extractedData.type}
                            </Text>
                          </>
                        )}
                      </View>
                    )}
                  </View>
                </View>
              )}
            </View>
          </View>
        )}
      </View>

      {/* Timestamp */}
      <Text style={[
        styles.timestamp,
        { color: colors.textSecondary },
        isUser ? styles.userTimestamp : styles.aiTimestamp,
        isRTL && (isUser ? styles.rtlUserTimestamp : styles.rtlAiTimestamp)
      ]}>
        {formatTime(timestamp)}
      </Text>

      {/* Suggestions */}
      {!isUser && suggestions && suggestions.length > 0 && (
        <View style={[styles.suggestionsContainer, isRTL && styles.rtlSuggestionsContainer]}>
          <Text style={[styles.suggestionsTitle, { color: colors.textSecondary }]}>
            {t('chat.tapToSelect')}:
          </Text>
          {suggestions.map((suggestion, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.suggestionButton,
                {
                  backgroundColor: colors.primary + '10',
                  borderColor: colors.primary
                }
              ]}
              onPress={() => onSuggestionPress?.(suggestion)}
              activeOpacity={0.7}
            >
              <Text style={[styles.suggestionText, { color: colors.primary }]}>
                {suggestion}
              </Text>
              <ArrowRight size={14} color={colors.primary} style={styles.suggestionIcon} />
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* Machinery Selection */}
      {!isUser && machineryList && conversationContext && onMachinerySelect && onRequestCancel && (
        <View style={styles.conversationalContainer}>
          <MachinerySelectionMessage
            machinery={machineryList}
            requestType={conversationContext.requestType}
            onMachinerySelect={onMachinerySelect}
            onCancel={onRequestCancel}
          />
        </View>
      )}

      {/* Field Input */}
      {!isUser && currentField && (
        <View style={styles.conversationalContainer}>
          <ConversationalFieldMessage
            field={currentField}
            onSuggestionPress={onSuggestionPress}
            onDateSelect={onDateSelect}
          />
        </View>
      )}

      {/* Browse Items */}
      {!isUser && browseResult && onItemSelect && (
        <View style={styles.conversationalContainer}>
          <BrowseItemsMessage
            browseResult={browseResult}
            onItemSelect={onItemSelect}
          />
        </View>
      )}

      {/* Request Options (for admin users) */}
      {!isUser && requestOptions && (
        <View style={styles.conversationalContainer}>
          <View style={[styles.requestOptionsContainer, { backgroundColor: colors.surface }]}>
            <Text style={[styles.requestOptionsTitle, { color: colors.text }]}>
              {t('chat.selectOption')}:
            </Text>
            {requestOptions.options.map((option, index) => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.requestOptionButton,
                  {
                    backgroundColor: colors.primary + '10',
                    borderColor: colors.primary
                  }
                ]}
                onPress={() => onSuggestionPress?.(option.label)}
                activeOpacity={0.7}
              >
                <View style={styles.requestOptionContent}>
                  <Text style={[styles.requestOptionLabel, { color: colors.primary }]}>
                    {option.label}
                  </Text>
                  <Text style={[styles.requestOptionDescription, { color: colors.textSecondary }]}>
                    {option.description}
                  </Text>
                </View>
                <ArrowRight size={16} color={colors.primary} />
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      {/* Requests List */}
      {!isUser && requestsList && (
        <View style={styles.conversationalContainer}>
          <View style={[styles.requestsListContainer, { backgroundColor: colors.surface }]}>
            {requestsList.requests.length > 0 ? (
              <>
                <Text style={[styles.requestsListTitle, { color: colors.text }]}>
                  {requestsList.requestType === 'all' ? t('chat.allRequests') :
                   requestsList.requestType === 'machinery' ? t('chat.machineryRequests') :
                   t('chat.inventoryRequests')} ({requestsList.totalCount})
                </Text>
                {requestsList.requests.slice(0, 5).map((request, index) => {
                  const statusEmoji = request.status === 'pending' ? '⏳' :
                                     request.status === 'approved' ? '✅' : '❌';
                  const typeEmoji = request.requestType === 'machinery' ? '🚜' : '📦';

                  return (
                    <TouchableOpacity
                      key={request.id || index}
                      style={[styles.requestItem, {
                        borderColor: colors.border,
                        backgroundColor: colors.background === '#121212' ? '#1e1e1e' : '#f8f9fa'
                      }]}
                      onPress={() => onRequestPress?.(request.id, request.farmId)}
                      activeOpacity={0.7}
                    >
                      <View style={styles.requestItemHeader}>
                        <View style={styles.requestItemTitleContainer}>
                          <Text style={[styles.requestItemTitle, { color: colors.text }]}>
                            {typeEmoji} {request.itemName || request.machineryName || 'Unknown Item'}
                          </Text>
                          <View style={[styles.statusBadge, {
                            backgroundColor: request.status === 'pending' ? '#FF9800' + '20' :
                                           request.status === 'approved' ? '#4CAF50' + '20' : '#F44336' + '20'
                          }]}>
                            <Text style={[styles.statusBadgeText, {
                              color: request.status === 'pending' ? '#FF9800' :
                                     request.status === 'approved' ? '#4CAF50' : '#F44336'
                            }]}>
                              {statusEmoji} {request.status}
                            </Text>
                          </View>
                        </View>
                      </View>

                      <View style={styles.requestItemDetails}>
                        <Text style={[styles.requestItemDate, { color: colors.textSecondary }]}>
                          📅 {new Date(request.createdAt).toLocaleDateString()}
                        </Text>

                        {request.quantity && (
                          <Text style={[styles.requestItemQuantity, { color: colors.textSecondary }]}>
                            📊 Quantity: {request.quantity} {request.unit || ''}
                          </Text>
                        )}

                        {request.requestedByName && (
                          <Text style={[styles.requestItemQuantity, { color: colors.textSecondary }]}>
                            👤 Requested by: {request.requestedByName}
                          </Text>
                        )}
                      </View>

                      <View style={styles.requestItemFooter}>
                        <Text style={[styles.tapToViewText, { color: colors.primary }]}>
                          Tap to view details →
                        </Text>
                      </View>
                    </TouchableOpacity>
                  );
                })}
                {requestsList.totalCount > 5 && (
                  <Text style={[styles.requestsListMore, { color: colors.textSecondary }]}>
                    ... and {requestsList.totalCount - 5} more requests
                  </Text>
                )}
              </>
            ) : (
              <Text style={[styles.noRequestsText, { color: colors.textSecondary }]}>
                {t('chat.noRequestsFound')}
              </Text>
            )}
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  messageContainer: {
    marginVertical: 8,
    paddingHorizontal: 16,
  },
  userMessageContainer: {
    alignItems: 'flex-end',
  },
  aiMessageContainer: {
    alignItems: 'flex-start',
  },
  rtlContainer: {
    // RTL adjustments handled by individual elements
  },
  messageBubble: {
    maxWidth: screenWidth * 0.8,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  userBubble: {
    borderBottomRightRadius: 4,
  },
  aiBubble: {
    borderBottomLeftRadius: 4,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  rtlUserBubble: {
    borderBottomRightRadius: 16,
    borderBottomLeftRadius: 4,
  },
  rtlAiBubble: {
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 4,
  },
  userGradient: {
    padding: 12,
  },
  aiContent: {
    padding: 12,
  },
  messageContent: {
    // Content styling
  },
  messageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 6,
  },
  rtlMessageHeader: {
    flexDirection: 'row-reverse',
  },
  senderText: {
    fontSize: 12,
    fontWeight: '600',
    flex: 1,
  },
  messageText: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'left',
  },
  timestamp: {
    fontSize: 11,
    marginTop: 4,
    marginHorizontal: 4,
  },
  userTimestamp: {
    textAlign: 'right',
  },
  aiTimestamp: {
    textAlign: 'left',
  },
  rtlUserTimestamp: {
    textAlign: 'left',
  },
  rtlAiTimestamp: {
    textAlign: 'right',
  },
  suggestionsContainer: {
    marginTop: 12,
    maxWidth: screenWidth * 0.8,
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'flex-start',
  },
  rtlSuggestionsContainer: {
    alignSelf: 'flex-end',
  },
  suggestionsTitle: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 6,
  },
  suggestionButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1.5,
    marginBottom: 6,
    marginRight: 8,
    alignSelf: 'flex-start',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    flexDirection: 'row',
    alignItems: 'center',
  },
  suggestionText: {
    fontSize: 14,
    textAlign: 'center',
    fontWeight: '500',
    flex: 1,
  },
  suggestionIcon: {
    marginLeft: 6,
  },
  imageContainer: {
    marginTop: 8,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  messageImage: {
    width: 200,
    height: 150,
    borderRadius: 8,
  },
  imageOverlay: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 12,
    padding: 4,
  },
  analysisContainer: {
    marginTop: 8,
    padding: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#2196F3',
  },
  analysisHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 4,
  },
  rtlAnalysisHeader: {
    flexDirection: 'row-reverse',
  },
  analysisTitle: {
    fontSize: 12,
    fontWeight: '600',
  },
  analysisContent: {
    marginTop: 4,
  },
  analysisType: {
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 2,
  },
  analysisConfidence: {
    fontSize: 11,
    marginBottom: 4,
  },
  extractedDataContainer: {
    marginTop: 4,
  },
  extractedDataTitle: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 2,
  },
  extractedDataItem: {
    fontSize: 11,
    marginBottom: 1,
    paddingLeft: 8,
  },
  conversationalContainer: {
    marginTop: 12,
  },
  // Request Options Styles
  requestOptionsContainer: {
    padding: 16,
    borderRadius: 12,
    marginTop: 8,
  },
  requestOptionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  requestOptionButton: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  requestOptionContent: {
    flex: 1,
  },
  requestOptionLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  requestOptionDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  // Requests List Styles
  requestsListContainer: {
    padding: 16,
    borderRadius: 12,
    marginTop: 8,
  },
  requestsListTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  requestItem: {
    padding: 16,
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  requestItemHeader: {
    marginBottom: 8,
  },
  requestItemTitleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  requestItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
  },
  requestItemStatus: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  statusBadgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  requestItemDetails: {
    marginBottom: 8,
  },
  requestItemDate: {
    fontSize: 12,
    marginBottom: 4,
  },
  requestItemQuantity: {
    fontSize: 12,
    marginBottom: 2,
  },
  requestItemFooter: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
    paddingTop: 8,
    alignItems: 'center',
  },
  tapToViewText: {
    fontSize: 12,
    fontWeight: '500',
  },
  requestsListMore: {
    fontSize: 12,
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 8,
  },
  noRequestsText: {
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
    padding: 20,
  },
});

import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated, Dimensions } from 'react-native';
import { Bot } from 'lucide-react-native';
import { useTheme } from '@/context/theme-context';
import { useLanguage } from '@/context/language-context';

const { width: screenWidth } = Dimensions.get('window');

export const TypingIndicator: React.FC = () => {
  const { colors } = useTheme();
  const { isRTL, t } = useLanguage();
  
  const dot1 = useRef(new Animated.Value(0)).current;
  const dot2 = useRef(new Animated.Value(0)).current;
  const dot3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animateDots = () => {
      const duration = 600;
      const delay = 200;

      const createAnimation = (dot: Animated.Value, delayTime: number) => {
        return Animated.loop(
          Animated.sequence([
            Animated.delay(delayTime),
            Animated.timing(dot, {
              toValue: 1,
              duration: duration / 2,
              useNativeDriver: true,
            }),
            Animated.timing(dot, {
              toValue: 0,
              duration: duration / 2,
              useNativeDriver: true,
            }),
          ])
        );
      };

      Animated.parallel([
        createAnimation(dot1, 0),
        createAnimation(dot2, delay),
        createAnimation(dot3, delay * 2),
      ]).start();
    };

    animateDots();
  }, [dot1, dot2, dot3]);

  return (
    <View style={[
      styles.container,
      isRTL && styles.rtlContainer
    ]}>
      <View style={[
        styles.bubble,
        { 
          backgroundColor: colors.surface,
          borderColor: colors.border
        }
      ]}>
        <View style={[styles.header, isRTL && styles.rtlHeader]}>
          <Bot size={16} color={colors.primary} />
          <Text style={[styles.assistantText, { color: colors.primary }]}>
            {t('chat.assistant')}
          </Text>
        </View>
        
        <View style={[styles.typingContainer, isRTL && styles.rtlTypingContainer]}>
          <Text style={[styles.typingText, { color: colors.textSecondary }]}>
            {t('chat.typing')}
          </Text>
          <View style={styles.dotsContainer}>
            <Animated.View
              style={[
                styles.dot,
                { backgroundColor: colors.textSecondary },
                {
                  opacity: dot1,
                  transform: [
                    {
                      scale: dot1.interpolate({
                        inputRange: [0, 1],
                        outputRange: [1, 1.2],
                      }),
                    },
                  ],
                },
              ]}
            />
            <Animated.View
              style={[
                styles.dot,
                { backgroundColor: colors.textSecondary },
                {
                  opacity: dot2,
                  transform: [
                    {
                      scale: dot2.interpolate({
                        inputRange: [0, 1],
                        outputRange: [1, 1.2],
                      }),
                    },
                  ],
                },
              ]}
            />
            <Animated.View
              style={[
                styles.dot,
                { backgroundColor: colors.textSecondary },
                {
                  opacity: dot3,
                  transform: [
                    {
                      scale: dot3.interpolate({
                        inputRange: [0, 1],
                        outputRange: [1, 1.2],
                      }),
                    },
                  ],
                },
              ]}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    paddingHorizontal: 16,
    alignItems: 'flex-start',
  },
  rtlContainer: {
    alignItems: 'flex-end',
  },
  bubble: {
    maxWidth: screenWidth * 0.6,
    borderRadius: 16,
    borderBottomLeftRadius: 4,
    borderWidth: 1,
    padding: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 6,
  },
  rtlHeader: {
    flexDirection: 'row-reverse',
  },
  assistantText: {
    fontSize: 12,
    fontWeight: '600',
  },
  typingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  rtlTypingContainer: {
    flexDirection: 'row-reverse',
  },
  typingText: {
    fontSize: 14,
  },
  dotsContainer: {
    flexDirection: 'row',
    gap: 3,
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
});

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  Dimensions,
  Platform,
  TextInput,
} from 'react-native';
import { ChevronDown, Check } from 'lucide-react-native';
import { useTheme } from '@/context/theme-context';

const { height: screenHeight } = Dimensions.get('window');

export interface DropdownItem {
  label: string;
  value: string;
  icon?: string;
}

interface ModalDropdownProps {
  items: DropdownItem[];
  selectedValue?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: boolean;
  style?: any;
  dropdownStyle?: any;
  textStyle?: any;
  placeholderStyle?: any;
  maxHeight?: number;
  searchable?: boolean;
  multiple?: boolean;
  selectedValues?: string[];
  onMultipleChange?: (values: string[]) => void;
}

export default function ModalDropdown({
  items,
  selectedValue,
  onValueChange,
  placeholder = 'Select an option',
  disabled = false,
  error = false,
  style,
  dropdownStyle,
  textStyle,
  placeholderStyle,
  maxHeight = screenHeight * 0.6,
  searchable = false,
  multiple = false,
  selectedValues = [],
  onMultipleChange,
}: ModalDropdownProps) {
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';
  const [modalVisible, setModalVisible] = useState(false);
  const [searchText, setSearchText] = useState('');

  const colors = {
    background: isDarkMode ? '#121212' : '#ffffff',
    surface: isDarkMode ? '#1e1e1e' : '#f5f5f5',
    card: isDarkMode ? '#252525' : '#ffffff',
    text: isDarkMode ? '#ffffff' : '#333333',
    textSecondary: isDarkMode ? '#aaaaaa' : '#666666',
    border: isDarkMode ? '#333333' : '#e0e0e0',
    primary: isDarkMode ? '#4ade80' : '#2E7D32',
    error: isDarkMode ? '#ef4444' : '#dc2626',
    overlay: 'rgba(0, 0, 0, 0.5)',
  };

  const filteredItems = searchable
    ? items.filter(item =>
        item.label.toLowerCase().includes(searchText.toLowerCase())
      )
    : items;

  const hasValidSelection = () => {
    if (multiple) {
      return selectedValues.length > 0;
    } else {
      return selectedValue && selectedValue.trim() !== '' && getSelectedLabel() !== placeholder;
    }
  };

  const getSelectedLabel = () => {
    if (multiple) {
      if (selectedValues.length === 0) return placeholder;
      if (selectedValues.length === 1) {
        const item = items.find(item => item.value === selectedValues[0]);
        return item?.label || placeholder;
      }
      return `${selectedValues.length} items selected`;
    } else {
      // Try exact match first
      let selectedItem = items.find(item => item.value === selectedValue);

      // If no exact match, try case-insensitive match
      if (!selectedItem && selectedValue) {
        selectedItem = items.find(item =>
          item.value.toLowerCase().trim() === selectedValue.toLowerCase().trim()
        );
      }

      // If still no match, try label match
      if (!selectedItem && selectedValue) {
        selectedItem = items.find(item =>
          item.label.toLowerCase().trim() === selectedValue.toLowerCase().trim()
        );
      }

      return selectedItem?.label || (selectedValue || placeholder);
    }
  };

  const handleItemPress = (item: DropdownItem) => {
    if (multiple) {
      const newSelectedValues = selectedValues.includes(item.value)
        ? selectedValues.filter(val => val !== item.value)
        : [...selectedValues, item.value];
      onMultipleChange?.(newSelectedValues);
    } else {
      onValueChange(item.value);
      setModalVisible(false);
    }
  };

  const isSelected = (item: DropdownItem) => {
    if (multiple) {
      return selectedValues.includes(item.value);
    } else {
      // Try exact match first
      if (selectedValue === item.value) return true;

      // Try case-insensitive match
      if (selectedValue && item.value.toLowerCase().trim() === selectedValue.toLowerCase().trim()) {
        return true;
      }

      // Try label match
      if (selectedValue && item.label.toLowerCase().trim() === selectedValue.toLowerCase().trim()) {
        return true;
      }

      return false;
    }
  };

  const openModal = () => {
    if (!disabled) {
      setModalVisible(true);
      setSearchText('');
    }
  };

  const closeModal = () => {
    setModalVisible(false);
    setSearchText('');
  };

  return (
    <>
      {/* Dropdown Button */}
      <TouchableOpacity
        style={[
          styles.dropdownButton,
          {
            backgroundColor: colors.surface,
            borderColor: error ? colors.error : colors.border,
          },
          disabled && styles.disabled,
          style,
        ]}
        onPress={openModal}
        disabled={disabled}
      >
        <Text
          style={[
            styles.dropdownText,
            {
              color: hasValidSelection() ? colors.text : colors.textSecondary,
            },
            hasValidSelection() ? textStyle : placeholderStyle,
          ]}
          numberOfLines={1}
        >
          {getSelectedLabel()}
        </Text>
        <ChevronDown
          size={20}
          color={disabled ? colors.textSecondary : colors.text}
          style={[
            styles.chevron,
            modalVisible && styles.chevronRotated,
          ]}
        />
      </TouchableOpacity>

      {/* Modal */}
      <Modal
        visible={modalVisible}
        transparent
        animationType="fade"
        onRequestClose={closeModal}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={closeModal}
        >
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: colors.card,
                borderColor: colors.border,
                maxHeight,
              },
              dropdownStyle,
            ]}
          >
            {/* Header */}
            <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                {placeholder}
              </Text>
              {multiple && (
                <TouchableOpacity onPress={closeModal}>
                  <Text style={[styles.doneButton, { color: colors.primary }]}>
                    Done
                  </Text>
                </TouchableOpacity>
              )}
            </View>

            {/* Search Input */}
            {searchable && (
              <View style={[styles.searchContainer, { borderBottomColor: colors.border }]}>
                <TextInput
                  style={[
                    styles.searchInput,
                    {
                      backgroundColor: colors.surface,
                      borderColor: colors.border,
                      color: colors.text,
                    },
                  ]}
                  placeholder="Search..."
                  placeholderTextColor={colors.textSecondary}
                  value={searchText}
                  onChangeText={setSearchText}
                />
              </View>
            )}

            {/* Items List */}
            <FlatList
              data={filteredItems}
              keyExtractor={(item) => item.value}
              showsVerticalScrollIndicator={false}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.modalItem,
                    { borderBottomColor: colors.border },
                    isSelected(item) && {
                      backgroundColor: colors.primary + '10',
                    },
                  ]}
                  onPress={() => handleItemPress(item)}
                >
                  <View style={styles.itemContent}>
                    {item.icon && (
                      <Text style={styles.itemIcon}>{item.icon}</Text>
                    )}
                    <Text
                      style={[
                        styles.modalItemText,
                        {
                          color: isSelected(item) ? colors.primary : colors.text,
                          fontWeight: isSelected(item) ? '600' : 'normal',
                        },
                      ]}
                    >
                      {item.label}
                    </Text>
                  </View>
                  {isSelected(item) && (
                    <Check size={20} color={colors.primary} />
                  )}
                </TouchableOpacity>
              )}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                    No items found
                  </Text>
                </View>
              }
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 8,
    minHeight: 48,
  },
  disabled: {
    opacity: 0.6,
  },
  dropdownText: {
    flex: 1,
    fontSize: 16,
  },
  chevron: {
    marginLeft: 8,
    transform: [{ rotate: '0deg' }],
  },
  chevronRotated: {
    transform: [{ rotate: '180deg' }],
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 12,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.25,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  doneButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  searchInput: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderRadius: 8,
    fontSize: 16,
  },
  modalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 0.5,
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  itemIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  modalItemText: {
    fontSize: 16,
    flex: 1,
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    fontStyle: 'italic',
  },
});

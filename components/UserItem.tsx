import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Image } from 'react-native';
import { User as UserType } from '@/services/user-service';
import { ChevronRight, Mail, Phone, MapPin, Shield, Calendar, Edit, Trash2, User, Building } from 'lucide-react-native';
import { useRouter } from 'expo-router';

interface UserItemProps {
  user: UserType;
  onPress?: () => void;
  onEditPress?: () => void;
  onDeletePress?: () => void;
  showEditOptions?: boolean;
  backgroundColor?: string;
  textColor?: string;
  secondaryTextColor?: string;
  borderColor?: string;
  isDarkMode?: boolean;
  role?: string;
  currentUserId?: string;
}

export const UserItem: React.FC<UserItemProps> = ({
  user,
  onPress,
  onEditPress,
  onDeletePress,
  showEditOptions = false,
  backgroundColor = '#ffffff',
  textColor = '#333333',
  secondaryTextColor = '#666666',
  borderColor = '#e0e0e0',
  isDarkMode = false,
  role = 'caretaker',
  currentUserId,
}) => {
  const router = useRouter();
  
  // Format date safely
  const formatDate = (dateValue?: string | any) => {
    if (!dateValue) return "Not available";

    try {
      let date: Date;

      if (typeof dateValue === 'string') {
        date = new Date(dateValue);
      } else if (dateValue.seconds) {
        // Firestore timestamp
        date = new Date(dateValue.seconds * 1000);
      } else if (dateValue.toDate) {
        // Firestore timestamp with toDate method
        date = dateValue.toDate();
      } else {
        date = new Date(dateValue);
      }

      if (isNaN(date.getTime())) {
        return "Not available";
      }

      return date.toLocaleDateString();
    } catch (error) {
      console.error('Error formatting date:', error);
      return "Not available";
    }
  };
  
  // Get role color and info
  const getRoleInfo = () => {
    switch (user.role) {
      case 'owner':
        return {
          color: isDarkMode ? '#4CAF50' : '#2E7D32',
          bgColor: isDarkMode ? '#4CAF5015' : '#4CAF5010',
          name: 'Owner',
          icon: Shield
        };
      case 'admin':
        return {
          color: isDarkMode ? '#2196F3' : '#1976D2',
          bgColor: isDarkMode ? '#2196F315' : '#2196F310',
          name: 'Admin',
          icon: Shield
        };
      case 'caretaker':
        return {
          color: isDarkMode ? '#FF9800' : '#F57C00',
          bgColor: isDarkMode ? '#FF980015' : '#FF980010',
          name: 'Caretaker',
          icon: User
        };
      default:
        return {
          color: isDarkMode ? '#757575' : '#9E9E9E',
          bgColor: isDarkMode ? '#75757515' : '#75757510',
          name: 'User',
          icon: User
        };
    }
  };

  // Get user initials for avatar
  const getUserInitials = () => {
    const name = user.displayName || user.name || "U";
    const nameParts = name.split(' ');
    if (nameParts.length === 1) {
      return nameParts[0].charAt(0).toUpperCase();
    } else {
      return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
    }
  };

  // Get phone number safely
  const getPhoneNumber = () => {
    return user.phoneNumber || user.phone || "";
  };

  // Get address safely - handle location objects
  const getAddress = () => {
    if (user.address) {
      // If address is a string, return it
      if (typeof user.address === 'string') {
        return user.address;
      }
      
      // If address is an object, try to extract meaningful text
      if (typeof user.address === 'object' && user.address !== null) {
        const addressObj = user.address as any;
        if (addressObj.address && typeof addressObj.address === 'string') {
          return addressObj.address;
        }
        
        if (addressObj.latitude && addressObj.longitude) {
          return `${String(addressObj.latitude)}, ${String(addressObj.longitude)}`;
        }
        
        // If it's an object but we can't extract meaningful data, return empty
        return "";
      }
    }
    
    return "";
  };

  // Check if current user can edit this user
  const canEdit = () => {
    if (!showEditOptions || !currentUserId) return false;
    
    // Users can't edit themselves in this context
    if (user.uid === currentUserId || user.id === currentUserId) return false;
    
    // Owner can edit everyone
    if (role === 'owner') return true;
    
    // Admin can only edit caretakers
    if (role === 'admin' && user.role === 'caretaker') return true;
    
    return false;
  };

  // Handle navigation to user details
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      router.push(`/users/${user.uid}`);
    }
  };

  const roleInfo = getRoleInfo();
  const RoleIcon = roleInfo.icon;

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor, borderColor }]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      {/* Header Section */}
      <View style={styles.header}>
        <View style={styles.avatarSection}>
          {user.photoURL ? (
            <Image 
              source={{ uri: user.photoURL }}
              style={styles.avatar}
              resizeMode="cover"
            />
          ) : (
            <View style={[styles.avatarPlaceholder, { backgroundColor: roleInfo.color }]}>
              <Text style={styles.avatarInitials}>
                {getUserInitials()}
              </Text>
            </View>
          )}
          
          {/* Online Status Indicator */}
          <View style={[styles.statusIndicator, { backgroundColor: '#4CAF50' }]} />
        </View>
        
        <View style={styles.userInfo}>
          <View style={styles.nameRow}>
            <Text style={[styles.userName, { color: textColor }]} numberOfLines={1}>
              {user.displayName || user.name || "Unnamed User"}
            </Text>
            <View style={[styles.roleBadge, { backgroundColor: roleInfo.bgColor }]}>
              <RoleIcon size={12} color={roleInfo.color} />
              <Text style={[styles.roleText, { color: roleInfo.color }]}>
                {roleInfo.name}
              </Text>
            </View>
          </View>
          
          <View style={styles.emailRow}>
            <Mail size={14} color={secondaryTextColor} />
            <Text style={[styles.emailText, { color: secondaryTextColor }]} numberOfLines={1}>
              {user.email || "No email"}
            </Text>
          </View>
        </View>
        
        {showEditOptions && canEdit() ? (
          <View style={styles.actionButtons}>
            {onEditPress && (
              <TouchableOpacity
                style={[styles.actionButton, styles.editButton]}
                onPress={onEditPress}
              >
                <Edit size={16} color="#2196F3" />
              </TouchableOpacity>
            )}
            {onDeletePress && (
              <TouchableOpacity
                style={[styles.actionButton, styles.deleteButton]}
                onPress={onDeletePress}
              >
                <Trash2 size={16} color="#F44336" />
              </TouchableOpacity>
            )}
          </View>
        ) : (
          <ChevronRight size={20} color={secondaryTextColor} style={styles.chevron} />
        )}
      </View>

      {/* Details Section */}
      <View style={styles.detailsSection}>
        <View style={styles.detailsGrid}>
          {getPhoneNumber() && (
            <View style={styles.detailItem}>
              <View style={[styles.detailIcon, { backgroundColor: '#2196F315' }]}>
                <Phone size={14} color="#2196F3" />
              </View>
              <View style={styles.detailContent}>
                <Text style={[styles.detailLabel, { color: secondaryTextColor }]}>Phone</Text>
                <Text style={[styles.detailValue, { color: textColor }]} numberOfLines={1}>
                  {getPhoneNumber()}
                </Text>
              </View>
            </View>
          )}
          
          {getAddress() && (
            <View style={styles.detailItem}>
              <View style={[styles.detailIcon, { backgroundColor: '#FF980315' }]}>
                <MapPin size={14} color="#FF9803" />
              </View>
              <View style={styles.detailContent}>
                <Text style={[styles.detailLabel, { color: secondaryTextColor }]}>Address</Text>
                <Text style={[styles.detailValue, { color: textColor }]} numberOfLines={2}>
                  {getAddress()}
                </Text>
              </View>
            </View>
          )}
        </View>

        {/* Bio Section */}
        {user.bio && (
          <View style={styles.bioSection}>
            <Text style={[styles.bioText, { color: secondaryTextColor }]} numberOfLines={2}>
              {user.bio}
            </Text>
          </View>
        )}

        {/* Footer Info */}
        <View style={styles.footerInfo}>
          <View style={styles.footerItem}>
            <Calendar size={12} color={secondaryTextColor} />
            <Text style={[styles.footerText, { color: secondaryTextColor }]}>
              Joined {formatDate(user.createdAt)}
            </Text>
          </View>
          
          {user.assignedFarmIds && user.assignedFarmIds.length > 0 && (
            <View style={styles.footerItem}>
              <Building size={12} color={secondaryTextColor} />
              <Text style={[styles.footerText, { color: secondaryTextColor }]}>
                {user.assignedFarmIds.length} farm{user.assignedFarmIds.length > 1 ? 's' : ''}
              </Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    marginBottom: 16,
    borderWidth: 1,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 12,
  },
  avatarSection: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  avatarPlaceholder: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarInitials: {
    fontSize: 20,
    fontWeight: '700',
    color: '#fff',
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 14,
    height: 14,
    borderRadius: 7,
    borderWidth: 2,
    borderColor: '#fff',
  },
  userInfo: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  userName: {
    fontSize: 18,
    fontWeight: '700',
    flex: 1,
    marginRight: 8,
  },
  roleBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  roleText: {
    fontSize: 11,
    fontWeight: '600',
  },
  emailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  emailText: {
    fontSize: 14,
    flex: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
    marginLeft: 8,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
  },
  editButton: {
    backgroundColor: '#2196F315',
    borderColor: '#2196F330',
  },
  deleteButton: {
    backgroundColor: '#F4433615',
    borderColor: '#F4433630',
  },
  chevron: {
    marginLeft: 8,
  },
  detailsSection: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  detailsGrid: {
    gap: 12,
    marginBottom: 12,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  detailIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  detailContent: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  bioSection: {
    marginTop: 8,
    marginBottom: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  bioText: {
    fontSize: 14,
    lineHeight: 20,
    fontStyle: 'italic',
  },
  footerInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  footerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  footerText: {
    fontSize: 12,
    fontWeight: '500',
  },
});
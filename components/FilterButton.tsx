import React from 'react';
import { StyleSheet, Text, TouchableOpacity, Dimensions } from 'react-native';
import { useLanguage } from '@/context/language-context';

const { width: screenWidth } = Dimensions.get('window');

interface FilterButtonProps {
  label: string;
  active: boolean;
  onPress: () => void;
  activeColor?: string;
  isDarkMode?: boolean;
}

export const FilterButton: React.FC<FilterButtonProps> = ({
  label,
  active,
  onPress,
  activeColor = '#2196F3',
  isDarkMode = false,
}) => {
  const { isRTL } = useLanguage();
  
  return (
    <TouchableOpacity
      style={[
        styles.button,
        active
          ? [styles.activeButton, { backgroundColor: activeColor + '20', borderColor: activeColor }]
          : {
              backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
              borderColor: isDarkMode ? '#333333' : '#e0e0e0',
            },
      ]}
      onPress={onPress}
    >
      <Text
        style={[
          styles.buttonText,
          active
            ? { color: activeColor, fontWeight: '600' }
            : { color: isDarkMode ? '#aaaaaa' : '#666666' },
          { textAlign: isRTL ? 'right' : 'left' }
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    paddingHorizontal: screenWidth < 380 ? 12 : 16,
    paddingVertical: screenWidth < 380 ? 6 : 8,
    borderRadius: screenWidth < 380 ? 16 : 20,
    borderWidth: 1,
    minWidth: screenWidth < 380 ? 50 : 60,
  },
  activeButton: {
    borderWidth: 1,
  },
  buttonText: {
    fontSize: screenWidth < 380 ? 11 : 13,
    fontWeight: '500',
    lineHeight: screenWidth < 380 ? 14 : 16,
  },
});
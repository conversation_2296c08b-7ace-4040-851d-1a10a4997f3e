import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Image } from 'expo-image';
import {
  Truck,
  Package,
  Fuel,
  Settings,
  MapPin,
  Calendar,
  DollarSign,
  Hash,
  Zap,
  Sprout,
  Droplets,
  Shield,
  Wrench,
  Heart,
  Wheat
} from 'lucide-react-native';
import { useTheme } from '@/context/theme-context';
import { useLanguage } from '@/context/language-context';
import { useFarm } from '@/context/farm-context';
import { useLookupStore } from '@/services/lookup_service';
import { BrowseResult } from '@/services/browse-service';

const { width: screenWidth } = Dimensions.get('window');

export interface BrowseItemsMessageProps {
  browseResult: BrowseResult;
  onItemSelect: (item: any, itemType: 'machinery' | 'inventory') => void;
  onRequestAll?: () => void;
}

export const BrowseItemsMessage: React.FC<BrowseItemsMessageProps> = ({
  browseResult,
  onItemSelect,
  onRequestAll,
}) => {
  const { colors } = useTheme();
  const { isRTL, t } = useLanguage();
  const { farms } = useFarm();
  const { getLookupsByCategory } = useLookupStore();

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'available':
      case 'working':
      case 'good':
        return '#4CAF50';
      case 'in_use':
      case 'low':
        return '#FF9800';
      case 'maintenance':
      case 'out_of_stock':
        return '#F44336';
      default:
        return colors.textSecondary;
    }
  };

  const isLowStock = (item: any) => {
    if (item.quantity === undefined || item.minQuantity === undefined) return false;
    return item.quantity <= item.minQuantity;
  };

  const getStockStatus = (item: any) => {
    if (item.quantity === 0) return { status: 'Out of Stock', color: '#F44336' };
    if (isLowStock(item)) return { status: 'Low Stock', color: '#FF9800' };
    return { status: 'In Stock', color: '#4CAF50' };
  };

  const getFarmName = (farmId: string) => {
    if (!farmId) return t('common.notSet');
    const farm = farms.find(f => f.id === farmId);
    return farm ? farm.name : t('common.unknownFarm');
  };

  const getMachineryTypeTitle = (typeId: string) => {
    const machineryTypes = getLookupsByCategory('machineryType');
    const typeItem = machineryTypes.find(item => item.id === typeId);
    if (typeItem) {
      const title = typeItem.title.toLowerCase();
      return t(`machinery.${title}`) || t(`machinery.types.${title}`) || typeItem.title;
    }
    return typeId.charAt(0).toUpperCase() + typeId.slice(1);
  };

  const getInventoryCategoryTitle = (category: string) => {
    // Try multiple translation paths for inventory categories
    return t(`inventory.categories.${category}`) ||
           t(`inventory.${category}`) ||
           t(`common.${category}`) ||
           category.charAt(0).toUpperCase() + category.slice(1);
  };

  const getCategoryIcon = (category: string, type?: string) => {
    if (browseResult.category === 'machinery') {
      switch (type?.toLowerCase()) {
        case 'tractor':
        case 'harvester':
        case 'planter':
          return <Truck size={20} color={colors.primary} />;
        case 'car':
        case 'jeep':
          return <Zap size={20} color={colors.primary} />;
        default:
          return <Settings size={20} color={colors.primary} />;
      }
    } else {
      // Inventory category icons
      switch (type?.toLowerCase()) {
        case 'seeds':
          return <Sprout size={20} color={colors.primary} />;
        case 'fertilizers':
          return <Wheat size={20} color={colors.primary} />;
        case 'pesticides':
          return <Shield size={20} color={colors.primary} />;
        case 'tools':
        case 'equipment':
          return <Wrench size={20} color={colors.primary} />;
        case 'feed':
          return <Package size={20} color={colors.primary} />;
        case 'medication':
        case 'vaccination':
          return <Heart size={20} color={colors.primary} />;
        case 'fuel':
          return <Fuel size={20} color={colors.primary} />;
        default:
          return <Package size={20} color={colors.primary} />;
      }
    }
  };

  const renderMachineryItem = (item: any, index: number) => (
    <TouchableOpacity
      key={item.id || index}
      style={[styles.itemCard, { borderColor: colors.border, backgroundColor: colors.background }]}
      onPress={() => onItemSelect(item, 'machinery')}
      activeOpacity={0.7}
    >
      <View style={[styles.cardContent, isRTL && styles.rtlCardContent]}>
        {/* Image */}
        <View style={styles.imageContainer}>
          {item.imageUrl || item.images?.[0] ? (
            <Image
              source={{ uri: item.imageUrl || item.images[0] }}
              style={styles.itemImage}
              contentFit="cover"
            />
          ) : (
            <View style={[styles.placeholderImage, { backgroundColor: colors.surface }]}>
              {getCategoryIcon(browseResult.category, item.type)}
            </View>
          )}
        </View>

        {/* Info */}
        <View style={styles.infoContainer}>
          <View style={[styles.headerRow, isRTL && styles.rtlHeaderRow]}>
            <Text style={[styles.itemName, { color: colors.text }]} numberOfLines={1}>
              {item.name}
            </Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) + '20' }]}>
              <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
                {(() => {
                  switch (item.status) {
                    case 'maintenance':
                      return t('machinery.maintenanceStatus') || t('machinery.maintenance') || t('common.maintenance');
                    case 'working':
                      return t('machinery.working') || t('common.working');
                    case 'malfunction':
                      return t('machinery.malfunction') || t('common.malfunction');
                    case 'in_use':
                    case 'in_use_other_farm':
                      return t('machinery.inUse') || t('common.inUse');
                    default:
                      return t(`common.${item.status}`) || t(`machinery.${item.status}`) || item.status;
                  }
                })()}
              </Text>
            </View>
          </View>

          <Text style={[styles.itemType, { color: colors.textSecondary }]}>
            {getMachineryTypeTitle(item.type)}
            {item.model && ` • ${item.model}`}
            {item.year && ` • ${item.year}`}
          </Text>

          {/* Additional Info */}
          <View style={[styles.additionalInfo, isRTL && styles.rtlAdditionalInfo]}>
            {item.fuelLevel !== undefined && (
              <View style={[styles.infoItem, isRTL && styles.rtlInfoItem]}>
                <Fuel size={12} color={colors.textSecondary} />
                <Text style={[styles.infoText, { color: colors.textSecondary }]}>
                  {item.fuelLevel}%
                </Text>
              </View>
            )}

            {(item.location || item.farmId) && (
              <View style={[styles.infoItem, isRTL && styles.rtlInfoItem]}>
                <MapPin size={12} color={colors.textSecondary} />
                <Text style={[styles.infoText, { color: colors.textSecondary }]} numberOfLines={1}>
                  {item.location || item.currentLocation || getFarmName(item.farmId)}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Request Button */}
        <TouchableOpacity
          style={[styles.requestButton, { backgroundColor: colors.primary }]}
          onPress={() => onItemSelect(item, browseResult.category)}
        >
          <Text style={styles.requestButtonText}>
            {t('common.request')}
          </Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderInventoryItem = (item: any, index: number) => (
    <TouchableOpacity
      key={item.id || index}
      style={[styles.itemCard, { borderColor: colors.border, backgroundColor: colors.background }]}
      onPress={() => onItemSelect(item, 'inventory')}
      activeOpacity={0.7}
    >
      <View style={[styles.cardContent, isRTL && styles.rtlCardContent]}>
        {/* Image */}
        <View style={styles.imageContainer}>
          {item.imageUrl || item.images?.[0] ? (
            <Image
              source={{ uri: item.imageUrl || item.images[0] }}
              style={styles.itemImage}
              contentFit="cover"
            />
          ) : (
            <View style={[styles.placeholderImage, { backgroundColor: colors.surface }]}>
              {getCategoryIcon('inventory', item.category)}
            </View>
          )}
        </View>

        {/* Info */}
        <View style={styles.infoContainer}>
          <View style={[styles.headerRow, isRTL && styles.rtlHeaderRow]}>
            <Text style={[styles.itemName, { color: colors.text }]} numberOfLines={1}>
              {item.name}
            </Text>
            <View style={[styles.statusBadge, { backgroundColor: getStockStatus(item).color + '20' }]}>
              <Text style={[styles.statusText, { color: getStockStatus(item).color }]}>
                {getStockStatus(item).status}
              </Text>
            </View>
          </View>

          <Text style={[styles.itemType, { color: colors.textSecondary }]}>
            {getInventoryCategoryTitle(item.category)}
            {item.supplier && ` • ${item.supplier}`}
          </Text>

          {/* Additional Info */}
          <View style={[styles.additionalInfo, isRTL && styles.rtlAdditionalInfo]}>
            {item.quantity !== undefined && (
              <View style={[styles.infoItem, isRTL && styles.rtlInfoItem]}>
                <Hash size={12} color={colors.textSecondary} />
                <Text style={[styles.infoText, { color: colors.textSecondary }]}>
                  {item.quantity} {item.unit || 'units'}
                </Text>
              </View>
            )}

            {item.price && (
              <View style={[styles.infoItem, isRTL && styles.rtlInfoItem]}>
                <DollarSign size={12} color={colors.textSecondary} />
                <Text style={[styles.infoText, { color: colors.textSecondary }]}>
                  Rs {item.price}/{item.unit || 'unit'}
                </Text>
              </View>
            )}

            {(item.location || item.farmId) && (
              <View style={[styles.infoItem, isRTL && styles.rtlInfoItem]}>
                <MapPin size={12} color={colors.textSecondary} />
                <Text style={[styles.infoText, { color: colors.textSecondary }]}>
                  {item.location || getFarmName(item.farmId)}
                </Text>
              </View>
            )}

            {item.expiryDate && (
              <View style={[styles.infoItem, isRTL && styles.rtlInfoItem]}>
                <Calendar size={12} color={colors.textSecondary} />
                <Text style={[styles.infoText, { color: colors.textSecondary }]}>
                  Exp: {new Date(item.expiryDate).toLocaleDateString()}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Request Button */}
        <TouchableOpacity
          style={[styles.requestButton, { backgroundColor: colors.primary }]}
          onPress={() => onItemSelect(item, 'inventory')}
        >
          <Text style={styles.requestButtonText}>
            {t('common.request')}
          </Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, isRTL && styles.rtlContainer]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <View style={[styles.headerContent, isRTL && styles.rtlHeaderContent]}>
          {getCategoryIcon(browseResult.category)}
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {browseResult.title}
          </Text>
          <Text style={[styles.countBadge, { color: colors.primary }]}>
            {browseResult.count}
          </Text>
        </View>
      </View>

      {/* Items List */}
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        nestedScrollEnabled={true}
      >
        {browseResult.items.map((item, index) => 
          browseResult.category === 'machinery' 
            ? renderMachineryItem(item, index)
            : renderInventoryItem(item, index)
        )}
      </ScrollView>

      {/* Request All Button (if applicable) */}
      {onRequestAll && browseResult.count > 1 && (
        <View style={styles.actionContainer}>
          <TouchableOpacity
            style={styles.requestAllButton}
            onPress={onRequestAll}
          >
            <LinearGradient
              colors={[colors.primary, colors.primaryDark]}
              style={styles.requestAllButtonGradient}
            >
              <Text style={styles.requestAllButtonText}>
                {t('chat.requestAll')} ({browseResult.count})
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: screenWidth * 0.95,
    backgroundColor: '#fff',
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    marginVertical: 8,
    alignSelf: 'center',
  },
  rtlContainer: {
    alignSelf: 'flex-end',
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  rtlHeaderContent: {
    flexDirection: 'row-reverse',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  countBadge: {
    fontSize: 14,
    fontWeight: 'bold',
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  scrollView: {
    maxHeight: 400,
    padding: 12,
  },
  itemCard: {
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: 12,
    backgroundColor: '#fff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  cardContent: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'flex-start',
    minHeight: 80,
  },
  rtlCardContent: {
    flexDirection: 'row-reverse',
  },
  imageContainer: {
    marginRight: 16,
    marginTop: 4,
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  placeholderImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoContainer: {
    flex: 1,
    paddingRight: 8,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 6,
    flexWrap: 'wrap',
  },
  rtlHeaderRow: {
    flexDirection: 'row-reverse',
  },
  itemName: {
    fontSize: 15,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
    lineHeight: 20,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
    marginLeft: 4,
    minWidth: 60,
    alignItems: 'center',
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    textAlign: 'center',
  },
  itemType: {
    fontSize: 13,
    marginBottom: 6,
    lineHeight: 18,
  },
  additionalInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 8,
  },
  rtlAdditionalInfo: {
    flexDirection: 'row-reverse',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 3,
    marginBottom: 2,
  },
  rtlInfoItem: {
    flexDirection: 'row-reverse',
  },
  infoText: {
    fontSize: 11,
    lineHeight: 16,
  },
  requestButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    marginLeft: 12,
    marginTop: 4,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    alignSelf: 'flex-start',
  },
  requestButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  actionContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  requestAllButton: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  requestAllButtonGradient: {
    paddingVertical: 12,
    alignItems: 'center',
  },
  requestAllButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
});

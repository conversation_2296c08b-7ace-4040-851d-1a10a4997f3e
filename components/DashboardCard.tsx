import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

const { width: screenWidth } = Dimensions.get('window');

interface DashboardCardProps {
  title: string;
  value: string;
  icon?: React.ReactNode;
  backgroundColor?: string;
  textColor?: string;
  secondaryTextColor?: string;
  borderColor?: string;
  gradientColors?: string[];
  onPress?: () => void;
  disabled?: boolean;
  shadowColor?: string;
  isDarkMode?: boolean;
}

export const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  value,
  icon,
  backgroundColor = '#ffffff',
  textColor = '#333333',
  secondaryTextColor = '#666666',
  borderColor = '#e0e0e0',
  gradientColors,
  onPress,
  disabled = false,
  shadowColor = '#000',
  isDarkMode = false,
}) => {
  // Responsive sizing based on screen width
  const isSmallScreen = screenWidth < 380;
  const isMediumScreen = screenWidth < 420;

  const responsiveStyles = {
    cardHeight: isSmallScreen ? 100 : isMediumScreen ? 110 : 120,
    titleFontSize: isSmallScreen ? 11 : isMediumScreen ? 12 : 14,
    valueFontSize: isSmallScreen ? 20 : isMediumScreen ? 24 : 28,
    padding: isSmallScreen ? 12 : isMediumScreen ? 14 : 16,
    iconSize: isSmallScreen ? 18 : isMediumScreen ? 20 : 24,
  };
  const shadowStyle = isDarkMode ? {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.,
    shadowRadius: 6,
    elevation: 0,
  } : {
    shadowColor: shadowColor,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 0,
  };

  const CardContainer = ({ children }: { children: React.ReactNode }) => {
    if (gradientColors) {
      return (
        <LinearGradient
          colors={gradientColors}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[styles.card, { borderColor, height: responsiveStyles.cardHeight, padding: responsiveStyles.padding }, shadowStyle]}
        >
          {children}
        </LinearGradient>
      );
    }

    return (
      <View style={[styles.card, { backgroundColor, borderColor, height: responsiveStyles.cardHeight, padding: responsiveStyles.padding }, shadowStyle]}>
        {children}
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={styles.cardWrapper}
      onPress={onPress}
      disabled={disabled || !onPress}
      activeOpacity={onPress && !disabled ? 0.7 : 1}
    >
      <CardContainer>
        <View style={styles.content}>
          <View style={styles.header}>
            {icon && <View style={[styles.iconContainer, { marginBottom: isSmallScreen ? 4 : 8 }]}>{icon}</View>}
            <Text
              style={[
                styles.title,
                {
                  color: secondaryTextColor || textColor,
                  fontSize: responsiveStyles.titleFontSize,
                  lineHeight: responsiveStyles.titleFontSize * 1.3
                }
              ]}
              numberOfLines={isSmallScreen ? 2 : 3}
              adjustsFontSizeToFit={isSmallScreen}
              minimumFontScale={0.8}
            >
              {title}
            </Text>
          </View>
          <Text
            style={[
              styles.value,
              {
                color: textColor,
                fontSize: responsiveStyles.valueFontSize,
                lineHeight: responsiveStyles.valueFontSize * 1.1
              }
            ]}
            numberOfLines={1}
            adjustsFontSizeToFit
            minimumFontScale={0.7}
          >
            {value}
          </Text>
        </View>
      </CardContainer>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  cardWrapper: {
    // Always show 2 cards per row for all screen sizes
    width: '48%',
    marginBottom: screenWidth < 380 ? 12 : 16,
    // Ensure minimum width is appropriate for 2 cards per row
    minWidth: (screenWidth / 2) - 24,
  },
  card: {
    borderRadius: screenWidth < 380 ? 12 : 16,
    borderWidth: 1.5,
    // height and padding are now dynamic via responsiveStyles
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
  },
  header: {
    marginBottom: screenWidth < 380 ? 4 : 8,
    flex: 1,
  },
  iconContainer: {
    // marginBottom is now dynamic
  },
  title: {
    fontWeight: '500',
    flexShrink: 1,
    // fontSize is now dynamic via responsiveStyles
  },
  value: {
    fontWeight: 'bold',
    textAlign: 'center',
    // fontSize is now dynamic via responsiveStyles
  },
});
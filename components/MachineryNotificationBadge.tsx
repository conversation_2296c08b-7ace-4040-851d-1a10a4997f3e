import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal, ScrollView, Alert } from 'react-native';
import { Bell, X, AlertTriangle, Info, AlertCircle, CheckCircle } from 'lucide-react-native';
import { useTheme } from '@/context/theme-context';
import { useFarm } from '@/context/farm-context';
import { 
  getMachineryNotifications, 
  getUnreadMachineryNotifications, 
  markMachineryNotificationAsRead,
  MachineryNotification 
} from '@/services/machinery-triggers';

export default function MachineryNotificationBadge() {
  const { colors } = useTheme();
  const { selectedFarm } = useFarm();
  const [notifications, setNotifications] = useState<MachineryNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadNotifications();
    
    // Refresh notifications every 30 seconds
    const interval = setInterval(loadNotifications, 30000);
    return () => clearInterval(interval);
  }, [selectedFarm]);

  const loadNotifications = async () => {
    if (!selectedFarm) return;
    
    try {
      const allNotifications = getMachineryNotifications(selectedFarm.id);
      const unreadNotifications = getUnreadMachineryNotifications(selectedFarm.id);
      
      setNotifications(allNotifications);
      setUnreadCount(unreadNotifications.length);
    } catch (error) {
      console.error('Error loading machinery notifications:', error);
    }
  };

  const handleNotificationPress = async (notification: MachineryNotification) => {
    if (!notification.isRead) {
      try {
        await markMachineryNotificationAsRead(notification.id);
        await loadNotifications();
      } catch (error) {
        console.error('Error marking notification as read:', error);
      }
    }
  };

  const getNotificationIcon = (type: MachineryNotification['type']) => {
    const iconProps = { size: 20 };
    
    switch (type) {
      case 'critical':
        return <AlertTriangle {...iconProps} color="#F44336" />;
      case 'warning':
        return <AlertCircle {...iconProps} color="#FF9800" />;
      case 'alert':
        return <AlertCircle {...iconProps} color="#F44336" />;
      case 'info':
      default:
        return <Info {...iconProps} color="#2196F3" />;
    }
  };

  const getNotificationColor = (type: MachineryNotification['type']) => {
    switch (type) {
      case 'critical':
        return '#F44336';
      case 'warning':
        return '#FF9800';
      case 'alert':
        return '#F44336';
      case 'info':
      default:
        return '#2196F3';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return date.toLocaleDateString();
  };

  if (!selectedFarm) return null;

  return (
    <>
      <TouchableOpacity
        style={[styles.badge, { backgroundColor: colors.surface }]}
        onPress={() => setModalVisible(true)}
      >
        <Bell size={24} color={colors.text} />
        {unreadCount > 0 && (
          <View style={[styles.unreadBadge, { backgroundColor: '#F44336' }]}>
            <Text style={styles.unreadText}>
              {unreadCount > 99 ? '99+' : unreadCount}
            </Text>
          </View>
        )}
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Machinery Notifications
            </Text>
            <TouchableOpacity
              onPress={() => setModalVisible(false)}
              style={styles.closeButton}
            >
              <X size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.notificationsList}>
            {notifications.length === 0 ? (
              <View style={styles.emptyState}>
                <CheckCircle size={48} color={colors.textSecondary} />
                <Text style={[styles.emptyText, { color: colors.text }]}>
                  No notifications
                </Text>
                <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
                  All machinery is running smoothly
                </Text>
              </View>
            ) : (
              notifications.map((notification) => (
                <TouchableOpacity
                  key={notification.id}
                  style={[
                    styles.notificationItem,
                    {
                      backgroundColor: notification.isRead ? colors.surface : colors.primary + '10',
                      borderColor: colors.border,
                      borderLeftColor: getNotificationColor(notification.type),
                    }
                  ]}
                  onPress={() => handleNotificationPress(notification)}
                >
                  <View style={styles.notificationContent}>
                    <View style={styles.notificationHeader}>
                      {getNotificationIcon(notification.type)}
                      <Text style={[
                        styles.notificationTitle,
                        { 
                          color: colors.text,
                          fontWeight: notification.isRead ? 'normal' : 'bold'
                        }
                      ]}>
                        {notification.title}
                      </Text>
                      {!notification.isRead && (
                        <View style={[styles.unreadDot, { backgroundColor: colors.primary }]} />
                      )}
                    </View>
                    <Text style={[
                      styles.notificationMessage,
                      { 
                        color: colors.textSecondary,
                        fontWeight: notification.isRead ? 'normal' : '500'
                      }
                    ]}>
                      {notification.message}
                    </Text>
                    <Text style={[styles.notificationTime, { color: colors.textSecondary }]}>
                      {formatTimeAgo(notification.createdAt)}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))
            )}
          </ScrollView>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  badge: {
    position: 'relative',
    padding: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  unreadBadge: {
    position: 'absolute',
    top: 2,
    right: 2,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  unreadText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  notificationsList: {
    flex: 1,
    padding: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  notificationItem: {
    borderRadius: 12,
    borderWidth: 1,
    borderLeftWidth: 4,
    marginBottom: 12,
    overflow: 'hidden',
  },
  notificationContent: {
    padding: 16,
  },
  notificationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  notificationTitle: {
    fontSize: 16,
    marginLeft: 8,
    flex: 1,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  notificationMessage: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  notificationTime: {
    fontSize: 12,
  },
});
{"name": "expo-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start --tunnel", "start-web": "expo start --web --tunnel", "start-web-dev": "DEBUG=expo* expo start --web --tunnel"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-picker/picker": "2.11.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/material-top-tabs": "^7.2.13", "@react-navigation/native": "^7.1.6", "date-fns": "^4.1.0", "expo": "53.0.19", "expo-av": "15.1.7", "expo-blur": "~14.1.5", "expo-camera": "16.1.10", "expo-constants": "17.1.7", "expo-file-system": "18.1.11", "expo-font": "13.3.2", "expo-haptics": "~14.1.4", "expo-image": "2.3.2", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "7.1.7", "expo-localization": "16.1.6", "expo-location": "18.1.6", "expo-router": "5.1.3", "expo-sharing": "~13.1.5", "expo-speech": "~13.1.7", "expo-splash-screen": "0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "5.0.10", "expo-updates": "0.28.17", "expo-web-browser": "~14.2.0", "firebase": "^11.7.3", "i18n-js": "^4.5.1", "lucide-react-native": "^0.475.0", "nativewind": "^4.1.23", "openai": "^5.10.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-pager-view": "6.7.1", "react-native-popup-menu": "^0.17.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-tab-view": "^4.1.0", "react-native-web": "^0.20.0", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.0", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true, "packageManager": "yarn@4.9.2"}
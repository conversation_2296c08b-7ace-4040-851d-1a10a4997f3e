import { collection, query, where, getDocs, doc, getDoc, addDoc, updateDoc, deleteDoc } from "firebase/firestore";
import { getFirestore } from "firebase/firestore";
import AsyncStorage from "@react-native-async-storage/async-storage";

const db = getFirestore();

// Define types
export interface Category {
  id: string;
  name: string;
  description?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface Unit {
  id: string;
  name: string;
  abbreviation: string;
  createdAt?: string;
  updatedAt?: string;
}

// Mock data for offline development
const mockCategories: Category[] = [
  {
    id: "1",
    name: "Seeds",
    description: "All types of seeds for planting",
  },
  {
    id: "2",
    name: "Fertilizers",
    description: "Organic and chemical fertilizers",
  },
  {
    id: "3",
    name: "Tools",
    description: "Farm tools and equipment",
  },
  {
    id: "4",
    name: "Pesticides",
    description: "Pest control products",
  },
  {
    id: "5",
    name: "Feed",
    description: "Animal feed and nutrition",
  },
  {
    id: "6",
    name: "Medication",
    description: "Medical supplies and medicines",
  },
  {
    id: "7",
    name: "Vaccination",
    description: "Vaccines and vaccination supplies",
  },
  {
    id: "8",
    name: "Fuel",
    description: "Fuel and energy supplies",
  },
];

const mockUnits: Unit[] = [
  {
    id: "1",
    name: "Kilogram",
    abbreviation: "kg",
  },
  {
    id: "2",
    name: "Gram",
    abbreviation: "g",
  },
  {
    id: "3",
    name: "Liter",
    abbreviation: "L",
  },
  {
    id: "4",
    name: "Milliliter",
    abbreviation: "mL",
  },
  {
    id: "5",
    name: "Units",
    abbreviation: "units",
  },
  {
    id: "6",
    name: "Bags",
    abbreviation: "bags",
  },
  {
    id: "7",
    name: "Boxes",
    abbreviation: "boxes",
  },
  {
    id: "8",
    name: "Bottles",
    abbreviation: "bottles",
  },
  {
    id: "9",
    name: "Packs",
    abbreviation: "packs",
  },
  {
    id: "10",
    name: "Pieces",
    abbreviation: "pieces",
  },
];

// Get all categories
export const getCategories = async (): Promise<Category[]> => {
  try {
    // Try to get from cache first
    const cachedCategories = await AsyncStorage.getItem("categories");
    if (cachedCategories) {
      return JSON.parse(cachedCategories);
    }

    // For now, return mock data
    // Cache the data
    await AsyncStorage.setItem("categories", JSON.stringify(mockCategories));
    
    return mockCategories;
  } catch (error) {
    console.error("Error getting categories:", error);
    return mockCategories;
  }
};

// Get all units
export const getUnits = async (): Promise<Unit[]> => {
  try {
    // Try to get from cache first
    const cachedUnits = await AsyncStorage.getItem("units");
    if (cachedUnits) {
      return JSON.parse(cachedUnits);
    }

    // For now, return mock data
    // Cache the data
    await AsyncStorage.setItem("units", JSON.stringify(mockUnits));
    
    return mockUnits;
  } catch (error) {
    console.error("Error getting units:", error);
    return mockUnits;
  }
};

// Create category
export const createCategory = async (categoryData: Partial<Category>): Promise<Category> => {
  try {
    const newCategory = {
      ...categoryData,
      createdAt: new Date().toISOString(),
    };

    // Add to Firestore
    // const docRef = await addDoc(collection(db, "categories"), newCategory);
    // newCategory.id = docRef.id;

    // For now, just add to mock data
    const id = (mockCategories.length + 1).toString();
    const createdCategory: Category = {
      id,
      name: newCategory.name || "",
      description: newCategory.description,
      createdAt: newCategory.createdAt,
    };
    
    mockCategories.push(createdCategory);

    // Update cache
    await AsyncStorage.setItem("categories", JSON.stringify(mockCategories));

    return createdCategory;
  } catch (error) {
    console.error("Error creating category:", error);
    throw error;
  }
};

// Update category
export const updateCategory = async (id: string, categoryData: Partial<Category>): Promise<Category> => {
  try {
    const updatedCategory = {
      ...categoryData,
      updatedAt: new Date().toISOString(),
    };

    // Update in Firestore
    // await updateDoc(doc(db, "categories", id), updatedCategory);

    // For now, just update mock data
    const index = mockCategories.findIndex((category) => category.id === id);
    if (index !== -1) {
      mockCategories[index] = { ...mockCategories[index], ...updatedCategory };
    }

    // Update cache
    await AsyncStorage.setItem("categories", JSON.stringify(mockCategories));

    return mockCategories[index] as Category;
  } catch (error) {
    console.error("Error updating category:", error);
    throw error;
  }
};

// Delete category
export const deleteCategory = async (id: string): Promise<boolean> => {
  try {
    // Delete from Firestore
    // await deleteDoc(doc(db, "categories", id));

    // For now, just remove from mock data
    const index = mockCategories.findIndex((category) => category.id === id);
    if (index !== -1) {
      mockCategories.splice(index, 1);
    }

    // Update cache
    await AsyncStorage.setItem("categories", JSON.stringify(mockCategories));

    return true;
  } catch (error) {
    console.error("Error deleting category:", error);
    throw error;
  }
};

// Create unit
export const createUnit = async (unitData: Partial<Unit>): Promise<Unit> => {
  try {
    const newUnit = {
      ...unitData,
      createdAt: new Date().toISOString(),
    };

    // Add to Firestore
    // const docRef = await addDoc(collection(db, "units"), newUnit);
    // newUnit.id = docRef.id;

    // For now, just add to mock data
    const id = (mockUnits.length + 1).toString();
    const createdUnit: Unit = {
      id,
      name: newUnit.name || "",
      abbreviation: newUnit.abbreviation || "",
      createdAt: newUnit.createdAt,
    };
    
    mockUnits.push(createdUnit);

    // Update cache
    await AsyncStorage.setItem("units", JSON.stringify(mockUnits));

    return createdUnit;
  } catch (error) {
    console.error("Error creating unit:", error);
    throw error;
  }
};

// Update unit
export const updateUnit = async (id: string, unitData: Partial<Unit>): Promise<Unit> => {
  try {
    const updatedUnit = {
      ...unitData,
      updatedAt: new Date().toISOString(),
    };

    // Update in Firestore
    // await updateDoc(doc(db, "units", id), updatedUnit);

    // For now, just update mock data
    const index = mockUnits.findIndex((unit) => unit.id === id);
    if (index !== -1) {
      mockUnits[index] = { ...mockUnits[index], ...updatedUnit };
    }

    // Update cache
    await AsyncStorage.setItem("units", JSON.stringify(mockUnits));

    return mockUnits[index] as Unit;
  } catch (error) {
    console.error("Error updating unit:", error);
    throw error;
  }
};

// Delete unit
export const deleteUnit = async (id: string): Promise<boolean> => {
  try {
    // Delete from Firestore
    // await deleteDoc(doc(db, "units", id));

    // For now, just remove from mock data
    const index = mockUnits.findIndex((unit) => unit.id === id);
    if (index !== -1) {
      mockUnits.splice(index, 1);
    }

    // Update cache
    await AsyncStorage.setItem("units", JSON.stringify(mockUnits));

    return true;
  } catch (error) {
    console.error("Error deleting unit:", error);
    throw error;
  }
};
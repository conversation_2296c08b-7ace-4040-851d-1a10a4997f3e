import { collection, query, where, getDocs, doc, getDoc, addDoc, updateDoc, deleteDoc, setDoc, arrayUnion, arrayRemove, serverTimestamp } from "firebase/firestore";
import { getFirestore } from "firebase/firestore";
import { getAuth, createUserWithEmailAndPassword, updateProfile } from "firebase/auth";
import AsyncStorage from "@react-native-async-storage/async-storage";

const db = getFirestore();
const auth = getAuth();

// Define User type
export interface User {
  uid: string;
  email: string;
  name: string;
  displayName?: string;
  role: UserRole;
  photoURL?: string;
  phone?: string;
  phoneNumber?: string;
  bio?: string;
  assignedFarmIds?: string[];
  farmIds?: string[]; // Alias for assignedFarmIds for backward compatibility
  farmId?: string; // Single farm ID for backward compatibility
  createdAt?: string | any; // Can be string or Firestore timestamp
  updatedAt?: string | any;
  lastLogin?: string | any;
  emailVerified?: boolean;
  isFirstLogin?: boolean;
  language?: string;
  preferredLanguage?: string;
  id?: string; // Alias for uid
  // New fields
  gender?: string;
  dateOfBirth?: string;
  cnic?: string;
  address?: string;
}

export type UserRole = "owner" | "admin" | "caretaker";

// Optimized user data for caching (includes all important fields for UI display)
interface CachedUser {
  uid: string;
  email: string;
  name: string;
  displayName?: string;
  role: UserRole;
  assignedFarmIds?: string[];
  farmId?: string;
  isFirstLogin?: boolean;
  photoURL?: string;
  phone?: string;
  phoneNumber?: string;
  bio?: string;
  createdAt?: string | any;
  updatedAt?: string | any;
  lastLogin?: string | any;
  emailVerified?: boolean;
  language?: string;
  preferredLanguage?: string;
  gender?: string;
  dateOfBirth?: string;
  cnic?: string;
  address?: string;
}

// Mock data for offline development
const mockUsers: User[] = [
  {
    uid: "1",
    email: "<EMAIL>",
    name: "John Smith",
    displayName: "John Smith",
    role: "owner",
    photoURL: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=200&auto=format&fit=crop",
    phone: "+1234567890",
    phoneNumber: "+1234567890",
    bio: "Farm owner and manager",
    assignedFarmIds: ["1", "2", "3", "4"],
    farmIds: ["1", "2", "3", "4"],
    farmId: "1",
    language: "en",
    preferredLanguage: "en",
    isFirstLogin: false,
    gender: "male",
    dateOfBirth: "15/05/1980",
    cnic: "12345-1234567-1",
    address: "123 Farm Road, Agriculture City",
  },
  {
    uid: "2",
    email: "<EMAIL>",
    name: "Maria Garcia",
    displayName: "Maria Garcia",
    role: "admin",
    photoURL: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=200&auto=format&fit=crop",
    phone: "+1987654321",
    phoneNumber: "+1987654321",
    bio: "Farm administrator",
    assignedFarmIds: ["1", "2"],
    farmIds: ["1", "2"],
    farmId: "1",
    language: "en",
    preferredLanguage: "en",
    isFirstLogin: false,
    gender: "female",
    dateOfBirth: "22/08/1985",
    cnic: "54321-7654321-2",
    address: "456 Admin Street, Management District",
  },
  {
    uid: "3",
    email: "<EMAIL>",
    name: "Raj Patel",
    displayName: "Raj Patel",
    role: "caretaker",
    photoURL: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=200&auto=format&fit=crop",
    phone: "+1122334455",
    phoneNumber: "+1122334455",
    assignedFarmIds: ["1"],
    farmIds: ["1"],
    farmId: "1",
    language: "en",
    preferredLanguage: "en",
    isFirstLogin: false,
    gender: "male",
    dateOfBirth: "10/12/1990",
    cnic: "11111-1111111-1",
    address: "789 Caretaker Lane, Farm Village",
  },
  {
    uid: "4",
    email: "<EMAIL>",
    name: "Sarah Johnson",
    displayName: "Sarah Johnson",
    role: "caretaker",
    photoURL: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=200&auto=format&fit=crop",
    phone: "+1555666777",
    phoneNumber: "+1555666777",
    assignedFarmIds: ["2"],
    farmIds: ["2"],
    farmId: "2",
    language: "en",
    preferredLanguage: "en",
    isFirstLogin: false,
    gender: "female",
    dateOfBirth: "05/03/1992",
    cnic: "22222-2222222-2",
    address: "321 Worker Street, Agricultural Town",
  },
  {
    uid: "5",
    email: "<EMAIL>",
    name: "Miguel Rodriguez",
    displayName: "Miguel Rodriguez",
    role: "caretaker",
    photoURL: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=200&auto=format&fit=crop",
    phone: "+1888999000",
    phoneNumber: "+1888999000",
    assignedFarmIds: ["3"],
    farmIds: ["3"],
    farmId: "3",
    language: "en",
    preferredLanguage: "en",
    isFirstLogin: false,
    gender: "male",
    dateOfBirth: "18/07/1988",
    cnic: "33333-3333333-3",
    address: "654 Farmer Avenue, Rural District",
  },
  {
    uid: "4hlAAFKZb0OG1AEF1TWIvBHoaOV2",
    email: "<EMAIL>",
    name: "Test User",
    displayName: "Test User",
    role: "admin",
    photoURL: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=200&auto=format&fit=crop",
    phone: "+1234567890",
    phoneNumber: "+1234567890",
    bio: "Test user for development",
    assignedFarmIds: ["1"],
    farmIds: ["1"],
    farmId: "1",
    language: "en",
    preferredLanguage: "en",
    isFirstLogin: false,
    gender: "male",
    dateOfBirth: "01/01/1990",
    cnic: "12345-1234567-1",
    address: "Test Address, Test City",
  },
];

// Cache management constants
const CACHE_KEY = "users_cache";
const CACHE_EXPIRY_KEY = "users_cache_expiry";
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const MAX_CACHE_SIZE = 20; // Reduced from 50 to 20

// Helper function to check storage quota
const checkStorageQuota = async (): Promise<boolean> => {
  try {
    // Try to store a small test item
    const testKey = "storage_test";
    const testData = "test";
    await AsyncStorage.setItem(testKey, testData);
    await AsyncStorage.removeItem(testKey);
    return true;
  } catch (error) {
    console.warn("Storage quota check failed:", error);
    return false;
  }
};

// Helper function to optimize user data for caching
const optimizeUserForCache = (user: User): CachedUser => {
  return {
    uid: user.uid,
    email: user.email,
    name: user.name || user.displayName || "",
    displayName: user.displayName || user.name,
    role: user.role,
    assignedFarmIds: user.assignedFarmIds || user.farmIds || (user.farmId ? [user.farmId] : []),
    farmId: user.farmId || (user.assignedFarmIds && user.assignedFarmIds[0]) || (user.farmIds && user.farmIds[0]),
    isFirstLogin: user.isFirstLogin || false,
    photoURL: user.photoURL,
    phone: user.phone,
    phoneNumber: user.phoneNumber,
    bio: user.bio,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
    lastLogin: user.lastLogin,
    emailVerified: user.emailVerified,
    language: user.language,
    preferredLanguage: user.preferredLanguage,
    gender: user.gender,
    dateOfBirth: user.dateOfBirth,
    cnic: user.cnic,
    address: user.address,
  };
};

// Helper function to restore full user data from cached data
const restoreUserFromCache = (cachedUser: CachedUser): User => {
  return {
    uid: cachedUser.uid,
    id: cachedUser.uid,
    email: cachedUser.email,
    name: cachedUser.name,
    displayName: cachedUser.displayName,
    role: cachedUser.role,
    assignedFarmIds: cachedUser.assignedFarmIds,
    farmIds: cachedUser.assignedFarmIds, // For backward compatibility
    farmId: cachedUser.farmId,
    isFirstLogin: cachedUser.isFirstLogin || false,
    photoURL: cachedUser.photoURL,
    phone: cachedUser.phone,
    phoneNumber: cachedUser.phoneNumber,
    bio: cachedUser.bio,
    createdAt: cachedUser.createdAt,
    updatedAt: cachedUser.updatedAt,
    lastLogin: cachedUser.lastLogin,
    emailVerified: cachedUser.emailVerified,
    language: cachedUser.language,
    preferredLanguage: cachedUser.preferredLanguage,
    gender: cachedUser.gender,
    dateOfBirth: cachedUser.dateOfBirth,
    cnic: cachedUser.cnic,
    address: cachedUser.address,
  };
};

// Safe cache operations with quota handling
const safeCacheSet = async (key: string, data: any): Promise<boolean> => {
  try {
    // Check if storage is available
    const hasQuota = await checkStorageQuota();
    if (!hasQuota) {
      console.warn("Storage quota exceeded, skipping cache");
      return false;
    }

    const jsonData = JSON.stringify(data);
    
    // Check data size (rough estimate) - reduced limit
    const dataSize = new Blob([jsonData]).size;
    if (dataSize > 512 * 1024) { // 512KB limit (reduced from 1MB)
      console.warn("Data too large for cache, skipping");
      return false;
    }

    await AsyncStorage.setItem(key, jsonData);
    return true;
  } catch (error) {
    console.warn("Failed to cache data:", error);
    // Clear some cache if quota exceeded
    if (error.name === 'QuotaExceededError') {
      await clearUsersCache();
    }
    return false;
  }
};

const safeCacheGet = async (key: string): Promise<any | null> => {
  try {
    const data = await AsyncStorage.getItem(key);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.warn("Failed to read cache:", error);
    return null;
  }
};

// Clear cache helper function
const clearUsersCache = async () => {
  try {
    await AsyncStorage.multiRemove([CACHE_KEY, CACHE_EXPIRY_KEY]);
    console.log("Users cache cleared");
  } catch (error) {
    console.error("Error clearing users cache:", error);
  }
};

// Check if cache is valid
const isCacheValid = async (): Promise<boolean> => {
  try {
    const expiryTime = await AsyncStorage.getItem(CACHE_EXPIRY_KEY);
    if (!expiryTime) return false;
    
    const now = Date.now();
    return now < parseInt(expiryTime);
  } catch (error) {
    return false;
  }
};

// Get users from cache
const getUsersFromCache = async (farmId?: string): Promise<User[] | null> => {
  try {
    const isValid = await isCacheValid();
    if (!isValid) return null;

    const cachedData = await safeCacheGet(CACHE_KEY);
    if (!cachedData) return null;

    const cachedUsers: CachedUser[] = cachedData;
    let users = cachedUsers.map(restoreUserFromCache);

    // Filter by farm if specified
    if (farmId) {
      users = users.filter(user => 
        (user.assignedFarmIds && user.assignedFarmIds.includes(farmId)) ||
        (user.farmIds && user.farmIds.includes(farmId)) ||
        (user.farmId === farmId)
      );
    }

    return users;
  } catch (error) {
    console.warn("Error reading users from cache:", error);
    return null;
  }
};

// Cache users with optimization
const cacheUsers = async (users: User[]): Promise<void> => {
  try {
    // Limit cache size
    const usersToCache = users.slice(0, MAX_CACHE_SIZE);
    
    // Optimize data for caching
    const optimizedUsers = usersToCache.map(optimizeUserForCache);
    
    // Set cache with expiry
    const success = await safeCacheSet(CACHE_KEY, optimizedUsers);
    if (success) {
      const expiryTime = Date.now() + CACHE_DURATION;
      await AsyncStorage.setItem(CACHE_EXPIRY_KEY, expiryTime.toString());
    }
  } catch (error) {
    console.warn("Error caching users:", error);
  }
};

// Force refresh users (bypass cache)
export const refreshUsers = async (farmId?: string): Promise<User[]> => {
  try {
    console.log("Force refreshing users, clearing cache...");
    // Clear cache first
    await clearUsersCache();

    // Fetch fresh data from Firebase
    let users: User[] = [];

    if (farmId) {
      const usersRef = collection(db, "users");
      const usersQuery = query(usersRef, where("assignedFarmIds", "array-contains", farmId));
      const usersSnapshot = await getDocs(usersQuery);
      users = usersSnapshot.docs.map(doc => ({
        uid: doc.id,
        id: doc.id,
        ...doc.data()
      } as User));
    } else {
      const usersRef = collection(db, "users");
      const usersSnapshot = await getDocs(usersRef);
      users = usersSnapshot.docs.map(doc => ({
        uid: doc.id,
        id: doc.id,
        ...doc.data()
      } as User));
    }

    // Cache the fresh data
    if (users.length > 0) {
      await cacheUsers(users);
    }

    // If no users from Firebase, return mock data
    if (users.length === 0) {
      if (farmId) {
        return mockUsers.filter(user =>
          (user.assignedFarmIds && user.assignedFarmIds.includes(farmId)) ||
          (user.farmIds && user.farmIds.includes(farmId)) ||
          (user.farmId === farmId)
        );
      }
      return mockUsers;
    }

    console.log("Force refresh completed, returning fresh users:", users.length);
    return users;
  } catch (error) {
    console.error("Error force refreshing users:", error);

    // Fallback to mock data
    if (farmId) {
      return mockUsers.filter(user =>
        (user.assignedFarmIds && user.assignedFarmIds.includes(farmId)) ||
        (user.farmIds && user.farmIds.includes(farmId)) ||
        (user.farmId === farmId)
      );
    }
    return mockUsers;
  }
};

// Get all users
export const getUsers = async (farmId?: string): Promise<User[]> => {
  try {
    // Try to get from cache first
    const cachedUsers = await getUsersFromCache(farmId);
    if (cachedUsers) {
      console.log("Returning users from cache:", cachedUsers.length);
      return cachedUsers;
    }

    // Fetch fresh data from Firebase
    let users: User[] = [];

    if (farmId) {
      const usersRef = collection(db, "users");
      const usersQuery = query(usersRef, where("assignedFarmIds", "array-contains", farmId));
      const usersSnapshot = await getDocs(usersQuery);
      users = usersSnapshot.docs.map(doc => ({
        uid: doc.id,
        id: doc.id,
        ...doc.data()
      } as User));
    } else {
      const usersRef = collection(db, "users");
      const usersSnapshot = await getDocs(usersRef);
      users = usersSnapshot.docs.map(doc => ({
        uid: doc.id,
        id: doc.id,
        ...doc.data()
      } as User));
    }

    // Cache the data (only if we got data from Firebase)
    if (users.length > 0) {
      await cacheUsers(users);
    }
    
    // If no users from Firebase, return mock data
    if (users.length === 0) {
      if (farmId) {
        return mockUsers.filter(user => 
          (user.assignedFarmIds && user.assignedFarmIds.includes(farmId)) ||
          (user.farmIds && user.farmIds.includes(farmId)) ||
          (user.farmId === farmId)
        );
      }
      return mockUsers;
    }
    
    return users;
  } catch (error) {
    console.error("Error getting users:", error);
    
    // Try to get from cache as fallback
    const cachedUsers = await getUsersFromCache(farmId);
    if (cachedUsers) {
      return cachedUsers;
    }
    
    // Fallback to mock data
    if (farmId) {
      return mockUsers.filter(user => 
        (user.assignedFarmIds && user.assignedFarmIds.includes(farmId)) ||
        (user.farmIds && user.farmIds.includes(farmId)) ||
        (user.farmId === farmId)
      );
    }
    return mockUsers;
  }
};

// Get users by role
export const getUsersByRole = async (role: UserRole): Promise<User[]> => {
  try {
    // Try cache first
    const cachedUsers = await getUsersFromCache();
    if (cachedUsers) {
      return cachedUsers.filter(user => user.role === role);
    }

    // Fetch from Firebase
    const usersRef = collection(db, "users");
    const usersQuery = query(usersRef, where("role", "==", role));
    const usersSnapshot = await getDocs(usersQuery);
    const users = usersSnapshot.docs.map(doc => ({
      uid: doc.id,
      id: doc.id,
      ...doc.data()
    } as User));

    // Cache if we got data
    if (users.length > 0) {
      await cacheUsers(users);
    }

    // If no users from Firebase, return filtered mock data
    if (users.length === 0) {
      return mockUsers.filter(user => user.role === role);
    }
    
    return users;
  } catch (error) {
    console.error(`Error getting ${role} users:`, error);
    return mockUsers.filter(user => user.role === role);
  }
};

// Get user by ID
export const getUserById = async (uid: string): Promise<User | null> => {
  try {
    // Try cache first
    const cachedUsers = await getUsersFromCache();
    if (cachedUsers) {
      const cachedUser = cachedUsers.find(user => user.uid === uid || user.id === uid);
      if (cachedUser) return cachedUser;
    }

    // Fetch from Firebase
    const userRef = doc(db, "users", uid);
    const userSnapshot = await getDoc(userRef);
    
    if (userSnapshot.exists()) {
      return {
        uid: userSnapshot.id,
        id: userSnapshot.id,
        ...userSnapshot.data()
      } as User;
    }

    // If no user from Firebase, return from mock data
    return mockUsers.find(user => user.uid === uid || user.id === uid) || null;
  } catch (error) {
    console.error("Error getting user:", error);
    return mockUsers.find(user => user.uid === uid || user.id === uid) || null;
  }
};

// Get user name by ID - convenience function
export const getUserNameById = async (uid: string): Promise<string> => {
  try {
    const user = await getUserById(uid);
    return user?.displayName || user?.name || "Unknown User";
  } catch (error) {
    console.error("Error getting user name:", error);
    return "Unknown User";
  }
};

// Create user with Firebase Auth and Firestore
export const createUser = async (userData: {
  name: string;
  email: string;
  password: string;
  role: string;
  phone?: string;
  phoneNumber?: string;
  bio?: string;
  photoURL?: string;
  assignedFarmIds?: string[];
  farmIds?: string[];
  farmId?: string;
  language?: string;
  preferredLanguage?: string;
  gender?: string;
  dateOfBirth?: string;
  cnic?: string;
  address?: string;
}): Promise<User> => {
  try {
    // Clear cache before creating user to ensure fresh data
    await clearUsersCache();
    
    // Normalize farm IDs
    let farmIds: string[] = [];
    if (userData.assignedFarmIds) {
      farmIds = userData.assignedFarmIds;
    } else if (userData.farmIds) {
      farmIds = userData.farmIds;
    } else if (userData.farmId) {
      farmIds = [userData.farmId];
    }
    
    // Create user in Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, userData.email, userData.password);
    const firebaseUser = userCredential.user;
    
    // Update the user's display name
    await updateProfile(firebaseUser, {
      displayName: userData.name
    });

    // Create user document in Firestore with the exact structure requested
    const userDocData = {
      assignedFarmIds: farmIds,
      bio: userData.bio || "",
      createdAt: serverTimestamp(),
      displayName: userData.name,
      email: userData.email,
      emailVerified: firebaseUser.emailVerified,
      farmId: farmIds.length > 0 ? farmIds[0] : "", // First farm in assignedFarmIds
      id: firebaseUser.uid,
      isFirstLogin: true,
      language: userData.language || "en",
      lastLogin: serverTimestamp(),
      name: userData.name,
      phone: userData.phone || "",
      phoneNumber: userData.phone || userData.phoneNumber || "",
      photoURL: userData.photoURL || "",
      preferredLanguage: userData.preferredLanguage || userData.language || "en",
      role: userData.role as UserRole,
      // New fields
      gender: userData.gender || "",
      dateOfBirth: userData.dateOfBirth || "",
      cnic: userData.cnic || "",
      address: userData.address || "",
    };

    await setDoc(doc(db, "users", firebaseUser.uid), userDocData);

    const createdUser: User = {
      uid: firebaseUser.uid,
      id: firebaseUser.uid,
      ...userDocData,
      createdAt: new Date().toISOString(), // For local representation
      lastLogin: new Date().toISOString(),
    };
    
    // Add to mock data for local development
    mockUsers.push(createdUser);

    console.log("User created successfully:", createdUser);
    return createdUser;
  } catch (error: any) {
    console.error("Error creating user:", error);
    
    // For development, still add to mock data if Firebase fails
    if (error.code === 'auth/network-request-failed' || error.code === 'auth/timeout') {
      const uid = (mockUsers.length + 1).toString();
      
      // Normalize farm IDs
      let farmIds: string[] = [];
      if (userData.assignedFarmIds) {
        farmIds = userData.assignedFarmIds;
      } else if (userData.farmIds) {
        farmIds = userData.farmIds;
      } else if (userData.farmId) {
        farmIds = [userData.farmId];
      }
      
      const createdUser: User = {
        uid,
        id: uid,
        assignedFarmIds: farmIds,
        bio: userData.bio || "",
        createdAt: new Date().toISOString(),
        displayName: userData.name,
        email: userData.email,
        emailVerified: false,
        farmId: farmIds.length > 0 ? farmIds[0] : "",
        isFirstLogin: true,
        language: userData.language || "en",
        lastLogin: new Date().toISOString(),
        name: userData.name,
        phone: userData.phone || "",
        phoneNumber: userData.phone || userData.phoneNumber || "",
        photoURL: userData.photoURL || "",
        preferredLanguage: userData.preferredLanguage || userData.language || "en",
        role: userData.role as UserRole,
        farmIds: farmIds, // For backward compatibility
        // New fields
        gender: userData.gender || "",
        dateOfBirth: userData.dateOfBirth || "",
        cnic: userData.cnic || "",
        address: userData.address || "",
      };
      
      mockUsers.push(createdUser);
      console.log("User created in mock data:", createdUser);
      return createdUser;
    }
    
    throw new Error(error.message || "Failed to create user");
  }
};

// Update user
export const updateUser = async (uid: string, userData: Partial<User>): Promise<User> => {
  try {
    console.log("🔄 Updating user in service:", { uid, userData });

    // Clear cache before updating to ensure fresh data
    await clearUsersCache();

    const updatedUser = {
      ...userData,
      updatedAt: serverTimestamp(),
    };

    // Ensure both assignedFarmIds and farmIds are updated for compatibility
    if (userData.assignedFarmIds) {
      updatedUser.farmIds = userData.assignedFarmIds;
      updatedUser.farmId = userData.assignedFarmIds.length > 0 ? userData.assignedFarmIds[0] : "";
    }
    if (userData.farmIds) {
      updatedUser.assignedFarmIds = userData.farmIds;
      updatedUser.farmId = userData.farmIds.length > 0 ? userData.farmIds[0] : "";
    }
    if (userData.farmId) {
      updatedUser.assignedFarmIds = [userData.farmId];
      updatedUser.farmIds = [userData.farmId];
    }

    // Ensure displayName is updated when name is updated
    if (userData.name) {
      updatedUser.displayName = userData.name;
    }
    if (userData.displayName) {
      updatedUser.name = userData.displayName;
    }

    console.log("📝 Final user data to update in Firestore:", {
      name: updatedUser.name,
      displayName: updatedUser.displayName,
      phone: updatedUser.phone,
      phoneNumber: updatedUser.phoneNumber
    });

    // Update in Firebase Firestore
    const userRef = doc(db, "users", uid);
    await updateDoc(userRef, updatedUser);
    console.log("✅ User updated in Firestore successfully");

    // Also update mock data for consistency
    const index = mockUsers.findIndex(user => user.uid === uid || user.id === uid);
    if (index !== -1) {
      mockUsers[index] = { ...mockUsers[index], ...updatedUser };
      console.log("✅ Mock user data updated successfully");
    }

    // Clear cache again after updating to ensure fresh data on next fetch
    await clearUsersCache();
    console.log("🧹 User cache cleared after update");

    return mockUsers[index] as User;
  } catch (error) {
    console.error("Error updating user:", error);
    
    // Fallback to mock data update
    const index = mockUsers.findIndex(user => user.uid === uid || user.id === uid);
    if (index !== -1) {
      const updatedUser = { ...userData };
      
      // Ensure both assignedFarmIds and farmIds are updated for compatibility
      if (userData.assignedFarmIds) {
        updatedUser.farmIds = userData.assignedFarmIds;
        updatedUser.farmId = userData.assignedFarmIds.length > 0 ? userData.assignedFarmIds[0] : "";
      }
      if (userData.farmIds) {
        updatedUser.assignedFarmIds = userData.farmIds;
        updatedUser.farmId = userData.farmIds.length > 0 ? userData.farmIds[0] : "";
      }
      if (userData.farmId) {
        updatedUser.assignedFarmIds = [userData.farmId];
        updatedUser.farmIds = [userData.farmId];
      }

      // Ensure displayName is updated when name is updated
      if (userData.name) {
        updatedUser.displayName = userData.name;
      }
      if (userData.displayName) {
        updatedUser.name = userData.displayName;
      }
      
      mockUsers[index] = { ...mockUsers[index], ...updatedUser };
      return mockUsers[index];
    }
    
    throw error;
  }
};

// Update user role - convenience function that wraps updateUser
export const updateUserRole = async (uid: string, role: UserRole): Promise<User> => {
  return updateUser(uid, { role });
};

// Delete user
export const deleteUser = async (uid: string): Promise<boolean> => {
  try {
    // Clear cache before deleting to ensure fresh data
    await clearUsersCache();
    
    // In a real implementation, we would delete from Firebase
    const userRef = doc(db, "users", uid);
    await deleteDoc(userRef);

    // For now, just remove from mock data
    const index = mockUsers.findIndex(user => user.uid === uid || user.id === uid);
    if (index !== -1) {
      mockUsers.splice(index, 1);
    }

    return true;
  } catch (error) {
    console.error("Error deleting user:", error);
    
    // Fallback to mock data deletion
    const index = mockUsers.findIndex(user => user.uid === uid || user.id === uid);
    if (index !== -1) {
      mockUsers.splice(index, 1);
      return true;
    }
    
    throw error;
  }
};

// Get users assigned to a farm
export const getUsersByFarm = async (farmId: string): Promise<User[]> => {
  return getUsers(farmId);
};

// Assign user to farm
export const assignUserToFarm = async (userId: string, farmId: string): Promise<User> => {
  try {
    const user = await getUserById(userId);
    if (!user) {
      throw new Error("User not found");
    }

    // In a real implementation, we would update in Firebase
    const userRef = doc(db, "users", userId);
    await updateDoc(userRef, {
      assignedFarmIds: arrayUnion(farmId),
      farmIds: arrayUnion(farmId), // For backward compatibility
      updatedAt: serverTimestamp()
    });

    // For now, just update mock data
    const index = mockUsers.findIndex(user => user.uid === userId || user.id === userId);
    if (index !== -1) {
      const assignedFarmIds = mockUsers[index].assignedFarmIds || [];
      const farmIds = mockUsers[index].farmIds || [];
      if (!assignedFarmIds.includes(farmId)) {
        assignedFarmIds.push(farmId);
        mockUsers[index].assignedFarmIds = assignedFarmIds;
      }
      if (!farmIds.includes(farmId)) {
        farmIds.push(farmId);
        mockUsers[index].farmIds = farmIds;
      }
      // Update single farmId for backward compatibility
      if (!mockUsers[index].farmId) {
        mockUsers[index].farmId = farmId;
      }
    }

    // Clear cache to force refresh on next fetch
    await clearUsersCache();

    return mockUsers[index] as User;
  } catch (error) {
    console.error(`Error assigning user ${userId} to farm ${farmId}:`, error);
    throw error;
  }
};

// Remove user from farm
export const removeUserFromFarm = async (userId: string, farmId: string): Promise<User> => {
  try {
    const user = await getUserById(userId);
    if (!user) {
      throw new Error("User not found");
    }

    // In a real implementation, we would update in Firebase
    const userRef = doc(db, "users", userId);
    await updateDoc(userRef, {
      assignedFarmIds: arrayRemove(farmId),
      farmIds: arrayRemove(farmId), // For backward compatibility
      updatedAt: serverTimestamp()
    });

    // For now, just update mock data
    const index = mockUsers.findIndex(user => user.uid === userId || user.id === userId);
    if (index !== -1) {
      const assignedFarmIds = mockUsers[index].assignedFarmIds || [];
      const farmIds = mockUsers[index].farmIds || [];
      mockUsers[index].assignedFarmIds = assignedFarmIds.filter(id => id !== farmId);
      mockUsers[index].farmIds = farmIds.filter(id => id !== farmId);
      
      // Update single farmId for backward compatibility
      if (mockUsers[index].farmId === farmId) {
        const remainingFarms = mockUsers[index].assignedFarmIds || [];
        mockUsers[index].farmId = remainingFarms.length > 0 ? remainingFarms[0] : "";
      }
    }

    // Clear cache to force refresh on next fetch
    await clearUsersCache();

    return mockUsers[index] as User;
  } catch (error) {
    console.error(`Error removing user ${userId} from farm ${farmId}:`, error);
    throw error;
  }
};

// Update user profile - convenience function for profile updates
export const updateUserProfile = async (userId: string, profileData: {
  name?: string;
  displayName?: string;
  photoURL?: string;
  phone?: string;
  phoneNumber?: string;
  bio?: string;
  language?: string;
  preferredLanguage?: string;
  gender?: string;
  dateOfBirth?: string;
  cnic?: string;
  address?: string;
}): Promise<User> => {
  console.log("🔄 Updating user profile:", {
    userId,
    profileData: {
      name: profileData.name,
      displayName: profileData.displayName,
      phone: profileData.phone
    }
  });

  // Ensure both name and displayName are updated for consistency
  const updatedProfileData = {
    ...profileData,
    // If name is provided, also update displayName
    ...(profileData.name && { displayName: profileData.name }),
    // If displayName is provided, also update name
    ...(profileData.displayName && { name: profileData.displayName }),
    // Ensure phone consistency
    ...(profileData.phone && { phoneNumber: profileData.phone }),
    ...(profileData.phoneNumber && { phone: profileData.phoneNumber }),
  };

  console.log("📝 Final profile data to update:", updatedProfileData);

  const result = await updateUser(userId, updatedProfileData);

  console.log("✅ User profile updated successfully");
  return result;
};
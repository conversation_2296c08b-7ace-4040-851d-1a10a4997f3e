import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

export interface SystemNotification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success' | 'machinery' | 'inventory' | 'request';
  farmId?: string;
  relatedId?: string;
  isRead: boolean;
  createdAt: string;
  expiresAt?: string;
}

class NotificationService {
  private notifications: SystemNotification[] = [];
  private isInitialized = false;

  async initialize() {
    if (this.isInitialized) return;
    
    try {
      const stored = await AsyncStorage.getItem('system_notifications');
      if (stored) {
        this.notifications = JSON.parse(stored);
        // Clean up expired notifications
        await this.cleanupExpiredNotifications();
      }
      this.isInitialized = true;
    } catch (error) {
      console.error('Error initializing notification service:', error);
    }
  }

  async createNotification(notification: Omit<SystemNotification, 'id' | 'isRead' | 'createdAt'>): Promise<SystemNotification> {
    const newNotification: SystemNotification = {
      ...notification,
      id: Date.now().toString(),
      isRead: false,
      createdAt: new Date().toISOString(),
    };

    this.notifications.unshift(newNotification);
    await this.saveNotifications();

    // Show platform notification if available
    if (Platform.OS !== 'web') {
      this.showPlatformNotification(newNotification);
    }

    return newNotification;
  }

  async getNotifications(farmId?: string): Promise<SystemNotification[]> {
    await this.initialize();
    
    let notifications = this.notifications;
    if (farmId) {
      notifications = notifications.filter(n => !n.farmId || n.farmId === farmId);
    }
    
    return notifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  async getUnreadNotifications(farmId?: string): Promise<SystemNotification[]> {
    const notifications = await this.getNotifications(farmId);
    return notifications.filter(n => !n.isRead);
  }

  async markAsRead(notificationId: string): Promise<void> {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.isRead = true;
      await this.saveNotifications();
    }
  }

  async markAllAsRead(farmId?: string): Promise<void> {
    let updated = false;
    this.notifications.forEach(notification => {
      if (!notification.isRead && (!farmId || notification.farmId === farmId)) {
        notification.isRead = true;
        updated = true;
      }
    });
    
    if (updated) {
      await this.saveNotifications();
    }
  }

  async deleteNotification(notificationId: string): Promise<void> {
    this.notifications = this.notifications.filter(n => n.id !== notificationId);
    await this.saveNotifications();
  }

  async clearAll(farmId?: string): Promise<void> {
    if (farmId) {
      this.notifications = this.notifications.filter(n => n.farmId !== farmId);
    } else {
      this.notifications = [];
    }
    await this.saveNotifications();
  }

  private async cleanupExpiredNotifications(): Promise<void> {
    const now = new Date();
    const initialCount = this.notifications.length;
    
    this.notifications = this.notifications.filter(n => {
      if (!n.expiresAt) return true;
      return new Date(n.expiresAt) > now;
    });

    if (this.notifications.length !== initialCount) {
      await this.saveNotifications();
    }
  }

  private async saveNotifications(): Promise<void> {
    try {
      await AsyncStorage.setItem('system_notifications', JSON.stringify(this.notifications));
    } catch (error) {
      console.error('Error saving notifications:', error);
    }
  }

  private showPlatformNotification(notification: SystemNotification): void {
    // This would integrate with expo-notifications or similar
    // For now, just log
    console.log('Platform notification:', notification.title, notification.message);
  }

  async getNotificationStats(farmId?: string): Promise<{
    total: number;
    unread: number;
    byType: { [key: string]: number };
    recent: number;
  }> {
    const notifications = await this.getNotifications(farmId);
    const unread = notifications.filter(n => !n.isRead);
    
    const byType: { [key: string]: number } = {};
    notifications.forEach(n => {
      byType[n.type] = (byType[n.type] || 0) + 1;
    });

    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);
    const recent = notifications.filter(n => new Date(n.createdAt) > oneDayAgo).length;

    return {
      total: notifications.length,
      unread: unread.length,
      byType,
      recent,
    };
  }
}

export const notificationService = new NotificationService();

export const createNotification = (notification: Omit<SystemNotification, 'id' | 'isRead' | 'createdAt'>) => 
  notificationService.createNotification(notification);

export const getNotifications = (farmId?: string) => notificationService.getNotifications(farmId);

export const getUnreadNotifications = (farmId?: string) => notificationService.getUnreadNotifications(farmId);

export const markNotificationAsRead = (notificationId: string) => notificationService.markAsRead(notificationId);

export const getNotificationStats = (farmId?: string) => notificationService.getNotificationStats(farmId);
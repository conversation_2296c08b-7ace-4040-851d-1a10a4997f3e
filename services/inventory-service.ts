import { collection, query, where, getDocs, doc, getDoc, addDoc, updateDoc, deleteDoc, orderBy, limit as firestoreLimit, onSnapshot, Timestamp } from "firebase/firestore";
import { getFirestore } from "firebase/firestore";
import AsyncStorage from "@react-native-async-storage/async-storage";

const db = getFirestore();

// Define InventoryItem type
export interface InventoryItem {
  id: string;
  name: string;
  category: string;
  quantity: number;
  unit: string;
  minQuantity: number;
  location: string; // Farm ID
  expiryDate?: string;
  purchaseDate?: string;
  description?: string;
  imageUrl?: string;
  supplier?: string;
  price?: number;
  history?: {
    type: string;
    date: string;
    userId: string;
    details: string;
  }[];
  createdAt?: string;
  updatedAt?: string;
}

// Define Activity type
export interface Activity {
  id: string;
  type: string;
  timestamp: string | Timestamp;
  userId: string;
  userName?: string;
  userRole?: string;
  farmId: string;
  farmName?: string;
  itemId?: string;
  itemName?: string;
  details?: string;
  quantity?: number;
  unit?: string;
  action?: string;
}

// Define InventoryRequest type
export interface InventoryRequest {
  id: string;
  itemId: string;
  itemName: string;
  quantity: number;
  unit: string;
  fromLocation: string; // Farm ID
  toLocation: string; // Farm ID
  requestedBy: string; // User name
  requesterId: string; // User ID
  status: "pending" | "approved" | "rejected";
  reason?: string;
  notes?: string;
  approvedBy?: string;
  approvedById?: string;
  rejectedBy?: string;
  rejectedById?: string;
  createdAt: string;
  updatedAt?: string;
}

// Cache management constants - Reduced limits to prevent quota issues
const CACHE_DURATION = 3 * 60 * 1000; // Reduced to 3 minutes
const MAX_CACHE_SIZE_PER_FARM = 25; // Reduced from 50 to 25
const MAX_TOTAL_CACHE_SIZE = 256 * 1024; // Reduced to 256KB total limit

// Helper function to check storage quota
const checkStorageQuota = async (): Promise<boolean> => {
  try {
    // Try to store a small test item
    const testKey = "storage_test";
    const testData = "test";
    await AsyncStorage.setItem(testKey, testData);
    await AsyncStorage.removeItem(testKey);
    return true;
  } catch (error) {
    console.warn("Storage quota check failed:", error);
    return false;
  }
};

// Safe cache operations with quota handling
const safeCacheSet = async (key: string, data: any): Promise<boolean> => {
  try {
    // Check if storage is available
    const hasQuota = await checkStorageQuota();
    if (!hasQuota) {
      console.warn("Storage quota exceeded, skipping cache");
      return false;
    }

    const jsonData = JSON.stringify(data);
    
    // Check data size (rough estimate) - more aggressive size checking
    const dataSize = new Blob([jsonData]).size;
    if (dataSize > MAX_TOTAL_CACHE_SIZE) {
      console.warn("Data too large for cache, skipping");
      return false;
    }

    await AsyncStorage.setItem(key, jsonData);
    return true;
  } catch (error) {
    console.warn("Failed to cache data:", error);
    // Clear some cache if quota exceeded
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorName = error instanceof Error ? error.name : 'UnknownError';
    if (errorName === 'QuotaExceededError' || errorMessage.includes('quota')) {
      await clearInventoryCache();
    }
    return false;
  }
};

const safeCacheGet = async (key: string): Promise<any | null> => {
  try {
    const data = await AsyncStorage.getItem(key);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.warn("Failed to read cache:", error);
    return null;
  }
};

// Clear cache helper function - More aggressive cleanup
const clearInventoryCache = async () => {
  try {
    const keys = await AsyncStorage.getAllKeys();
    const inventoryKeys = keys.filter(key => 
      key.startsWith('inventory_') || 
      key.startsWith('activities_') ||
      key.startsWith('userFarms_') ||
      key === 'farms'
    );
    await AsyncStorage.multiRemove(inventoryKeys);
    console.log("Inventory cache cleared:", inventoryKeys.length, "keys");
  } catch (error) {
    console.error("Error clearing inventory cache:", error);
  }
};

// Optimize inventory item for caching (store only essential fields)
const optimizeItemForCache = (item: InventoryItem): Partial<InventoryItem> => {
  return {
    id: item.id,
    name: item.name,
    category: item.category,
    quantity: item.quantity,
    unit: item.unit,
    minQuantity: item.minQuantity,
    location: item.location,
    expiryDate: item.expiryDate,
    updatedAt: item.updatedAt
  };
};

// Get all inventory items for a specific farm
export const getInventoryItems = async (farmId?: string): Promise<InventoryItem[]> => {
  try {
    if (!farmId) {
      // If no farmId provided, return empty array
      return [];
    }
    
    // Reference to the specific farm's inventory collection
    const inventoryRef = collection(db, 'farms', farmId, 'inventory');
    
    // Get all documents in the inventory collection
    const inventorySnapshot = await getDocs(inventoryRef);
    
    // Process each inventory document
    const inventoryItems: InventoryItem[] = [];
    inventorySnapshot.forEach(doc => {
      inventoryItems.push({
        id: doc.id,
        ...doc.data(),
        location: farmId // Ensure location is set to farmId
      } as InventoryItem);
    });
    
    // Cache a limited subset of the data - more aggressive limiting
    const itemsToCache = inventoryItems.slice(0, MAX_CACHE_SIZE_PER_FARM).map(optimizeItemForCache);
    const success = await safeCacheSet(`inventory_${farmId}`, itemsToCache);
    if (!success) {
      console.warn("Failed to cache inventory items due to storage limitations");
    }
    
    return inventoryItems;
  } catch (error) {
    console.error("Error getting inventory items:", error);
    
    // Try to get from cache
    const cachedInventory = await safeCacheGet(`inventory_${farmId}`);
    if (cachedInventory) {
      return cachedInventory;
    }
    
    return [];
  }
};

// Get inventory item by ID
export const getInventoryItemById = async (id: string, farmId?: string): Promise<InventoryItem | null> => {
  try {
    if (!farmId) {
      // If no farmId provided, try to find the item in all farms
      // This is inefficient but necessary if we don't know the farm
      const allFarms = await AsyncStorage.getItem("farms");
      if (allFarms) {
        const farms = JSON.parse(allFarms);
        for (const farm of farms) {
          try {
            const itemRef = doc(db, 'farms', farm.id, 'inventory', id);
            const itemDoc = await getDoc(itemRef);
            if (itemDoc.exists()) {
              return {
                id: itemDoc.id,
                ...itemDoc.data(),
                location: farm.id
              } as InventoryItem;
            }
          } catch (e) {
            console.error(`Error checking farm ${farm.id} for item:`, e);
          }
        }
      }
      
      // If we couldn't find the item, return null
      return null;
    }
    
    // If farmId is provided, get the item directly
    const itemRef = doc(db, 'farms', farmId, 'inventory', id);
    const itemDoc = await getDoc(itemRef);
    
    if (itemDoc.exists()) {
      return {
        id: itemDoc.id,
        ...itemDoc.data(),
        location: farmId
      } as InventoryItem;
    }
    
    // Try to get from cache
    const cachedInventory = await safeCacheGet(`inventory_${farmId}`);
    if (cachedInventory) {
      const items: InventoryItem[] = cachedInventory;
      const item = items.find(item => item.id === id);
      if (item) return item;
    }
    
    return null;
  } catch (error) {
    console.error("Error getting inventory item:", error);
    
    // Try to get from cache
    if (farmId) {
      const cachedInventory = await safeCacheGet(`inventory_${farmId}`);
      if (cachedInventory) {
        const items: InventoryItem[] = cachedInventory;
        const item = items.find(item => item.id === id);
        if (item) return item;
      }
    }
    
    return null;
  }
};

// Create inventory item
export const createInventoryItem = async (itemData: Partial<InventoryItem>, userId: string): Promise<InventoryItem> => {
  try {
    if (!itemData.location) {
      throw new Error("Farm location is required");
    }
    
    const farmId = itemData.location;
    
    // Remove undefined values
    const cleanedItemData = Object.entries(itemData).reduce((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string, any>);
    
    const newItem = {
      ...cleanedItemData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      history: [
        {
          type: "created",
          date: new Date().toISOString(),
          userId: userId,
          details: "Item created"
        }
      ]
    };
    
    // Add to Firestore
    const inventoryRef = collection(db, 'farms', farmId, 'inventory');
    const docRef = await addDoc(inventoryRef, newItem);
    
    const createdItem: InventoryItem = {
      id: docRef.id,
      ...newItem as InventoryItem
    };
    
    // Create activity record
    await createActivity({
      type: "inventory_created",
      userId: userId,
      farmId: farmId,
      itemId: docRef.id,
      itemName: itemData.name,
      details: `Created ${itemData.name} with quantity ${itemData.quantity} ${itemData.unit}`,
      quantity: itemData.quantity,
      unit: itemData.unit
    });

    // Create expense record if price is provided
    if (itemData.price && itemData.price > 0) {
      try {
        const { createExpense } = await import('./expense-service');
        const { getFarmById } = await import('./farm-service');
        const { getUserById } = await import('./user-service');

        const farm = await getFarmById(farmId);
        const user = await getUserById(userId);

        if (farm && user) {
          await createExpense({
            farmId: farmId,
            farmName: farm.name,
            type: 'farm',
            category: 'inventory',
            amount: itemData.price * (itemData.quantity || 1),
            description: `Purchase of ${itemData.name} - ${itemData.quantity || 1} ${itemData.unit || 'units'}`,
            date: new Date().toISOString().split('T')[0],
            relatedItemId: createdItem.id,
            relatedItemName: itemData.name || 'Unknown Item',
            relatedItemType: 'inventory',
            notes: `Automatic expense from inventory addition`,
            createdBy: userId,
            createdByName: user.displayName || user.email || 'Unknown User',
            createdByRole: user.role || 'unknown',
          });
        }
      } catch (expenseError) {
        console.error('Error creating expense for inventory item:', expenseError);
        // Don't fail the inventory creation if expense creation fails
      }
    }

    // Clear cache to force refresh
    await safeCacheSet(`inventory_${farmId}`, null);

    return createdItem;
  } catch (error) {
    console.error("Error creating inventory item:", error);
    throw error;
  }
};

// Update inventory item
export const updateInventoryItem = async (id: string, itemData: Partial<InventoryItem>, userId: string): Promise<InventoryItem> => {
  try {
    if (!itemData.location) {
      throw new Error("Farm location is required");
    }
    
    const farmId = itemData.location;
    
    // Get the current item to compare changes
    const currentItemRef = doc(db, 'farms', farmId, 'inventory', id);
    const currentItemDoc = await getDoc(currentItemRef);
    
    if (!currentItemDoc.exists()) {
      throw new Error("Item not found");
    }
    
    const currentItem = currentItemDoc.data() as InventoryItem;
    
    // Prepare history entry
    let historyDetails = "Item updated";
    if (currentItem.quantity !== itemData.quantity) {
      historyDetails = `Quantity changed from ${currentItem.quantity} to ${itemData.quantity} ${itemData.unit}`;
    }
    
    // Get existing history or create new array
    const history = currentItem.history || [];
    
    // Create a clean update object without undefined values
    const cleanedItemData = Object.entries(itemData).reduce((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string, any>);
    
    const updatedItem = {
      ...cleanedItemData,
      updatedAt: new Date().toISOString(),
      history: [
        ...history,
        {
          type: "updated",
          date: new Date().toISOString(),
          userId: userId,
          details: historyDetails
        }
      ]
    };
    
    // Update in Firestore
    await updateDoc(currentItemRef, updatedItem);
    
    // Create activity record
    await createActivity({
      type: "inventory_updated",
      userId: userId,
      farmId: farmId,
      itemId: id,
      itemName: itemData.name || currentItem.name,
      details: historyDetails,
      quantity: itemData.quantity,
      unit: itemData.unit || currentItem.unit
    });
    
    // Clear cache to force refresh
    await safeCacheSet(`inventory_${farmId}`, null);
    
    return {
      id,
      ...currentItem,
      ...cleanedItemData,
      location: farmId,
      updatedAt: updatedItem.updatedAt,
      history: updatedItem.history
    } as InventoryItem;
  } catch (error) {
    console.error("Error updating inventory item:", error);
    throw error;
  }
};

// Delete inventory item
export const deleteInventoryItem = async (id: string, farmId?: string): Promise<boolean> => {
  try {
    if (!farmId) {
      // If no farmId provided, try to find the item in all farms
      const allFarms = await AsyncStorage.getItem("farms");
      if (allFarms) {
        const farms = JSON.parse(allFarms);
        for (const farm of farms) {
          try {
            const itemRef = doc(db, 'farms', farm.id, 'inventory', id);
            const itemDoc = await getDoc(itemRef);
            if (itemDoc.exists()) {
              await deleteDoc(itemRef);
              
              // Clear cache
              await safeCacheSet(`inventory_${farm.id}`, null);
              
              return true;
            }
          } catch (e) {
            console.error(`Error checking farm ${farm.id} for item:`, e);
          }
        }
      }
      
      return false;
    }
    
    // If farmId is provided, delete the item directly
    const itemRef = doc(db, 'farms', farmId, 'inventory', id);
    
    // Get the item details before deleting for activity record
    const itemDoc = await getDoc(itemRef);
    if (itemDoc.exists()) {
      const itemData = itemDoc.data() as InventoryItem;
      
      // Delete from Firestore
      await deleteDoc(itemRef);
      
      // Create activity record
      await createActivity({
        type: "inventory_deleted",
        userId: "system", // We don't have userId here, use system
        farmId: farmId,
        itemId: id,
        itemName: itemData.name,
        details: `Deleted ${itemData.name} with quantity ${itemData.quantity} ${itemData.unit}`,
        quantity: itemData.quantity,
        unit: itemData.unit
      });
      
      // Clear cache
      await safeCacheSet(`inventory_${farmId}`, null);
      
      return true;
    }
    
    return false;
  } catch (error) {
    console.error("Error deleting inventory item:", error);
    throw error;
  }
};

// Create activity record
export const createActivity = async (activityData: Partial<Activity>): Promise<Activity> => {
  try {
    if (!activityData.farmId) {
      throw new Error("farmId is required for activity");
    }
    
    const farmId = activityData.farmId;
    
    // Remove undefined values
    const cleanedActivityData = Object.entries(activityData).reduce((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string, any>);
    
    const newActivity = {
      ...cleanedActivityData,
      timestamp: new Date().toISOString(),
    };
    
    // Add to Firestore under farms/{farmId}/activities
    const activitiesRef = collection(db, "farms", farmId, "activities");
    const docRef = await addDoc(activitiesRef, newActivity);
    
    const createdActivity: Activity = {
      id: docRef.id,
      ...newActivity as Activity
    };
    
    return createdActivity;
  } catch (error) {
    console.error("Error creating activity:", error);
    // Don't throw error for activities, just log it
    return {
      id: "local-" + Date.now(),
      type: activityData.type || "unknown",
      timestamp: new Date().toISOString(),
      userId: activityData.userId || "system",
      farmId: activityData.farmId || "unknown",
      details: activityData.details || "Activity created locally due to error"
    };
  }
};

// Get activities for a farm
export const getActivities = async (farmId: string, count: number = 3): Promise<Activity[]> => {
  try {
    if (!farmId) {
      return [];
    }
    
    const activitiesRef = collection(db, "farms", farmId, "activities");
    const activitiesQuery = query(
      activitiesRef,
      orderBy("timestamp", "desc"),
      firestoreLimit(count)
    );
    
    const activitiesSnapshot = await getDocs(activitiesQuery);
    
    const activities: Activity[] = [];
    activitiesSnapshot.forEach(doc => {
      const data = doc.data();
      // Convert Firestore Timestamp to string if needed
      const timestamp = data.timestamp instanceof Timestamp 
        ? data.timestamp.toDate().toISOString()
        : data.timestamp;
      
      activities.push({
        id: doc.id,
        ...data,
        timestamp
      } as Activity);
    });
    
    // Cache the data with smaller size
    const limitedActivities = activities.slice(0, 3); // Only cache 3 activities
    await safeCacheSet(`activities_${farmId}`, limitedActivities);
    
    return activities;
  } catch (error) {
    console.error("Error getting activities:", error);
    
    // Check if it's an index error
    const errorMessage = error instanceof Error ? error.message : String(error);
    if (errorMessage.includes("index") || errorMessage.includes("failed-precondition")) {
      console.warn("Index error detected. Using fallback query without ordering.");
      
      try {
        // Fallback query without ordering
        const activitiesRef = collection(db, "farms", farmId, "activities");
        const fallbackQuery = query(activitiesRef);
        
        const activitiesSnapshot = await getDocs(fallbackQuery);
        
        let activities: Activity[] = [];
        activitiesSnapshot.forEach(doc => {
          const data = doc.data();
          // Convert Firestore Timestamp to string if needed
          const timestamp = data.timestamp instanceof Timestamp 
            ? data.timestamp.toDate().toISOString()
            : data.timestamp;
          
          activities.push({
            id: doc.id,
            ...data,
            timestamp
          } as Activity);
        });
        
        // Sort manually
        activities.sort((a, b) => {
          const timestampA = a.timestamp instanceof Timestamp ? a.timestamp.toDate().toISOString() : a.timestamp;
          const timestampB = b.timestamp instanceof Timestamp ? b.timestamp.toDate().toISOString() : b.timestamp;
          const dateA = new Date(timestampA).getTime();
          const dateB = new Date(timestampB).getTime();
          return dateB - dateA; // descending order
        });
        
        // Limit to requested count
        activities = activities.slice(0, count);
        
        // Cache the data
        const limitedActivities = activities.slice(0, 3);
        await safeCacheSet(`activities_${farmId}`, limitedActivities);
        
        return activities;
      } catch (fallbackError) {
        console.error("Fallback query also failed:", fallbackError);
      }
    }
    
    // Try to get from cache
    const cachedActivities = await safeCacheGet(`activities_${farmId}`);
    if (cachedActivities) {
      return cachedActivities;
    }
    
    return [];
  }
};

// Subscribe to inventory changes for a farm
export const subscribeToInventory = (farmId: string, callback: (items: InventoryItem[]) => void): (() => void) => {
  if (!farmId) {
    callback([]);
    return () => {};
  }
  
  const inventoryRef = collection(db, 'farms', farmId, 'inventory');
  
  const unsubscribe = onSnapshot(
    inventoryRef,
    (snapshot) => {
      const items: InventoryItem[] = [];
      snapshot.forEach((doc) => {
        items.push({
          id: doc.id,
          ...doc.data(),
          location: farmId
        } as InventoryItem);
      });
      
      // Clear cache instead of updating to prevent quota issues
      safeCacheSet(`inventory_${farmId}`, null)
        .catch(error => console.error("Error clearing inventory cache:", error));
      
      callback(items);
    },
    (error) => {
      console.error("Error subscribing to inventory:", error);
      
      // Try to get from cache
      safeCacheGet(`inventory_${farmId}`)
        .then(cachedInventory => {
          if (cachedInventory) {
            callback(cachedInventory);
          } else {
            callback([]);
          }
        })
        .catch(() => callback([]));
    }
  );
  
  return unsubscribe;
};

// Subscribe to activities for a farm
export const subscribeToActivities = (count: number = 3, farmId?: string, callback?: (activities: Activity[]) => void): (() => void) => {
  if (!farmId || !callback) {
    if (callback) callback([]);
    return () => {};
  }
  
  try {
    const activitiesRef = collection(db, "farms", farmId, "activities");
    const activitiesQuery = query(
      activitiesRef,
      orderBy("timestamp", "desc"),
      firestoreLimit(count)
    );
    
    const unsubscribe = onSnapshot(
      activitiesQuery,
      (snapshot) => {
        const activities: Activity[] = [];
        snapshot.forEach((doc) => {
          const data = doc.data();
          // Convert Firestore Timestamp to string if needed
          const timestamp = data.timestamp instanceof Timestamp 
            ? data.timestamp.toDate().toISOString()
            : data.timestamp;
          
          activities.push({
            id: doc.id,
            ...data,
            timestamp
          } as Activity);
        });
        
        // Clear cache instead of updating
        safeCacheSet(`activities_${farmId}`, null)
          .catch(error => console.error("Error clearing activities cache:", error));
        
        callback(activities);
      },
      (error) => {
        console.error("Error subscribing to activities:", error);
        
        // Check if it's an index error
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes("index") || errorMessage.includes("failed-precondition")) {
          console.warn("Index error detected. Using fallback query without ordering.");
          
          // Fallback to regular query without real-time updates
          getActivities(farmId, count)
            .then(activities => callback(activities))
            .catch(() => {
              // Try to get from cache as last resort
              safeCacheGet(`activities_${farmId}`)
                .then(cachedActivities => {
                  if (cachedActivities) {
                    callback(cachedActivities);
                  } else {
                    callback([]);
                  }
                })
                .catch(() => callback([]));
            });
        } else {
          // Try to get from cache
          safeCacheGet(`activities_${farmId}`)
            .then(cachedActivities => {
              if (cachedActivities) {
                callback(cachedActivities);
              } else {
                callback([]);
              }
            })
            .catch(() => callback([]));
        }
      }
    );
    
    return unsubscribe;
  } catch (error) {
    console.error("Error setting up activities subscription:", error);
    
    // Try to get from cache
    safeCacheGet(`activities_${farmId}`)
      .then(cachedActivities => {
        if (cachedActivities) {
          callback(cachedActivities);
        } else {
          callback([]);
        }
      })
      .catch(() => callback([]));
    
    return () => {};
  }
};

// Get dashboard stats
export const getDashboardStats = async (userRole: string, farmId?: string): Promise<{
  stats: any;
  recentActivity: Activity[];
}> => {
  try {
    if (!farmId) {
      return {
        stats: {
          totalItems: 0,
          lowStockCount: 0,
          expiryAlerts: 0,
          pendingRequests: 0,
          caretakers: 0,
          totalUsers: 0,
          availableItems: 0,
          approvedRequests: 0,
          notes: 0
        },
        recentActivity: []
      };
    }
    
    // Get inventory items for the farm
    const inventoryItems = await getInventoryItems(farmId);
    
    // Calculate stats based on inventory
    const totalItems = inventoryItems.length;
    const lowStockCount = inventoryItems.filter(item => item.quantity <= item.minQuantity).length;
    
    const today = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(today.getDate() + 30);
    
    const expiryAlerts = inventoryItems.filter(item => {
      if (!item.expiryDate) return false;
      const expiryDate = new Date(item.expiryDate);
      return expiryDate <= thirtyDaysFromNow && expiryDate >= today;
    }).length;
    
    // Get recent activities
    const recentActivity = await getActivities(farmId, 3);
    
    // Mock data for other stats based on user role
    let stats: any = {
      totalItems,
      lowStockCount,
      expiryAlerts
    };
    
    if (userRole === "owner") {
      stats = {
        ...stats,
        totalUsers: 12, // Mock data
        totalFarms: 4 // Will be overridden by actual count in the component
      };
    } else if (userRole === "admin") {
      stats = {
        ...stats,
        pendingRequests: 3, // Mock data
        caretakers: 5 // Mock data
      };
    } else if (userRole === "caretaker") {
      stats = {
        ...stats,
        availableItems: totalItems,
        pendingRequests: 2, // Mock data
        approvedRequests: 8, // Mock data
        notes: 4 // Mock data
      };
    }
    
    return {
      stats,
      recentActivity
    };
  } catch (error) {
    console.error("Error getting dashboard stats:", error);
    
    // Return default values
    return {
      stats: {
        totalItems: 0,
        lowStockCount: 0,
        expiryAlerts: 0,
        pendingRequests: 0,
        caretakers: 0,
        totalUsers: 0,
        availableItems: 0,
        approvedRequests: 0,
        notes: 0
      },
      recentActivity: []
    };
  }
};

// Create inventory move request
export const createInventoryMoveRequest = async (requestData: {
  itemId: string;
  itemName: string;
  quantity: number;
  unit: string;
  fromLocation: string;
  toLocation: string;
  requestedBy: string;
  requesterId: string;
  reason?: string;
}): Promise<InventoryRequest> => {
  try {
    if (!requestData.fromLocation) {
      throw new Error("Source farm location is required");
    }
    
    // Remove undefined values
    const cleanedRequestData = Object.entries(requestData).reduce((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string, any>);
    
    const newRequest = {
      ...cleanedRequestData,
      status: "pending",
      createdAt: new Date().toISOString(),
    };
    
    // Add to Firestore under the source farm's requests
    const requestsRef = collection(db, "farms", requestData.fromLocation, "inventory_requests");
    const docRef = await addDoc(requestsRef, newRequest);
    
    const createdRequest: InventoryRequest = {
      id: docRef.id,
      ...newRequest as InventoryRequest
    };
    
    // Create activity record
    await createActivity({
      type: "request_created",
      userId: requestData.requesterId,
      farmId: requestData.fromLocation,
      itemId: requestData.itemId,
      itemName: requestData.itemName,
      details: `Requested transfer of ${requestData.quantity} ${requestData.unit} of ${requestData.itemName} to ${requestData.toLocation}`,
      quantity: requestData.quantity,
      unit: requestData.unit
    });
    
    return createdRequest;
  } catch (error) {
    console.error("Error creating inventory request:", error);
    throw error;
  }
};

// Get inventory requests
export const getInventoryRequests = async (
  userId?: string,
  status?: "pending" | "approved" | "rejected",
  farmId?: string
): Promise<InventoryRequest[]> => {
  try {
    const requests: InventoryRequest[] = [];
    
    if (farmId) {
      // Get requests for this specific farm
      const requestsRef = collection(db, "farms", farmId, "inventory_requests");
      let requestsQuery = query(requestsRef);
      
      // Add filters
      if (userId) {
        requestsQuery = query(requestsRef, where("requesterId", "==", userId));
      }
      
      if (status) {
        requestsQuery = query(requestsRef, where("status", "==", status));
      }
      
      const requestsSnapshot = await getDocs(requestsQuery);
      
      requestsSnapshot.forEach(doc => {
        requests.push({
          id: doc.id,
          ...doc.data()
        } as InventoryRequest);
      });
    } else {
      // Get all farms first to query their subcollections
      const farmsRef = collection(db, "farms");
      const farmsSnapshot = await getDocs(farmsRef);
      
      // For each farm, query its inventory_requests subcollection
      const queryPromises = farmsSnapshot.docs.map(async (farmDoc) => {
        const farmId = farmDoc.id;
        const requestsRef = collection(db, "farms", farmId, "inventory_requests");
        let requestsQuery = query(requestsRef);
        
        // Add filters
        if (userId) {
          requestsQuery = query(requestsRef, where("requesterId", "==", userId));
        }
        
        if (status) {
          requestsQuery = query(requestsRef, where("status", "==", status));
        }
        
        const requestsSnapshot = await getDocs(requestsQuery);
        
        requestsSnapshot.forEach(doc => {
          requests.push({
            id: doc.id,
            ...doc.data()
          } as InventoryRequest);
        });
      });
      
      // Wait for all queries to complete
      await Promise.all(queryPromises);
    }
    
    // Sort by createdAt
    requests.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
    
    return requests;
  } catch (error) {
    console.error("Error getting inventory requests:", error);
    return [];
  }
};

// Update inventory request status
export const updateInventoryRequestStatus = async (
  requestId: string,
  status: "approved" | "rejected",
  userId: string,
  userName: string,
  notes?: string,
  farmId?: string
): Promise<InventoryRequest> => {
  try {
    let request: InventoryRequest | null = null;
    let requestRef;
    
    if (farmId) {
      // Try to find the request in the specified farm
      const farmsRequestsRef = collection(db, "farms", farmId, "inventory_requests");
      const requestDoc = await getDoc(doc(farmsRequestsRef, requestId));
      
      if (requestDoc.exists()) {
        request = {
          id: requestDoc.id,
          ...requestDoc.data()
        } as InventoryRequest;
        requestRef = doc(farmsRequestsRef, requestId);
      }
    } else {
      // Search in all farms
      const farmsRef = collection(db, "farms");
      const farmsSnapshot = await getDocs(farmsRef);
      
      for (const farmDoc of farmsSnapshot.docs) {
        const farmId = farmDoc.id;
        const requestDoc = await getDoc(doc(db, "farms", farmId, "inventory_requests", requestId));
        
        if (requestDoc.exists()) {
          request = {
            id: requestDoc.id,
            ...requestDoc.data()
          } as InventoryRequest;
          requestRef = doc(db, "farms", farmId, "inventory_requests", requestId);
          break;
        }
      }
    }
    
    if (!request || !requestRef) {
      throw new Error("Request not found");
    }
    
    // Update request status
    const updateData: any = {
      status,
      updatedAt: new Date().toISOString(),
      notes: notes || ""
    };
    
    if (status === "approved") {
      updateData.approvedBy = userName;
      updateData.approvedById = userId;
    } else {
      updateData.rejectedBy = userName;
      updateData.rejectedById = userId;
    }
    
    await updateDoc(requestRef, updateData);
    
    // If approved, update inventory quantities
    if (status === "approved") {
      try {
        // Get source item
        const sourceItemRef = doc(db, 'farms', request.fromLocation, 'inventory', request.itemId);
        const sourceItemDoc = await getDoc(sourceItemRef);
        
        if (sourceItemDoc.exists()) {
          const sourceItem = sourceItemDoc.data() as InventoryItem;
          
          // Ensure there's enough quantity
          if (sourceItem.quantity >= request.quantity) {
            // Update source item quantity
            await updateDoc(sourceItemRef, {
              quantity: sourceItem.quantity - request.quantity,
              updatedAt: new Date().toISOString(),
              history: [
                ...(sourceItem.history || []),
                {
                  type: "transfer_out",
                  date: new Date().toISOString(),
                  userId: userId,
                  details: `Transferred ${request.quantity} ${request.unit} to ${request.toLocation}`
                }
              ]
            });
            
            // Check if destination already has this item
            const destItemsQuery = query(
              collection(db, 'farms', request.toLocation, 'inventory'),
              where("name", "==", request.itemName)
            );
            const destItemsSnapshot = await getDocs(destItemsQuery);
            
            if (!destItemsSnapshot.empty) {
              // Update existing item at destination
              const destItemDoc = destItemsSnapshot.docs[0];
              const destItem = destItemDoc.data() as InventoryItem;
              
              await updateDoc(destItemDoc.ref, {
                quantity: destItem.quantity + request.quantity,
                updatedAt: new Date().toISOString(),
                history: [
                  ...(destItem.history || []),
                  {
                    type: "transfer_in",
                    date: new Date().toISOString(),
                    userId: userId,
                    details: `Received ${request.quantity} ${request.unit} from ${request.fromLocation}`
                  }
                ]
              });
            } else {
              // Create new item at destination
              const newItem = {
                name: request.itemName,
                category: sourceItem.category,
                quantity: request.quantity,
                unit: request.unit,
                minQuantity: sourceItem.minQuantity,
                location: request.toLocation,
                description: sourceItem.description,
                supplier: sourceItem.supplier,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                history: [
                  {
                    type: "transfer_in",
                    date: new Date().toISOString(),
                    userId: userId,
                    details: `Received ${request.quantity} ${request.unit} from ${request.fromLocation}`
                  }
                ]
              };
              
              await addDoc(collection(db, 'farms', request.toLocation, 'inventory'), newItem);
            }
            
            // Create activity records
            await createActivity({
              type: "transfer_approved",
              userId: userId,
              farmId: request.fromLocation,
              itemId: request.itemId,
              itemName: request.itemName,
              details: `Approved transfer of ${request.quantity} ${request.unit} of ${request.itemName} to ${request.toLocation}`,
              quantity: request.quantity,
              unit: request.unit
            });
            
            // Also create activity for destination farm
            await createActivity({
              type: "transfer_received",
              userId: userId,
              farmId: request.toLocation,
              itemId: request.itemId,
              itemName: request.itemName,
              details: `Received ${request.quantity} ${request.unit} of ${request.itemName} from ${request.fromLocation}`,
              quantity: request.quantity,
              unit: request.unit
            });
          } else {
            // Not enough quantity, reject the request
            await updateDoc(requestRef, {
              status: "rejected",
              updatedAt: new Date().toISOString(),
              rejectedBy: userName,
              rejectedById: userId,
              notes: "Insufficient quantity available"
            });
            
            await createActivity({
              type: "transfer_rejected",
              userId: userId,
              farmId: request.fromLocation,
              itemId: request.itemId,
              itemName: request.itemName,
              details: `Rejected transfer due to insufficient quantity`,
              quantity: request.quantity,
              unit: request.unit
            });
            
            throw new Error("Insufficient quantity available");
          }
        } else {
          // Item not found, reject the request
          await updateDoc(requestRef, {
            status: "rejected",
            updatedAt: new Date().toISOString(),
            rejectedBy: userName,
            rejectedById: userId,
            notes: "Item no longer exists"
          });
          
          await createActivity({
            type: "transfer_rejected",
            userId: userId,
            farmId: request.fromLocation,
            itemId: request.itemId,
            itemName: request.itemName,
            details: `Rejected transfer because item no longer exists`,
            quantity: request.quantity,
            unit: request.unit
          });
          
          throw new Error("Item no longer exists");
        }
      } catch (error) {
        console.error("Error processing inventory transfer:", error);
        throw error;
      }
    } else {
      // Create activity record for rejection
      await createActivity({
        type: "transfer_rejected",
        userId: userId,
        farmId: request.fromLocation,
        itemId: request.itemId,
        itemName: request.itemName,
        details: `Rejected transfer of ${request.quantity} ${request.unit} of ${request.itemName} to ${request.toLocation}`,
        quantity: request.quantity,
        unit: request.unit
      });
    }
    
    return {
      ...request,
      ...updateData
    };
  } catch (error) {
    console.error("Error updating inventory request:", error);
    throw error;
  }
};
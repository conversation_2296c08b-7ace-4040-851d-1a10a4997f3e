import * as FileSystem from 'expo-file-system';

// OpenRouter configuration
const OPENROUTER_API_KEY = '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';
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

// OpenRouter client configuration
const openRouterConfig = {
  apiKey: OPENROUTER_API_KEY,
  baseURL: OPENROUTER_BASE_URL,
  defaultHeaders: {
    'HTTP-Referer': 'https://farm-inventory-app.com',
    'X-Title': 'Farm Inventory Control App',
  },
};

// Enhanced analysis result types (using OpenRouter)
export interface OpenAIAnalysisResult {
  type: 'inventory' | 'machinery' | 'unknown';
  confidence: number;
  extractedData: EnhancedInventoryData | EnhancedMachineryData | null;
  suggestions: string[];
  description: string;
  rawAnalysis: string;
}

export interface EnhancedInventoryData {
  name: string;
  category: string;
  quantity?: number;
  unit?: string;
  unitPrice?: number;
  totalPrice?: number;
  supplier?: string;
  brand?: string;
  model?: string;
  expiryDate?: string;
  batchNumber?: string;
  condition?: string;
  minStockLevel?: number;
  maxStockLevel?: number;
  location?: string;
  notes?: string;
}

export interface EnhancedMachineryData {
  name: string;
  type: string;
  brand?: string;
  model?: string;
  year?: number;
  serialNumber?: string;
  registrationNumber?: string;
  condition?: string;
  fuelType?: string;
  fuelCapacity?: number;
  currentFuelLevel?: number;
  odometerReading?: number;
  lastMaintenanceDate?: string;
  nextMaintenanceDate?: string;
  purchasePrice?: number;
  currentValue?: number;
  supplier?: string;
  location?: string;
  notes?: string;
}

// Convert image to base64
const convertImageToBase64 = async (imageUri: string): Promise<string> => {
  try {
    const base64 = await FileSystem.readAsStringAsync(imageUri, {
      encoding: FileSystem.EncodingType.Base64,
    });
    return base64;
  } catch (error) {
    console.error('Error converting image to base64:', error);
    throw new Error('Failed to process image');
  }
};

// Analyze image using OpenRouter Vision API
export const analyzeImageWithOpenAI = async (
  imageUri: string,
  userMessage?: string
): Promise<OpenAIAnalysisResult> => {
  try {
    // Convert image to base64
    const base64Image = await convertImageToBase64(imageUri);

    // Create the prompt for comprehensive analysis
    const systemPrompt = `You are an expert farm inventory and machinery analyst. Analyze the provided image and extract as much detailed information as possible.

CRITICAL: You must accurately distinguish between INVENTORY ITEMS and MACHINERY. This is extremely important for proper categorization.

INVENTORY ITEMS (consumable/storable supplies) include:
- Fertilizer bags/containers (DAP, Urea, NPK, Compost, Manure, etc.)
- Seed packets/bags (wheat, corn, rice, cotton, vegetables, etc.)
- Pesticide bottles/containers (insecticides, herbicides, fungicides, weedicides)
- Feed bags/containers (animal feed, poultry feed, cattle feed, fodder)
- Hand tools (shovels, hoes, rakes, spades, pruning shears, etc.)
- Fuel containers (diesel, gasoline in drums/barrels/containers)
- Medication bottles/packages (veterinary medicines, antibiotics)
- Vaccination vials/packages (livestock vaccines, poultry vaccines)
- Irrigation supplies (pipes, sprinklers, drip systems - when packaged/stored)
- Farm chemicals (growth regulators, soil conditioners)
- Packaging materials (bags, containers, labels)
- Safety equipment (gloves, masks, protective gear)
- Any packaged, containerized, or storable farm supplies

MACHINERY (mechanical equipment/vehicles) include:
- Large vehicles (tractors, harvesters, combines, threshers)
- Farm equipment (planters, seeders, sprayers, cultivators, plows, harrows)
- Vehicles (cars, jeeps, trucks, motorbikes, bicycles, ATVs)
- Irrigation machinery (pumps, generators, motors)
- Processing equipment (mills, grinders, choppers)
- Large mechanical tools and equipment
- Any motorized or large mechanical devices

DECISION CRITERIA:
- If it's packaged, bagged, bottled, or containerized → INVENTORY
- If it's a vehicle, machine, or large equipment → MACHINERY
- If it's a small hand tool → INVENTORY
- If it's motorized or has an engine → MACHINERY
- If you can store it on shelves or in warehouses → INVENTORY
- If it requires fuel, electricity, or mechanical operation → MACHINERY

Please identify if this is:
1. INVENTORY ITEM (packaged supplies, chemicals, seeds, feed, tools, etc.)
2. MACHINERY (vehicles, large equipment, mechanical devices)
3. UNKNOWN (if you cannot clearly identify)

For INVENTORY items, extract:
- Name/Product name (be specific, e.g., "DAP Fertilizer", "Wheat Seeds", "Roundup Herbicide")
- Category (fertilizers, seeds, pesticides, tools, feed, medication, fuel, irrigation, safety, packaging, other)
- Quantity (if visible on packaging/labels - look for numbers on bags/containers)
- Unit (bags, kg, liters, tons, pieces, bottles, boxes, packets, gallons, pounds)
- Brand/Manufacturer (look for company names and logos)
- Model/Product code (specific product variants or codes)
- Unit price (if visible on labels/receipts/price tags)
- Total price (if visible on receipts or multiple items)
- Supplier/Distributor (if visible on packaging or documents)
- Expiry date (if visible - important for chemicals and medicines)
- Batch number/Lot number (if visible on packaging)
- Condition (new, good, used, damaged, expired)
- Storage requirements (if mentioned - cool, dry, etc.)
- Active ingredients (for chemicals and medicines)
- Application instructions (if visible on labels)
- Any other relevant details

For MACHINERY items, extract:
- Name/Equipment name (be specific, e.g., "John Deere 5050D Tractor", "Honda Civic Car")
- Type (tractor, harvester, planter, sprayer, cultivator, car, jeep, motorbike, bicycle, pump, generator, other)
- Brand/Manufacturer (look for logos and brand names)
- Model (specific model number or name)
- Year of manufacture (if visible on vehicle or documents)
- Serial number (if visible on equipment)
- Registration number (license plate or registration stickers)
- Condition (working, maintenance, malfunction, excellent, good, fair, poor, damaged)
- Fuel type (diesel, gasoline, electric, hybrid, manual/pedal)
- Engine specifications (horsepower, displacement if visible)
- Fuel capacity (tank size if visible or mentioned)
- Current fuel level (if gauge is visible)
- Odometer/Hour meter reading (if visible)
- Tire condition (if visible)
- Attachments or implements (if any are attached)
- Purchase price (if visible on documents)
- Supplier/Dealer (if visible on documents or stickers)
- Maintenance records (if visible)
- Any other relevant specifications or details

RESPONSE FORMAT: Respond in JSON format with the following structure:
{
  "type": "inventory" | "machinery" | "unknown",
  "confidence": 0.0-1.0,
  "name": "specific item name",
  "category_or_type": "specific category for inventory or type for machinery",
  "brand": "manufacturer/brand name",
  "model": "model name/number",
  "condition": "condition assessment",
  "extracted_details": {
    // All other relevant fields based on type
  },
  "reasoning": "brief explanation of why you classified it as inventory/machinery",
  "visible_text": "any text you can read in the image",
  "recommendations": ["list of suggestions for the user"]
}

Be extremely thorough and extract every detail you can see. Pay special attention to text, labels, numbers, and brand markings.`;

    const userPrompt = userMessage
      ? `User message: "${userMessage}"\n\nPlease analyze this image and provide comprehensive details about the farm inventory or machinery shown.`
      : 'Please analyze this image and provide comprehensive details about the farm inventory or machinery shown.';

    // Call OpenRouter Vision API
    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://farm-inventory-app.com',
        'X-Title': 'Farm Inventory Control App',
      },
      body: JSON.stringify({
        model: "anthropic/claude-3.5-sonnet",
        messages: [
          {
            role: "system",
            content: systemPrompt
          },
          {
            role: "user",
            content: [
              {
                type: "text",
                text: userPrompt
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/jpeg;base64,${base64Image}`,
                  detail: "high"
                }
              }
            ]
          }
        ],
        max_tokens: 1000,
        temperature: 0.1
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const analysisText = data.choices[0]?.message?.content || '';

    // Parse the response
    return parseOpenAIResponse(analysisText, userMessage);

  } catch (error) {
    console.error('Error analyzing image with OpenRouter:', error);

    // Fallback to basic analysis if OpenRouter fails
    return {
      type: 'unknown',
      confidence: 0.1,
      extractedData: null,
      suggestions: [
        'AI analysis failed, please try again',
        'Check your internet connection',
        'Provide more descriptive text with the image'
      ],
      description: 'Failed to analyze image with AI. Please try again or provide more details.',
      rawAnalysis: 'OpenRouter API error'
    };
  }
};

// Parse OpenRouter response and structure the data
const parseOpenAIResponse = (analysisText: string, userMessage?: string): OpenAIAnalysisResult => {
  try {


    // Try to extract JSON from the response
    let jsonMatch = analysisText.match(/\{[\s\S]*\}/);
    let analysisData: any = {};
    
    if (jsonMatch) {
      try {
        analysisData = JSON.parse(jsonMatch[0]);
      } catch (e) {
        // If JSON parsing fails, extract information manually
        analysisData = extractInfoManually(analysisText);
      }
    } else {
      analysisData = extractInfoManually(analysisText);
    }

    // Determine type and confidence
    const type = determineType(analysisData, analysisText);
    const confidence = analysisData.confidence || estimateConfidence(analysisText, type);
    
    // Structure the extracted data
    const extractedData = type === 'inventory' 
      ? structureInventoryData(analysisData, analysisText)
      : type === 'machinery'
      ? structureMachineryData(analysisData, analysisText)
      : null;

    return {
      type,
      confidence: Math.min(Math.max(confidence, 0), 1),
      extractedData,
      suggestions: generateSuggestions(type, confidence, extractedData),
      description: generateDescription(type, confidence, extractedData),
      rawAnalysis: analysisText
    };

  } catch (error) {
    console.error('Error parsing OpenAI response:', error);
    return {
      type: 'unknown',
      confidence: 0.2,
      extractedData: null,
      suggestions: ['Could not parse analysis results', 'Try taking a clearer photo'],
      description: 'Analysis completed but results could not be parsed properly.',
      rawAnalysis: analysisText
    };
  }
};

// Helper functions for parsing
const determineType = (data: any, text: string): 'inventory' | 'machinery' | 'unknown' => {
  const lowerText = text.toLowerCase();

  // First check if the AI explicitly provided a type
  if (data.type && ['inventory', 'machinery', 'unknown'].includes(data.type)) {
    return data.type;
  }

  // Enhanced inventory detection (prioritize packaged/containerized items)
  const inventoryKeywords = [
    'fertilizer', 'seed', 'pesticide', 'insecticide', 'herbicide', 'fungicide',
    'bag', 'bottle', 'container', 'package', 'packet', 'box', 'drum', 'barrel',
    'chemical', 'feed', 'medication', 'medicine', 'vaccination', 'vaccine',
    'urea', 'dap', 'npk', 'compost', 'manure', 'supplement',
    'tool', 'shovel', 'hoe', 'rake', 'spade', 'gloves', 'mask',
    'fuel container', 'diesel container', 'oil container',
    'irrigation supplies', 'pipes', 'sprinkler', 'drip system',
    'safety equipment', 'protective gear', 'packaging material'
  ];

  // Enhanced machinery detection (prioritize vehicles and large equipment)
  const machineryKeywords = [
    'tractor', 'harvester', 'combine', 'thresher', 'reaper',
    'planter', 'seeder', 'sprayer', 'cultivator', 'plow', 'harrow',
    'car', 'jeep', 'truck', 'vehicle', 'motorbike', 'motorcycle', 'bicycle',
    'pump', 'generator', 'motor', 'engine', 'mill', 'grinder', 'chopper',
    'machinery', 'equipment', 'implement', 'attachment'
  ];

  // Count matches for each type
  const inventoryMatches = inventoryKeywords.filter(keyword => lowerText.includes(keyword)).length;
  const machineryMatches = machineryKeywords.filter(keyword => lowerText.includes(keyword)).length;

  // Additional context clues
  let inventoryScore = inventoryMatches;
  let machineryScore = machineryMatches;

  // Boost inventory score for packaging indicators
  if (lowerText.includes('packaged') || lowerText.includes('stored') ||
      lowerText.includes('shelf') || lowerText.includes('warehouse') ||
      lowerText.includes('label') || lowerText.includes('expiry') ||
      lowerText.includes('batch') || lowerText.includes('quantity')) {
    inventoryScore += 2;
  }

  // Boost machinery score for vehicle/equipment indicators
  if (lowerText.includes('wheel') || lowerText.includes('tire') ||
      lowerText.includes('steering') || lowerText.includes('cabin') ||
      lowerText.includes('fuel tank') || lowerText.includes('registration') ||
      lowerText.includes('license plate') || lowerText.includes('odometer') ||
      lowerText.includes('horsepower') || lowerText.includes('engine')) {
    machineryScore += 2;
  }

  // Make decision based on scores
  if (inventoryScore > machineryScore && inventoryScore > 0) {
    return 'inventory';
  } else if (machineryScore > inventoryScore && machineryScore > 0) {
    return 'machinery';
  }

  return 'unknown';
};

const estimateConfidence = (text: string, type: string): number => {
  const lowerText = text.toLowerCase();
  let confidence = 0.5;
  
  // Increase confidence based on specific details mentioned
  if (lowerText.includes('brand') || lowerText.includes('model')) confidence += 0.1;
  if (lowerText.includes('price') || lowerText.includes('cost')) confidence += 0.1;
  if (lowerText.includes('quantity') || lowerText.includes('amount')) confidence += 0.1;
  if (lowerText.includes('clear') || lowerText.includes('visible')) confidence += 0.1;
  if (lowerText.includes('label') || lowerText.includes('text')) confidence += 0.1;
  
  return Math.min(confidence, 0.95);
};

const extractInfoManually = (text: string): any => {
  const data: any = {};
  const lines = text.split('\n');
  
  for (const line of lines) {
    const lowerLine = line.toLowerCase();
    
    // Extract common fields
    if (lowerLine.includes('name:') || lowerLine.includes('product:')) {
      data.name = line.split(':')[1]?.trim();
    }
    if (lowerLine.includes('brand:') || lowerLine.includes('manufacturer:')) {
      data.brand = line.split(':')[1]?.trim();
    }
    if (lowerLine.includes('quantity:') || lowerLine.includes('amount:')) {
      const qty = line.match(/\d+/);
      if (qty) data.quantity = parseInt(qty[0]);
    }
    if (lowerLine.includes('price:') || lowerLine.includes('cost:')) {
      const price = line.match(/[\d,]+/);
      if (price) data.price = parseFloat(price[0].replace(',', ''));
    }
  }
  
  return data;
};

const structureInventoryData = (data: any, text: string): EnhancedInventoryData => {
  return {
    name: data.name || data.product_name || extractFromText(text, ['name', 'product']) || 'Unknown Item',
    category: data.category || categorizeFromText(text) || 'other',
    quantity: data.quantity || extractNumberFromText(text, ['quantity', 'amount', 'count']) || 1,
    unit: data.unit || extractUnitFromText(text) || 'pieces',
    unitPrice: data.unit_price || data.price || extractNumberFromText(text, ['price', 'cost', 'rate']) || 0,
    totalPrice: data.total_price || (data.quantity && data.unit_price ? data.quantity * data.unit_price : 0),
    supplier: data.supplier || data.distributor || extractFromText(text, ['supplier', 'distributor', 'dealer']) || '',
    brand: data.brand || data.manufacturer || extractFromText(text, ['brand', 'manufacturer', 'company']) || '',
    model: data.model || data.product_code || extractFromText(text, ['model', 'code', 'variant']) || '',
    expiryDate: data.expiry_date || data.expiration || extractDateFromText(text) || '',
    batchNumber: data.batch_number || data.lot_number || extractFromText(text, ['batch', 'lot']) || '',
    condition: data.condition || determineConditionFromText(text) || 'good',
    minStockLevel: data.min_stock || 10,
    maxStockLevel: data.max_stock || 100,
    location: data.location || extractFromText(text, ['location', 'store', 'warehouse']) || '',
    notes: `Analyzed via OpenAI Vision API. ${data.notes || ''}`
  };
};

const structureMachineryData = (data: any, text: string): EnhancedMachineryData => {
  return {
    name: data.name || data.equipment_name || extractFromText(text, ['name', 'equipment']) || 'Unknown Equipment',
    type: data.type || categorizeMachineryFromText(text) || 'other',
    brand: data.brand || data.manufacturer || extractFromText(text, ['brand', 'manufacturer', 'make']) || '',
    model: data.model || extractFromText(text, ['model', 'variant', 'series']) || '',
    year: data.year || data.manufacture_year || extractYearFromText(text) || new Date().getFullYear(),
    serialNumber: data.serial_number || extractFromText(text, ['serial', 'chassis']) || '',
    registrationNumber: data.registration_number || data.reg_number || extractFromText(text, ['registration', 'reg']) || '',
    condition: data.condition || determineConditionFromText(text) || 'working',
    fuelType: data.fuel_type || extractFuelTypeFromText(text) || 'diesel',
    fuelCapacity: data.fuel_capacity || 100,
    currentFuelLevel: data.current_fuel || 100,
    odometerReading: data.odometer || data.hours || extractNumberFromText(text, ['hours', 'odometer', 'mileage']) || 0,
    lastMaintenanceDate: data.last_maintenance || '',
    nextMaintenanceDate: data.next_maintenance || '',
    purchasePrice: data.purchase_price || data.price || extractNumberFromText(text, ['price', 'cost', 'value']) || 0,
    currentValue: data.current_value || data.purchase_price || 0,
    supplier: data.supplier || data.dealer || extractFromText(text, ['supplier', 'dealer', 'distributor']) || '',
    location: data.location || extractFromText(text, ['location', 'yard', 'shed']) || '',
    notes: `Analyzed via OpenAI Vision API. ${data.notes || ''}`
  };
};

// Utility functions for text extraction
const extractFromText = (text: string, keywords: string[]): string => {
  const lines = text.split('\n');
  for (const line of lines) {
    for (const keyword of keywords) {
      if (line.toLowerCase().includes(keyword + ':')) {
        return line.split(':')[1]?.trim() || '';
      }
    }
  }
  return '';
};

const extractNumberFromText = (text: string, keywords: string[]): number => {
  const lines = text.split('\n');
  for (const line of lines) {
    for (const keyword of keywords) {
      if (line.toLowerCase().includes(keyword)) {
        const match = line.match(/[\d,]+\.?\d*/);
        if (match) {
          return parseFloat(match[0].replace(',', ''));
        }
      }
    }
  }
  return 0;
};

const extractUnitFromText = (text: string): string => {
  const units = ['bags', 'kg', 'liters', 'tons', 'pieces', 'bottles', 'boxes', 'packets'];
  const lowerText = text.toLowerCase();

  for (const unit of units) {
    if (lowerText.includes(unit)) {
      return unit;
    }
  }
  return 'pieces';
};

const categorizeFromText = (text: string): string => {
  const lowerText = text.toLowerCase();

  // Fertilizers
  if (lowerText.includes('fertilizer') || lowerText.includes('urea') ||
      lowerText.includes('dap') || lowerText.includes('npk') ||
      lowerText.includes('compost') || lowerText.includes('manure') ||
      lowerText.includes('phosphate') || lowerText.includes('potash') ||
      lowerText.includes('nitrogen') || lowerText.includes('organic fertilizer')) {
    return 'fertilizers';
  }

  // Seeds
  if (lowerText.includes('seed') || lowerText.includes('grain') ||
      lowerText.includes('wheat') || lowerText.includes('corn') ||
      lowerText.includes('rice') || lowerText.includes('cotton') ||
      lowerText.includes('vegetable seed') || lowerText.includes('flower seed')) {
    return 'seeds';
  }

  // Pesticides
  if (lowerText.includes('pesticide') || lowerText.includes('insecticide') ||
      lowerText.includes('herbicide') || lowerText.includes('fungicide') ||
      lowerText.includes('weedicide') || lowerText.includes('roundup') ||
      lowerText.includes('spray') || lowerText.includes('chemical control')) {
    return 'pesticides';
  }

  // Tools
  if (lowerText.includes('tool') || lowerText.includes('shovel') ||
      lowerText.includes('hoe') || lowerText.includes('rake') ||
      lowerText.includes('spade') || lowerText.includes('pruning') ||
      lowerText.includes('hand tool') || lowerText.includes('garden tool')) {
    return 'tools';
  }

  // Fuel
  if (lowerText.includes('fuel') || lowerText.includes('diesel') ||
      lowerText.includes('gasoline') || lowerText.includes('petrol') ||
      lowerText.includes('oil') || lowerText.includes('lubricant')) {
    return 'fuel';
  }

  // Feed
  if (lowerText.includes('feed') || lowerText.includes('fodder') ||
      lowerText.includes('animal feed') || lowerText.includes('cattle feed') ||
      lowerText.includes('poultry feed') || lowerText.includes('hay') ||
      lowerText.includes('silage') || lowerText.includes('supplement')) {
    return 'feed';
  }

  // Medication
  if (lowerText.includes('medicine') || lowerText.includes('vaccine') ||
      lowerText.includes('medication') || lowerText.includes('antibiotic') ||
      lowerText.includes('veterinary') || lowerText.includes('treatment') ||
      lowerText.includes('injection') || lowerText.includes('tablet')) {
    return 'medication';
  }

  // Irrigation
  if (lowerText.includes('irrigation') || lowerText.includes('pipe') ||
      lowerText.includes('sprinkler') || lowerText.includes('drip') ||
      lowerText.includes('hose') || lowerText.includes('fitting')) {
    return 'irrigation';
  }

  // Safety
  if (lowerText.includes('safety') || lowerText.includes('protective') ||
      lowerText.includes('gloves') || lowerText.includes('mask') ||
      lowerText.includes('helmet') || lowerText.includes('boots')) {
    return 'safety';
  }

  // Packaging
  if (lowerText.includes('packaging') || lowerText.includes('container') ||
      lowerText.includes('storage') || lowerText.includes('bag') ||
      lowerText.includes('box') || lowerText.includes('label')) {
    return 'packaging';
  }

  return 'other';
};

const categorizeMachineryFromText = (text: string): string => {
  const lowerText = text.toLowerCase();

  // Tractors
  if (lowerText.includes('tractor') || lowerText.includes('john deere') ||
      lowerText.includes('massey ferguson') || lowerText.includes('new holland') ||
      lowerText.includes('kubota') || lowerText.includes('mahindra')) {
    return 'tractor';
  }

  // Harvesters
  if (lowerText.includes('harvester') || lowerText.includes('combine') ||
      lowerText.includes('reaper') || lowerText.includes('thresher')) {
    return 'harvester';
  }

  // Planters
  if (lowerText.includes('planter') || lowerText.includes('seeder') ||
      lowerText.includes('drill') || lowerText.includes('sowing')) {
    return 'planter';
  }

  // Sprayers
  if (lowerText.includes('sprayer') || lowerText.includes('spray machine') ||
      lowerText.includes('boom sprayer') || lowerText.includes('mist blower')) {
    return 'sprayer';
  }

  // Cultivators
  if (lowerText.includes('cultivator') || lowerText.includes('plow') ||
      lowerText.includes('harrow') || lowerText.includes('disc') ||
      lowerText.includes('tillage')) {
    return 'cultivator';
  }

  // Cars
  if (lowerText.includes('car') || lowerText.includes('sedan') ||
      lowerText.includes('hatchback') || lowerText.includes('suv') ||
      lowerText.includes('honda') || lowerText.includes('toyota') ||
      lowerText.includes('suzuki') || lowerText.includes('hyundai')) {
    return 'car';
  }

  // Jeeps
  if (lowerText.includes('jeep') || lowerText.includes('4x4') ||
      lowerText.includes('off-road') || lowerText.includes('pickup')) {
    return 'jeep';
  }

  // Motorbikes
  if (lowerText.includes('motorbike') || lowerText.includes('motorcycle') ||
      lowerText.includes('bike') || lowerText.includes('scooter') ||
      lowerText.includes('yamaha') || lowerText.includes('honda bike')) {
    return 'motorbike';
  }

  // Bicycles
  if (lowerText.includes('bicycle') || lowerText.includes('cycle') ||
      lowerText.includes('pedal') || lowerText.includes('mountain bike')) {
    return 'bicycle';
  }

  // Pumps
  if (lowerText.includes('pump') || lowerText.includes('water pump') ||
      lowerText.includes('irrigation pump') || lowerText.includes('submersible')) {
    return 'pump';
  }

  // Generators
  if (lowerText.includes('generator') || lowerText.includes('genset') ||
      lowerText.includes('power generator') || lowerText.includes('diesel generator')) {
    return 'generator';
  }

  return 'other';
};

const extractDateFromText = (text: string): string => {
  const dateMatch = text.match(/\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}/);
  return dateMatch ? dateMatch[0] : '';
};

const extractYearFromText = (text: string): number => {
  const yearMatch = text.match(/\b(19|20)\d{2}\b/);
  return yearMatch ? parseInt(yearMatch[0]) : new Date().getFullYear();
};

const extractFuelTypeFromText = (text: string): string => {
  const lowerText = text.toLowerCase();

  if (lowerText.includes('diesel')) return 'diesel';
  if (lowerText.includes('petrol') || lowerText.includes('gasoline')) return 'gasoline';
  if (lowerText.includes('electric')) return 'electric';
  if (lowerText.includes('hybrid')) return 'hybrid';

  return 'diesel';
};

const determineConditionFromText = (text: string): string => {
  const lowerText = text.toLowerCase();

  if (lowerText.includes('excellent') || lowerText.includes('new')) return 'excellent';
  if (lowerText.includes('good') || lowerText.includes('working')) return 'good';
  if (lowerText.includes('fair') || lowerText.includes('used')) return 'fair';
  if (lowerText.includes('poor') || lowerText.includes('damaged')) return 'poor';
  if (lowerText.includes('maintenance') || lowerText.includes('repair')) return 'maintenance';

  return 'good';
};

const generateSuggestions = (type: string, confidence: number, data: any): string[] => {
  const suggestions: string[] = [];

  if (confidence < 0.7) {
    suggestions.push('Take a clearer photo for better analysis');
    suggestions.push('Ensure labels and text are visible');
    suggestions.push('Add descriptive text with the image');
  }

  if (type === 'inventory') {
    suggestions.push('Verify extracted quantity and unit');
    suggestions.push('Check supplier and price information');
    suggestions.push('Set appropriate stock levels');
  } else if (type === 'machinery') {
    suggestions.push('Verify model and year information');
    suggestions.push('Check registration/serial numbers');
    suggestions.push('Schedule maintenance if needed');
  }

  return suggestions;
};

const generateDescription = (type: string, confidence: number, data: any): string => {
  const confidencePercent = Math.round(confidence * 100);

  if (type === 'inventory' && data) {
    let description = `✅ Identified as INVENTORY ITEM: ${data.category} - "${data.name}" (${confidencePercent}% confidence)`;

    if (data.quantity && data.unit) {
      description += `\n📦 Quantity: ${data.quantity} ${data.unit}`;
    }
    if (data.brand) {
      description += `\n🏷️ Brand: ${data.brand}`;
    }
    if (data.condition) {
      description += `\n🔍 Condition: ${data.condition}`;
    }

    description += `\n\n💡 This item will be added to your inventory system for tracking stock levels and usage.`;

    return description;
  } else if (type === 'machinery' && data) {
    let description = `✅ Identified as MACHINERY: ${data.type} - "${data.name}" (${confidencePercent}% confidence)`;

    if (data.brand) {
      description += `\n🏷️ Brand: ${data.brand}`;
    }
    if (data.model) {
      description += `\n📋 Model: ${data.model}`;
    }
    if (data.condition) {
      description += `\n🔍 Condition: ${data.condition}`;
    }
    if (data.fuelType) {
      description += `\n⛽ Fuel Type: ${data.fuelType}`;
    }

    description += `\n\n💡 This equipment will be added to your machinery system for maintenance tracking and usage monitoring.`;

    return description;
  } else {
    return `❓ Analysis completed with ${confidencePercent}% confidence, but could not clearly identify whether this is inventory or machinery.\n\n💡 Try taking a clearer photo or provide more descriptive text to help with identification.`;
  }
};

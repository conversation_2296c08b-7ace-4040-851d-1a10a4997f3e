import { getMachineryByFarm } from './machinery-service';
import { getInventoryItems } from './inventory-service';

// Define browse types
export type BrowseType = 'all_machinery' | 'all_inventory' | 'tractors' | 'harvesters' | 'jeeps' | 'cars' | 'tools' | 'fertilizers' | 'seeds' | 'pesticides';

// Define browse result structure
export interface BrowseResult {
  type: BrowseType;
  title: string;
  items: any[];
  category: 'machinery' | 'inventory';
  count: number;
}

// Detect browse commands from user message
export const detectBrowseCommand = (message: string): { type: BrowseType | null; confidence: number } => {
  const lowerMessage = message.toLowerCase();
  
  // Check for "show all" patterns
  if (lowerMessage.includes('show all') || lowerMessage.includes('list all') || lowerMessage.includes('display all')) {
    
    // Machinery categories
    if (lowerMessage.includes('machinery') || lowerMessage.includes('machines')) {
      return { type: 'all_machinery', confidence: 0.95 };
    }
    
    if (lowerMessage.includes('tractor')) {
      return { type: 'tractors', confidence: 0.95 };
    }
    
    if (lowerMessage.includes('harvester')) {
      return { type: 'harvesters', confidence: 0.95 };
    }
    
    if (lowerMessage.includes('jeep')) {
      return { type: 'jeeps', confidence: 0.95 };
    }
    
    if (lowerMessage.includes('car')) {
      return { type: 'cars', confidence: 0.95 };
    }
    
    // Inventory categories
    if (lowerMessage.includes('inventory') || lowerMessage.includes('items')) {
      return { type: 'all_inventory', confidence: 0.95 };
    }
    
    if (lowerMessage.includes('tool')) {
      return { type: 'tools', confidence: 0.95 };
    }
    
    if (lowerMessage.includes('fertilizer')) {
      return { type: 'fertilizers', confidence: 0.95 };
    }
    
    if (lowerMessage.includes('seed')) {
      return { type: 'seeds', confidence: 0.95 };
    }
    
    if (lowerMessage.includes('pesticide')) {
      return { type: 'pesticides', confidence: 0.95 };
    }
  }
  
  // Check for simple category commands
  if (lowerMessage.includes('machinery') && !lowerMessage.includes('add') && !lowerMessage.includes('create')) {
    return { type: 'all_machinery', confidence: 0.85 };
  }
  
  if (lowerMessage.includes('inventory') && !lowerMessage.includes('add') && !lowerMessage.includes('create')) {
    return { type: 'all_inventory', confidence: 0.85 };
  }
  
  if (lowerMessage.includes('tractor') && !lowerMessage.includes('add') && !lowerMessage.includes('create')) {
    return { type: 'tractors', confidence: 0.85 };
  }
  
  return { type: null, confidence: 0 };
};

// Get machinery by type
export const getMachineryByType = async (farmId: string, type?: string): Promise<any[]> => {
  try {
    const allMachinery = await getMachineryByFarm(farmId);

    if (!type) {
      return allMachinery;
    }

    return allMachinery.filter(machinery =>
      machinery.type?.toLowerCase() === type.toLowerCase()
    );
  } catch (error) {
    console.error('Error fetching machinery by type:', error);
    return [];
  }
};

// Get inventory by category
export const getInventoryByCategory = async (farmId: string, category?: string): Promise<any[]> => {
  try {
    const allInventory = await getInventoryItems(farmId);

    if (!category) {
      return allInventory;
    }

    return allInventory.filter(item =>
      item.category?.toLowerCase() === category.toLowerCase()
    );
  } catch (error) {
    console.error('Error fetching inventory by category:', error);
    return [];
  }
};

// Execute browse command
export const executeBrowseCommand = async (
  browseType: BrowseType,
  farmId: string
): Promise<BrowseResult> => {
  let items: any[] = [];
  let title = '';
  let category: 'machinery' | 'inventory' = 'machinery';
  
  switch (browseType) {
    case 'all_machinery':
      items = await getMachineryByType(farmId);
      title = 'All Machinery';
      category = 'machinery';
      break;
      
    case 'tractors':
      items = await getMachineryByType(farmId, 'tractor');
      title = 'All Tractors';
      category = 'machinery';
      break;
      
    case 'harvesters':
      items = await getMachineryByType(farmId, 'harvester');
      title = 'All Harvesters';
      category = 'machinery';
      break;
      
    case 'jeeps':
      items = await getMachineryByType(farmId, 'jeep');
      title = 'All Jeeps';
      category = 'machinery';
      break;
      
    case 'cars':
      items = await getMachineryByType(farmId, 'car');
      title = 'All Cars';
      category = 'machinery';
      break;
      
    case 'all_inventory':
      items = await getInventoryByCategory(farmId);
      title = 'All Inventory';
      category = 'inventory';
      break;
      
    case 'tools':
      items = await getInventoryByCategory(farmId, 'tools');
      title = 'All Tools';
      category = 'inventory';
      break;
      
    case 'fertilizers':
      items = await getInventoryByCategory(farmId, 'fertilizers');
      title = 'All Fertilizers';
      category = 'inventory';
      break;
      
    case 'seeds':
      items = await getInventoryByCategory(farmId, 'seeds');
      title = 'All Seeds';
      category = 'inventory';
      break;
      
    case 'pesticides':
      items = await getInventoryByCategory(farmId, 'pesticides');
      title = 'All Pesticides';
      category = 'inventory';
      break;
      
    default:
      items = [];
      title = 'Unknown Category';
  }
  
  return {
    type: browseType,
    title,
    items,
    category,
    count: items.length
  };
};

// Generate browse response message
export const generateBrowseMessage = (result: BrowseResult): string => {
  if (result.count === 0) {
    return `No ${result.title.toLowerCase()} found in your farm.`;
  }
  
  if (result.count === 1) {
    return `Found 1 ${result.category} in ${result.title}:`;
  }
  
  return `Found ${result.count} ${result.category === 'machinery' ? 'machinery items' : 'inventory items'} in ${result.title}:`;
};

// Get browse suggestions based on available data
export const getBrowseSuggestions = async (farmId: string): Promise<string[]> => {
  try {
    const [machinery, inventory] = await Promise.all([
      getMachineryByFarm(farmId),
      getInventoryItems(farmId)
    ]);
    
    const suggestions: string[] = [];
    
    // Add machinery suggestions if available
    if (machinery.length > 0) {
      suggestions.push('Show all machinery');
      
      // Check for specific types
      const tractors = machinery.filter(m => m.type?.toLowerCase() === 'tractor');
      if (tractors.length > 0) suggestions.push('Show all tractors');
      
      const harvesters = machinery.filter(m => m.type?.toLowerCase() === 'harvester');
      if (harvesters.length > 0) suggestions.push('Show all harvesters');
      
      const jeeps = machinery.filter(m => m.type?.toLowerCase() === 'jeep');
      if (jeeps.length > 0) suggestions.push('Show all jeeps');
    }
    
    // Add inventory suggestions if available
    if (inventory.length > 0) {
      suggestions.push('Show all inventory');
      
      // Check for specific categories
      const tools = inventory.filter(i => i.category?.toLowerCase() === 'tools');
      if (tools.length > 0) suggestions.push('Show all tools');
      
      const fertilizers = inventory.filter(i => i.category?.toLowerCase() === 'fertilizers');
      if (fertilizers.length > 0) suggestions.push('Show all fertilizers');
      
      const seeds = inventory.filter(i => i.category?.toLowerCase() === 'seeds');
      if (seeds.length > 0) suggestions.push('Show all seeds');
    }
    
    return suggestions.slice(0, 6); // Limit to 6 suggestions
  } catch (error) {
    console.error('Error getting browse suggestions:', error);
    return ['Show all machinery', 'Show all inventory'];
  }
};

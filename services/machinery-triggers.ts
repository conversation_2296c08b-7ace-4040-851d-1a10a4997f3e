import AsyncStorage from '@react-native-async-storage/async-storage';
import { Machinery, updateMachineryStatus, getMachineryByFarm, addMaintenanceRecord } from './machinery-service';
import { createNotification } from './notification-service';

export interface MachineryTrigger {
  id: string;
  type: 'maintenance_due' | 'fuel_low' | 'status_change' | 'usage_threshold' | 'overdue_return';
  machineryId: string;
  farmId: string;
  condition: any;
  action: 'notify' | 'status_change' | 'schedule_maintenance' | 'alert_admin';
  isActive: boolean;
  lastTriggered?: string;
  createdAt: string;
}

export interface MachineryEvent {
  type: 'status_changed' | 'fuel_updated' | 'maintenance_completed' | 'usage_updated' | 'request_approved' | 'returned';
  machineryId: string;
  farmId: string;
  data: any;
  timestamp: string;
}

export interface MachineryNotification {
  id: string;
  type: 'warning' | 'alert' | 'info' | 'critical';
  title: string;
  message: string;
  machineryId: string;
  farmId: string;
  isRead: boolean;
  createdAt: string;
}

class MachineryTriggerService {
  private triggers: MachineryTrigger[] = [];
  private notifications: MachineryNotification[] = [];
  private eventListeners: { [key: string]: ((event: MachineryEvent) => void)[] } = {};
  private isInitialized = false;

  async initialize() {
    if (this.isInitialized) return;
    
    try {
      // Load triggers from storage
      const storedTriggers = await AsyncStorage.getItem('machinery_triggers');
      if (storedTriggers) {
        this.triggers = JSON.parse(storedTriggers);
      }

      // Load notifications from storage
      const storedNotifications = await AsyncStorage.getItem('machinery_notifications');
      if (storedNotifications) {
        this.notifications = JSON.parse(storedNotifications);
      }

      // Set up default triggers
      await this.setupDefaultTriggers();
      
      // Start periodic checks
      this.startPeriodicChecks();
      
      this.isInitialized = true;
      console.log('Machinery trigger service initialized');
    } catch (error) {
      console.error('Error initializing machinery trigger service:', error);
    }
  }

  // Event System
  on(eventType: string, callback: (event: MachineryEvent) => void) {
    if (!this.eventListeners[eventType]) {
      this.eventListeners[eventType] = [];
    }
    this.eventListeners[eventType].push(callback);
  }

  off(eventType: string, callback: (event: MachineryEvent) => void) {
    if (this.eventListeners[eventType]) {
      this.eventListeners[eventType] = this.eventListeners[eventType].filter(cb => cb !== callback);
    }
  }

  emit(event: MachineryEvent) {
    console.log('Machinery event emitted:', event.type, event.machineryId);
    
    // Trigger event listeners
    if (this.eventListeners[event.type]) {
      this.eventListeners[event.type].forEach(callback => {
        try {
          callback(event);
        } catch (error) {
          console.error('Error in event listener:', error);
        }
      });
    }

    // Process triggers
    this.processTriggers(event);
  }

  // Trigger Management
  async addTrigger(trigger: Omit<MachineryTrigger, 'id' | 'createdAt'>): Promise<MachineryTrigger> {
    const newTrigger: MachineryTrigger = {
      ...trigger,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
    };

    this.triggers.push(newTrigger);
    await this.saveTriggers();
    return newTrigger;
  }

  async removeTrigger(triggerId: string): Promise<void> {
    this.triggers = this.triggers.filter(t => t.id !== triggerId);
    await this.saveTriggers();
  }

  async updateTrigger(triggerId: string, updates: Partial<MachineryTrigger>): Promise<void> {
    const triggerIndex = this.triggers.findIndex(t => t.id === triggerId);
    if (triggerIndex !== -1) {
      this.triggers[triggerIndex] = { ...this.triggers[triggerIndex], ...updates };
      await this.saveTriggers();
    }
  }

  getTriggers(farmId?: string): MachineryTrigger[] {
    if (farmId) {
      return this.triggers.filter(t => t.farmId === farmId);
    }
    return this.triggers;
  }

  // Setup Default Triggers
  private async setupDefaultTriggers() {
    // Only add default triggers if none exist
    if (this.triggers.length === 0) {
      const defaultTriggers = [
        {
          type: 'maintenance_due' as const,
          machineryId: '*', // Applies to all machinery
          farmId: '*', // Applies to all farms
          condition: { daysBeforeDue: 7 },
          action: 'notify' as const,
          isActive: true,
        },
        {
          type: 'fuel_low' as const,
          machineryId: '*',
          farmId: '*',
          condition: { threshold: 20 }, // 20% fuel remaining
          action: 'notify' as const,
          isActive: true,
        },
        {
          type: 'usage_threshold' as const,
          machineryId: '*',
          farmId: '*',
          condition: { hoursThreshold: 50 }, // 50 hours since last maintenance
          action: 'notify' as const,
          isActive: true,
        },
      ];

      for (const trigger of defaultTriggers) {
        await this.addTrigger(trigger);
      }
    }
  }

  // Process Triggers
  private async processTriggers(event: MachineryEvent) {
    const relevantTriggers = this.triggers.filter(trigger => 
      trigger.isActive && 
      (trigger.machineryId === '*' || trigger.machineryId === event.machineryId) &&
      (trigger.farmId === '*' || trigger.farmId === event.farmId)
    );

    for (const trigger of relevantTriggers) {
      try {
        await this.evaluateTrigger(trigger, event);
      } catch (error) {
        console.error('Error processing trigger:', trigger.id, error);
      }
    }
  }

  private async evaluateTrigger(trigger: MachineryTrigger, event: MachineryEvent) {
    let shouldTrigger = false;

    switch (trigger.type) {
      case 'status_change':
        shouldTrigger = event.type === 'status_changed';
        break;
      case 'fuel_low':
        shouldTrigger = event.type === 'fuel_updated' && this.checkFuelLowCondition(trigger, event);
        break;
      case 'maintenance_due':
        shouldTrigger = this.checkMaintenanceDueCondition(trigger, event);
        break;
      case 'usage_threshold':
        shouldTrigger = event.type === 'usage_updated' && this.checkUsageThresholdCondition(trigger, event);
        break;
      case 'overdue_return':
        shouldTrigger = this.checkOverdueReturnCondition(trigger, event);
        break;
    }

    if (shouldTrigger) {
      await this.executeTriggerAction(trigger, event);
      
      // Update last triggered time
      trigger.lastTriggered = new Date().toISOString();
      await this.saveTriggers();
    }
  }

  // Condition Checkers
  private checkFuelLowCondition(trigger: MachineryTrigger, event: MachineryEvent): boolean {
    const { currentFuelLevel, fuelCapacity } = event.data;
    const threshold = trigger.condition.threshold || 20;
    const fuelPercentage = (currentFuelLevel / fuelCapacity) * 100;
    return fuelPercentage <= threshold;
  }

  private checkMaintenanceDueCondition(trigger: MachineryTrigger, event: MachineryEvent): boolean {
    const { nextMaintenanceDate } = event.data;
    const daysBeforeDue = trigger.condition.daysBeforeDue || 7;
    const dueDate = new Date(nextMaintenanceDate);
    const warningDate = new Date();
    warningDate.setDate(warningDate.getDate() + daysBeforeDue);
    return dueDate <= warningDate;
  }

  private checkUsageThresholdCondition(trigger: MachineryTrigger, event: MachineryEvent): boolean {
    const { hoursSinceLastMaintenance } = event.data;
    const threshold = trigger.condition.hoursThreshold || 50;
    return hoursSinceLastMaintenance >= threshold;
  }

  private checkOverdueReturnCondition(trigger: MachineryTrigger, event: MachineryEvent): boolean {
    const { expectedReturnDate } = event.data;
    if (!expectedReturnDate) return false;
    const returnDate = new Date(expectedReturnDate);
    const now = new Date();
    return now > returnDate;
  }

  // Action Executors
  private async executeTriggerAction(trigger: MachineryTrigger, event: MachineryEvent) {
    console.log('Executing trigger action:', trigger.action, 'for machinery:', event.machineryId);

    switch (trigger.action) {
      case 'notify':
        await this.createNotification(trigger, event);
        break;
      case 'status_change':
        await this.changeStatus(trigger, event);
        break;
      case 'schedule_maintenance':
        await this.scheduleMaintenance(trigger, event);
        break;
      case 'alert_admin':
        await this.alertAdmin(trigger, event);
        break;
    }
  }

  private async createNotification(trigger: MachineryTrigger, event: MachineryEvent) {
    let title = '';
    let message = '';
    let type: 'warning' | 'alert' | 'info' | 'critical' = 'info';

    switch (trigger.type) {
      case 'fuel_low':
        title = 'Low Fuel Alert';
        message = `Machinery ${event.data.machineryName || event.machineryId} has low fuel (${Math.round((event.data.currentFuelLevel / event.data.fuelCapacity) * 100)}%)`;
        type = 'warning';
        break;
      case 'maintenance_due':
        title = 'Maintenance Due';
        message = `Machinery ${event.data.machineryName || event.machineryId} is due for maintenance`;
        type = 'alert';
        break;
      case 'usage_threshold':
        title = 'Usage Threshold Reached';
        message = `Machinery ${event.data.machineryName || event.machineryId} has reached usage threshold`;
        type = 'warning';
        break;
      case 'overdue_return':
        title = 'Overdue Return';
        message = `Machinery ${event.data.machineryName || event.machineryId} is overdue for return`;
        type = 'critical';
        break;
      case 'status_change':
        title = 'Status Changed';
        message = `Machinery ${event.data.machineryName || event.machineryId} status changed to ${event.data.newStatus}`;
        type = 'info';
        break;
    }

    const notification: MachineryNotification = {
      id: Date.now().toString(),
      type,
      title,
      message,
      machineryId: event.machineryId,
      farmId: event.farmId,
      isRead: false,
      createdAt: new Date().toISOString(),
    };

    this.notifications.push(notification);
    await this.saveNotifications();

    // Also create a system notification if available
    try {
      await createNotification({
        title,
        message,
        type: 'machinery',
        farmId: event.farmId,
        relatedId: event.machineryId,
      });
    } catch (error) {
      console.error('Error creating system notification:', error);
    }
  }

  private async changeStatus(trigger: MachineryTrigger, event: MachineryEvent) {
    // Implement automatic status changes based on conditions
    if (trigger.type === 'maintenance_due') {
      await updateMachineryStatus(event.machineryId, event.farmId, 'maintenance');
    }
  }

  private async scheduleMaintenance(trigger: MachineryTrigger, event: MachineryEvent) {
    // Implement automatic maintenance scheduling
    try {
      await addMaintenanceRecord({
        machineryId: event.machineryId,
        type: 'scheduled',
        description: 'Automatically scheduled maintenance due to trigger',
        performedBy: 'system',
        performedAt: new Date().toISOString(),
        cost: 0,
        odometerReading: event.data.odometerReading || 0,
        nextMaintenanceDue: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
        notes: 'Scheduled by automatic trigger system',
      }, event.farmId);
    } catch (error) {
      console.error('Error scheduling maintenance:', error);
    }
  }

  private async alertAdmin(trigger: MachineryTrigger, event: MachineryEvent) {
    // Send high-priority alert to admins
    await this.createNotification(trigger, event);
    
    // Could also send email, push notification, etc.
    console.log('Admin alert sent for machinery:', event.machineryId);
  }

  // Periodic Checks
  private startPeriodicChecks() {
    // Check every hour for maintenance due, fuel levels, etc.
    setInterval(async () => {
      await this.performPeriodicChecks();
    }, 60 * 60 * 1000); // 1 hour

    // Initial check
    setTimeout(() => {
      this.performPeriodicChecks();
    }, 5000); // 5 seconds after initialization
  }

  private async performPeriodicChecks() {
    try {
      console.log('Performing periodic machinery checks...');
      
      // Get all farms and their machinery
      // Note: This would need to be adapted based on your farm service
      // For now, we'll check triggers that need periodic evaluation
      
      const now = new Date();
      
      // Check for maintenance due
      await this.checkMaintenanceDue();
      
      // Check for overdue returns
      await this.checkOverdueReturns();
      
      // Check for fuel levels (if we have cached data)
      await this.checkFuelLevels();
      
      console.log('Periodic checks completed');
    } catch (error) {
      console.error('Error in periodic checks:', error);
    }
  }

  private async checkMaintenanceDue() {
    // This would need to be implemented based on your data access patterns
    // For now, we'll emit events for machinery that might be due for maintenance
    console.log('Checking maintenance due dates...');
  }

  private async checkOverdueReturns() {
    // Check for machinery that should have been returned
    console.log('Checking overdue returns...');
  }

  private async checkFuelLevels() {
    // Check cached fuel levels and emit events if low
    console.log('Checking fuel levels...');
  }

  // Notification Management
  getNotifications(farmId?: string): MachineryNotification[] {
    let notifications = this.notifications;
    if (farmId) {
      notifications = notifications.filter(n => n.farmId === farmId);
    }
    return notifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  getUnreadNotifications(farmId?: string): MachineryNotification[] {
    return this.getNotifications(farmId).filter(n => !n.isRead);
  }

  async markNotificationAsRead(notificationId: string): Promise<void> {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.isRead = true;
      await this.saveNotifications();
    }
  }

  async markAllNotificationsAsRead(farmId?: string): Promise<void> {
    let updated = false;
    this.notifications.forEach(notification => {
      if (!notification.isRead && (!farmId || notification.farmId === farmId)) {
        notification.isRead = true;
        updated = true;
      }
    });
    
    if (updated) {
      await this.saveNotifications();
    }
  }

  async deleteNotification(notificationId: string): Promise<void> {
    this.notifications = this.notifications.filter(n => n.id !== notificationId);
    await this.saveNotifications();
  }

  async clearOldNotifications(daysOld: number = 30): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);
    
    this.notifications = this.notifications.filter(n => 
      new Date(n.createdAt) > cutoffDate
    );
    
    await this.saveNotifications();
  }

  // Storage
  private async saveTriggers(): Promise<void> {
    try {
      await AsyncStorage.setItem('machinery_triggers', JSON.stringify(this.triggers));
    } catch (error) {
      console.error('Error saving triggers:', error);
    }
  }

  private async saveNotifications(): Promise<void> {
    try {
      await AsyncStorage.setItem('machinery_notifications', JSON.stringify(this.notifications));
    } catch (error) {
      console.error('Error saving notifications:', error);
    }
  }

  // Utility Methods
  async checkMachineryHealth(machinery: Machinery): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    // Check fuel level
    const fuelPercentage = (machinery.currentFuelLevel / machinery.fuelCapacity) * 100;
    if (fuelPercentage < 10) {
      issues.push('Critical fuel level');
      recommendations.push('Refuel immediately');
      status = 'critical';
    } else if (fuelPercentage < 25) {
      issues.push('Low fuel level');
      recommendations.push('Schedule refueling soon');
      if (status === 'healthy') status = 'warning';
    }

    // Check maintenance due
    const nextMaintenanceDate = new Date(machinery.nextMaintenanceDate);
    const now = new Date();
    const daysUntilMaintenance = Math.ceil((nextMaintenanceDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysUntilMaintenance < 0) {
      issues.push('Maintenance overdue');
      recommendations.push('Schedule maintenance immediately');
      status = 'critical';
    } else if (daysUntilMaintenance < 7) {
      issues.push('Maintenance due soon');
      recommendations.push('Schedule maintenance within a week');
      if (status === 'healthy') status = 'warning';
    }

    // Check status
    if (machinery.status === 'malfunction') {
      issues.push('Machinery malfunction reported');
      recommendations.push('Inspect and repair before use');
      status = 'critical';
    } else if (machinery.status === 'maintenance') {
      issues.push('Currently under maintenance');
      recommendations.push('Complete maintenance before use');
      if (status === 'healthy') status = 'warning';
    }

    return { status, issues, recommendations };
  }

  // Analytics
  getTriggerStats(farmId?: string): {
    totalTriggers: number;
    activeTriggers: number;
    triggersToday: number;
    notificationsToday: number;
    criticalNotifications: number;
  } {
    const triggers = this.getTriggers(farmId);
    const notifications = this.getNotifications(farmId);
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const triggersToday = triggers.filter(t => 
      t.lastTriggered && new Date(t.lastTriggered) >= today
    ).length;
    
    const notificationsToday = notifications.filter(n => 
      new Date(n.createdAt) >= today
    ).length;
    
    const criticalNotifications = notifications.filter(n => 
      n.type === 'critical' && !n.isRead
    ).length;

    return {
      totalTriggers: triggers.length,
      activeTriggers: triggers.filter(t => t.isActive).length,
      triggersToday,
      notificationsToday,
      criticalNotifications,
    };
  }
}

// Create singleton instance
export const machineryTriggerService = new MachineryTriggerService();

// Helper functions for easy integration
export const initializeMachineryTriggers = () => machineryTriggerService.initialize();

export const emitMachineryEvent = (event: MachineryEvent) => machineryTriggerService.emit(event);

export const getMachineryNotifications = (farmId?: string) => machineryTriggerService.getNotifications(farmId);

export const getUnreadMachineryNotifications = (farmId?: string) => machineryTriggerService.getUnreadNotifications(farmId);

export const markMachineryNotificationAsRead = (notificationId: string) => machineryTriggerService.markNotificationAsRead(notificationId);

export const checkMachineryHealth = (machinery: Machinery) => machineryTriggerService.checkMachineryHealth(machinery);

export const getMachineryTriggerStats = (farmId?: string) => machineryTriggerService.getTriggerStats(farmId);
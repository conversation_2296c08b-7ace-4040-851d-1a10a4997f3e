import { FormField, GuidedFormData } from '@/components/GuidedFormMessage';
import { ImageAnalysisResult } from './image-analysis-service';
import { getUserPermissions } from './ai-assistant-service';

// Define form field configurations for inventory
export const getInventoryFormFields = (analysisResult: ImageAnalysisResult, userRole: string): FormField[] => {
  const permissions = getUserPermissions(userRole);
  const extractedData = analysisResult.extractedData as any;
  
  const fields: FormField[] = [
    {
      id: 'name',
      label: 'Item Name',
      type: 'text',
      required: true,
      value: extractedData?.name || '',
      placeholder: 'Enter item name',
    },
    {
      id: 'category',
      label: 'Category',
      type: 'select',
      required: true,
      value: extractedData?.category || '',
      options: ['fertilizers', 'seeds', 'pesticides', 'tools', 'equipment', 'feed', 'medication', 'fuel', 'vaccination', 'other'],
    },
    {
      id: 'quantity',
      label: 'Quantity',
      type: 'number',
      required: true,
      value: extractedData?.estimatedQuantity?.toString() || '',
      placeholder: 'Enter quantity',
    },
    {
      id: 'unit',
      label: 'Unit',
      type: 'select',
      required: true,
      value: extractedData?.unit || '',
      options: ['bags', 'kg', 'liters', 'tons', 'pieces', 'bottles', 'boxes', 'packets'],
    },
    {
      id: 'unitPrice',
      label: 'Unit Price',
      type: 'number',
      required: false,
      value: extractedData?.unitPrice?.toString() || '',
      placeholder: 'Enter price per unit',
      prefix: 'Rs',
    },
    {
      id: 'supplier',
      label: 'Supplier',
      type: 'text',
      required: false,
      value: extractedData?.supplier || '',
      placeholder: 'Enter supplier name',
    },
    {
      id: 'brand',
      label: 'Brand',
      type: 'text',
      required: false,
      value: extractedData?.brand || '',
      placeholder: 'Enter brand name',
    },
    {
      id: 'expiryDate',
      label: 'Expiry Date',
      type: 'date',
      required: false,
      value: extractedData?.expiryDate || '',
      placeholder: 'DD/MM/YYYY',
    },
    {
      id: 'minStockLevel',
      label: 'Minimum Stock Level',
      type: 'number',
      required: false,
      value: extractedData?.minStockLevel?.toString() || '10',
      placeholder: 'Enter minimum stock level',
    },
    {
      id: 'location',
      label: 'Storage Location',
      type: 'text',
      required: false,
      value: extractedData?.location || '',
      placeholder: 'Enter storage location',
    },
  ];

  // Filter fields based on permissions
  if (!permissions.canCreateInventory) {
    return [];
  }

  return fields;
};

// Define form field configurations for machinery
export const getMachineryFormFields = (analysisResult: ImageAnalysisResult, userRole: string): FormField[] => {
  const permissions = getUserPermissions(userRole);
  const extractedData = analysisResult.extractedData as any;
  
  const fields: FormField[] = [
    {
      id: 'name',
      label: 'Machinery Name',
      type: 'text',
      required: true,
      value: extractedData?.name || '',
      placeholder: 'Enter machinery name',
    },
    {
      id: 'type',
      label: 'Machinery Type',
      type: 'select',
      required: true,
      value: extractedData?.type || '',
      options: ['tractor', 'harvester', 'planter', 'sprayer', 'cultivator', 'car', 'jeep', 'motorbike', 'bicycle', 'other'],
    },
    {
      id: 'brand',
      label: 'Brand',
      type: 'text',
      required: false,
      value: extractedData?.brand || '',
      placeholder: 'Enter brand name',
    },
    {
      id: 'model',
      label: 'Model',
      type: 'text',
      required: false,
      value: extractedData?.model || '',
      placeholder: 'Enter model',
    },
    {
      id: 'year',
      label: 'Year of Manufacture',
      type: 'number',
      required: false,
      value: extractedData?.estimatedYear?.toString() || new Date().getFullYear().toString(),
      placeholder: 'Enter year',
    },
    {
      id: 'serialNumber',
      label: 'Serial Number',
      type: 'text',
      required: false,
      value: extractedData?.serialNumber || '',
      placeholder: 'Enter serial number',
    },
    {
      id: 'registrationNumber',
      label: 'Registration Number',
      type: 'text',
      required: false,
      value: extractedData?.registrationNumber || '',
      placeholder: 'Enter registration number',
    },
    {
      id: 'condition',
      label: 'Condition',
      type: 'select',
      required: true,
      value: extractedData?.condition || 'working',
      options: ['working', 'maintenance', 'malfunction', 'excellent', 'good', 'fair', 'poor'],
    },
    {
      id: 'fuelType',
      label: 'Fuel Type',
      type: 'select',
      required: false,
      value: extractedData?.fuelType || 'diesel',
      options: ['diesel', 'gasoline', 'electric', 'hybrid'],
    },
    {
      id: 'purchasePrice',
      label: 'Purchase Price',
      type: 'number',
      required: false,
      value: extractedData?.purchasePrice?.toString() || '',
      placeholder: 'Enter purchase price',
      prefix: 'Rs',
    },
    {
      id: 'supplier',
      label: 'Supplier/Dealer',
      type: 'text',
      required: false,
      value: extractedData?.supplier || '',
      placeholder: 'Enter supplier name',
    },
    {
      id: 'location',
      label: 'Storage Location',
      type: 'text',
      required: false,
      value: extractedData?.location || '',
      placeholder: 'Enter storage location',
    },
  ];

  // Filter fields based on permissions
  if (!permissions.canCreateMachinery) {
    return [];
  }

  return fields;
};

// Create guided form data from analysis result
export const createGuidedFormData = (
  imageUri: string,
  analysisResult: ImageAnalysisResult,
  userRole: string
): GuidedFormData | null => {
  if (analysisResult.type === 'unknown') {
    return null;
  }

  const permissions = getUserPermissions(userRole);
  
  // Check permissions
  if (analysisResult.type === 'inventory' && !permissions.canCreateInventory) {
    return null;
  }
  
  if (analysisResult.type === 'machinery' && !permissions.canCreateMachinery) {
    return null;
  }

  const fields = analysisResult.type === 'inventory' 
    ? getInventoryFormFields(analysisResult, userRole)
    : getMachineryFormFields(analysisResult, userRole);

  if (fields.length === 0) {
    return null;
  }

  return {
    type: analysisResult.type,
    imageUri,
    analysisResult,
    currentStep: 0,
    fields,
    isComplete: false,
  };
};

// Update form field value
export const updateFormField = (
  formData: GuidedFormData,
  fieldId: string,
  value: string
): GuidedFormData => {
  const updatedFields = formData.fields.map(field =>
    field.id === fieldId ? { ...field, value } : field
  );

  return {
    ...formData,
    fields: updatedFields,
  };
};

// Move to next step
export const moveToNextStep = (formData: GuidedFormData): GuidedFormData => {
  const nextStep = Math.min(formData.currentStep + 1, formData.fields.length - 1);
  const isComplete = nextStep === formData.fields.length - 1;

  return {
    ...formData,
    currentStep: nextStep,
    isComplete,
  };
};

// Convert form data to submission format
export const convertFormDataToSubmission = (formData: GuidedFormData) => {
  const data: Record<string, any> = {};
  
  formData.fields.forEach(field => {
    if (field.value) {
      if (field.type === 'number') {
        data[field.id] = parseFloat(field.value);
      } else {
        data[field.id] = field.value;
      }
    }
  });

  // Add image URI
  data.imageUri = formData.imageUri;
  
  // Add analysis metadata
  data.analysisConfidence = formData.analysisResult.confidence;
  data.analysisDescription = formData.analysisResult.description;

  return data;
};

// Validate all required fields
export const validateFormData = (formData: GuidedFormData): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  formData.fields.forEach(field => {
    if (field.required && !field.value.trim()) {
      errors.push(`${field.label} is required`);
    }
    
    if (field.type === 'number' && field.value && isNaN(Number(field.value))) {
      errors.push(`${field.label} must be a valid number`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
  };
};

import * as FileSystem from 'expo-file-system';
import { analyzeImageWithOpenAI, OpenAIAnalysisResult } from './openai-vision-service';

// Define image analysis result types
export interface ImageAnalysisResult {
  type: 'inventory' | 'machinery' | 'unknown';
  confidence: number;
  extractedData: InventoryData | MachineryData | null;
  suggestions: string[];
  description: string;
}

export interface InventoryData {
  name: string;
  category: string;
  estimatedQuantity?: number;
  unit?: string;
  condition?: string;
  notes?: string;
  // Enhanced fields from OpenAI analysis
  unitPrice?: number;
  totalPrice?: number;
  supplier?: string;
  brand?: string;
  model?: string;
  expiryDate?: string;
  batchNumber?: string;
  minStockLevel?: number;
  location?: string;
}

export interface MachineryData {
  name: string;
  type: string;
  model?: string;
  condition?: string;
  estimatedYear?: number;
  notes?: string;
  // Enhanced fields from OpenAI analysis
  brand?: string;
  serialNumber?: string;
  registrationNumber?: string;
  fuelType?: string;
  fuelCapacity?: number;
  currentFuelLevel?: number;
  odometerReading?: number;
  purchasePrice?: number;
  supplier?: string;
  location?: string;
}

// Enhanced image analysis patterns for better detection
const inventoryPatterns = [
  {
    keywords: ['bag', 'sack', 'fertilizer', 'urea', 'dap', 'npk', 'compost', 'manure', 'phosphate'],
    category: 'fertilizers',
    unit: 'bags',
    confidence: 0.90
  },
  {
    keywords: ['seed', 'grain', 'wheat', 'corn', 'rice', 'cotton', 'vegetable seed', 'flower seed'],
    category: 'seeds',
    unit: 'kg',
    confidence: 0.85
  },
  {
    keywords: ['bottle', 'spray', 'pesticide', 'insecticide', 'herbicide', 'fungicide', 'weedicide', 'roundup'],
    category: 'pesticides',
    unit: 'bottles',
    confidence: 0.85
  },
  {
    keywords: ['tool', 'shovel', 'hoe', 'rake', 'spade', 'pruning', 'hand tool', 'garden tool'],
    category: 'tools',
    unit: 'pieces',
    confidence: 0.80
  },
  {
    keywords: ['fuel', 'diesel', 'petrol', 'gasoline', 'oil', 'lubricant', 'fuel container'],
    category: 'fuel',
    unit: 'liters',
    confidence: 0.85
  },
  {
    keywords: ['feed', 'fodder', 'animal feed', 'cattle feed', 'poultry feed', 'hay', 'silage'],
    category: 'feed',
    unit: 'bags',
    confidence: 0.80
  },
  {
    keywords: ['medicine', 'vaccine', 'medication', 'antibiotic', 'veterinary', 'injection', 'tablet'],
    category: 'medication',
    unit: 'bottles',
    confidence: 0.85
  },
  {
    keywords: ['irrigation', 'pipe', 'sprinkler', 'drip', 'hose', 'fitting', 'irrigation supplies'],
    category: 'irrigation',
    unit: 'pieces',
    confidence: 0.75
  },
  {
    keywords: ['safety', 'protective', 'gloves', 'mask', 'helmet', 'boots', 'safety equipment'],
    category: 'safety',
    unit: 'pieces',
    confidence: 0.75
  },
  {
    keywords: ['packaging', 'container', 'storage', 'box', 'label', 'packaging material'],
    category: 'packaging',
    unit: 'pieces',
    confidence: 0.70
  }
];

const machineryPatterns = [
  {
    keywords: ['tractor', 'john deere', 'massey ferguson', 'new holland', 'kubota', 'mahindra'],
    type: 'tractor',
    confidence: 0.95
  },
  {
    keywords: ['harvester', 'combine', 'reaper', 'thresher'],
    type: 'harvester',
    confidence: 0.90
  },
  {
    keywords: ['planter', 'seeder', 'drill', 'sowing machine'],
    type: 'planter',
    confidence: 0.85
  },
  {
    keywords: ['sprayer', 'spray machine', 'boom sprayer', 'mist blower'],
    type: 'sprayer',
    confidence: 0.85
  },
  {
    keywords: ['cultivator', 'plow', 'harrow', 'disc', 'tillage'],
    type: 'cultivator',
    confidence: 0.80
  },
  {
    keywords: ['car', 'sedan', 'hatchback', 'suv', 'honda', 'toyota', 'suzuki'],
    type: 'car',
    confidence: 0.90
  },
  {
    keywords: ['jeep', '4x4', 'off-road', 'pickup truck'],
    type: 'jeep',
    confidence: 0.85
  },
  {
    keywords: ['motorbike', 'motorcycle', 'scooter', 'yamaha', 'honda bike'],
    type: 'motorbike',
    confidence: 0.85
  },
  {
    keywords: ['bicycle', 'cycle', 'pedal bike', 'mountain bike'],
    type: 'bicycle',
    confidence: 0.80
  },
  {
    keywords: ['pump', 'water pump', 'irrigation pump', 'submersible pump'],
    type: 'pump',
    confidence: 0.80
  },
  {
    keywords: ['generator', 'genset', 'power generator', 'diesel generator'],
    type: 'generator',
    confidence: 0.80
  }
];

// Enhanced image analysis using OpenAI Vision API with fallback
export const analyzeImage = async (imageUri: string, userMessage?: string): Promise<ImageAnalysisResult> => {
  try {
    // Get image info
    const imageInfo = await FileSystem.getInfoAsync(imageUri);
    if (!imageInfo.exists) {
      throw new Error('Image file not found');
    }

    // Try OpenAI Vision API first
    try {
      console.log('Attempting OpenAI Vision analysis...');
      const openAIResult = await analyzeImageWithOpenAI(imageUri, userMessage);

      // Convert OpenAI result to our format
      return convertOpenAIResult(openAIResult);
    } catch (openAIError) {
      console.warn('OpenAI Vision analysis failed, falling back to basic analysis:', openAIError);

      // Fallback to basic pattern matching
      return await performBasicAnalysis(imageUri, userMessage);
    }
  } catch (error) {
    console.error('Error analyzing image:', error);
    throw new Error('Failed to analyze image');
  }
};

// Convert OpenAI result to our ImageAnalysisResult format
const convertOpenAIResult = (openAIResult: OpenAIAnalysisResult): ImageAnalysisResult => {
  if (openAIResult.type === 'inventory' && openAIResult.extractedData) {
    const data = openAIResult.extractedData as any;
    return {
      type: 'inventory',
      confidence: openAIResult.confidence,
      extractedData: {
        name: data.name,
        category: data.category,
        estimatedQuantity: data.quantity,
        unit: data.unit,
        condition: data.condition,
        notes: data.notes,
        // Additional fields from OpenAI
        unitPrice: data.unitPrice,
        totalPrice: data.totalPrice,
        supplier: data.supplier,
        brand: data.brand,
        model: data.model,
        expiryDate: data.expiryDate,
        batchNumber: data.batchNumber,
        minStockLevel: data.minStockLevel,
        location: data.location
      } as InventoryData,
      suggestions: openAIResult.suggestions,
      description: openAIResult.description
    };
  } else if (openAIResult.type === 'machinery' && openAIResult.extractedData) {
    const data = openAIResult.extractedData as any;
    return {
      type: 'machinery',
      confidence: openAIResult.confidence,
      extractedData: {
        name: data.name,
        type: data.type,
        model: data.model,
        condition: data.condition,
        estimatedYear: data.year,
        notes: data.notes,
        // Additional fields from OpenAI
        brand: data.brand,
        serialNumber: data.serialNumber,
        registrationNumber: data.registrationNumber,
        fuelType: data.fuelType,
        fuelCapacity: data.fuelCapacity,
        currentFuelLevel: data.currentFuelLevel,
        odometerReading: data.odometerReading,
        purchasePrice: data.purchasePrice,
        supplier: data.supplier,
        location: data.location
      } as MachineryData,
      suggestions: openAIResult.suggestions,
      description: openAIResult.description
    };
  } else {
    return {
      type: openAIResult.type,
      confidence: openAIResult.confidence,
      extractedData: null,
      suggestions: openAIResult.suggestions,
      description: openAIResult.description
    };
  }
};

// Fallback basic analysis function
const performBasicAnalysis = async (imageUri: string, userMessage?: string): Promise<ImageAnalysisResult> => {
  // Extract filename for pattern matching
  const filename = imageUri.split('/').pop()?.toLowerCase() || '';
  const messageText = (userMessage || '').toLowerCase();
  const combinedText = `${filename} ${messageText}`;

  // Simulate processing delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Try to match machinery patterns first
  for (const pattern of machineryPatterns) {
    const matchCount = pattern.keywords.filter(keyword =>
      combinedText.includes(keyword)
    ).length;

    if (matchCount > 0) {
      const confidence = Math.min(pattern.confidence + (matchCount * 0.1), 0.95);

      return {
        type: 'machinery',
        confidence,
        extractedData: {
          name: extractMachineryName(combinedText, pattern.type),
          type: pattern.type,
          condition: 'working',
          notes: `Detected from image analysis${userMessage ? ` with message: "${userMessage}"` : ''}`
        } as MachineryData,
        suggestions: [
          `Add ${pattern.type} to machinery`,
          `Set maintenance schedule`,
          `Update machinery status`
        ],
        description: `Detected ${pattern.type} in the image with ${Math.round(confidence * 100)}% confidence`
      };
    }
  }

  // Try to match inventory patterns
  for (const pattern of inventoryPatterns) {
    const matchCount = pattern.keywords.filter(keyword =>
      combinedText.includes(keyword)
    ).length;

    if (matchCount > 0) {
      const confidence = Math.min(pattern.confidence + (matchCount * 0.1), 0.95);
      const estimatedQuantity = extractQuantityFromText(combinedText);

      return {
        type: 'inventory',
        confidence,
        extractedData: {
          name: extractInventoryName(combinedText, pattern.category),
          category: pattern.category,
          estimatedQuantity: estimatedQuantity || 1,
          unit: pattern.unit,
          condition: 'good',
          notes: `Detected from image analysis${userMessage ? ` with message: "${userMessage}"` : ''}`
        } as InventoryData,
        suggestions: [
          `Add ${pattern.category} to inventory`,
          `Set minimum stock level`,
          `Update quantity if needed`
        ],
        description: `Detected ${pattern.category} in the image with ${Math.round(confidence * 100)}% confidence`
      };
    }
  }

  // If no specific patterns match, try general analysis
  if (combinedText.includes('machine') || combinedText.includes('equipment')) {
    return {
      type: 'machinery',
      confidence: 0.60,
      extractedData: {
        name: 'Unknown Equipment',
        type: 'other',
        condition: 'unknown',
        notes: `General equipment detected from image${userMessage ? ` with message: "${userMessage}"` : ''}`
      } as MachineryData,
      suggestions: [
        'Specify equipment type',
        'Add equipment details',
        'Set maintenance schedule'
      ],
      description: 'Detected equipment in the image, but could not identify specific type'
    };
  }

  // Default fallback
  return {
    type: 'unknown',
    confidence: 0.30,
    extractedData: null,
    suggestions: [
      'Try taking a clearer photo',
      'Include descriptive text with the image',
      'Focus on the main subject'
    ],
    description: 'Could not identify specific inventory or machinery in the image. Please provide more details or try a different angle.'
  };
};

// Helper functions
const extractMachineryName = (text: string, type: string): string => {
  // Look for common brand names
  const brands = ['john deere', 'massey ferguson', 'new holland', 'case', 'kubota', 'mahindra'];
  const foundBrand = brands.find(brand => text.includes(brand));
  
  if (foundBrand) {
    return `${foundBrand.split(' ').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')} ${type.charAt(0).toUpperCase() + type.slice(1)}`;
  }
  
  return `${type.charAt(0).toUpperCase() + type.slice(1)}`;
};

const extractInventoryName = (text: string, category: string): string => {
  // Look for specific product names
  const products: Record<string, string[]> = {
    fertilizers: ['urea', 'dap', 'npk', 'phosphate'],
    seeds: ['wheat', 'corn', 'rice', 'cotton'],
    pesticides: ['roundup', 'atrazine', 'glyphosate'],
    tools: ['shovel', 'hoe', 'rake', 'spade']
  };
  
  const categoryProducts = products[category] || [];
  const foundProduct = categoryProducts.find(product => text.includes(product));
  
  if (foundProduct) {
    return foundProduct.charAt(0).toUpperCase() + foundProduct.slice(1);
  }
  
  return category.charAt(0).toUpperCase() + category.slice(1);
};

const extractQuantityFromText = (text: string): number | null => {
  // Look for numbers in the text
  const numberMatch = text.match(/\d+/);
  if (numberMatch) {
    const number = parseInt(numberMatch[0], 10);
    return number > 0 && number < 10000 ? number : null;
  }
  return null;
};

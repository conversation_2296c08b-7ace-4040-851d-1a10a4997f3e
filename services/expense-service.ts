import { 
  collection, 
  query, 
  where, 
  getDocs, 
  doc, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  orderBy, 
  limit as firestoreLimit, 
  onSnapshot, 
  Timestamp,
  startAfter,
  endBefore,
  startAt,
  endAt
} from "firebase/firestore";
import { getFirestore } from "firebase/firestore";

const db = getFirestore();

// Define Expense type
export interface Expense {
  id: string;
  farmId: string;
  farmName: string;
  type: 'farm' | 'user'; // farm expense or user-based expense
  category: 'inventory' | 'machinery' | 'fuel' | 'maintenance' | 'labor' | 'utilities' | 'transport' | 'other';
  amount: number;
  description: string;
  date: string;
  userId?: string; // For user-based expenses
  userName?: string; // For user-based expenses
  userRole?: string; // For user-based expenses
  relatedItemId?: string; // Related inventory or machinery item
  relatedItemName?: string; // Related inventory or machinery item name
  relatedItemType?: 'inventory' | 'machinery'; // Type of related item
  allocationId?: string; // Related allocation if expense is from allocation
  requestId?: string; // Related request if expense is from request approval
  notes?: string;
  attachments?: string[]; // URLs to receipts or documents
  createdBy: string;
  createdByName: string;
  createdByRole: string;
  createdAt: string;
  updatedAt: string;
}

// Define ExpenseFilter type
export interface ExpenseFilter {
  type?: 'farm' | 'user' | 'all';
  category?: string;
  userId?: string;
  dateFrom?: string;
  dateTo?: string;
  farmId?: string;
}

// Define ExpenseSummary type
export interface ExpenseSummary {
  totalExpenses: number; // Total of all inventory and machinery prices (farm expenses)
  farmExpenses: number; // Total price of all user expenses
  userExpenses: number; // Total of all user expenses
  monthlyExpenses: number; // Total of all user expenses in current month
  yearlyExpenses: number;
  categoryBreakdown: { [category: string]: number };
  userBreakdown: { [userId: string]: { amount: number; userName: string; userRole: string } };
  monthlyTrend: { month: string; amount: number }[];
  topCategories: { category: string; amount: number; percentage: number }[];
  topUsers: { userId: string; userName: string; amount: number; percentage: number }[];
}

// Create expense
export const createExpense = async (expenseData: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    const now = new Date().toISOString();
    const expense: Omit<Expense, 'id'> = {
      ...expenseData,
      createdAt: now,
      updatedAt: now,
    };

    const docRef = await addDoc(collection(db, 'expenses'), expense);
    console.log('Expense created with ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('Error creating expense:', error);
    throw error;
  }
};

// Get expense by ID
export const getExpenseById = async (expenseId: string): Promise<Expense | null> => {
  try {
    const docRef = doc(db, 'expenses', expenseId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as Expense;
    } else {
      console.log('No expense found with ID:', expenseId);
      return null;
    }
  } catch (error) {
    console.error('Error getting expense:', error);
    throw error;
  }
};

// Get expenses by farm with filtering
export const getExpensesByFarm = async (
  farmId: string, 
  filter?: ExpenseFilter,
  limit?: number,
  lastDoc?: any
): Promise<{ expenses: Expense[]; lastDoc: any }> => {
  try {
    let q = query(
      collection(db, 'expenses'),
      where('farmId', '==', farmId),
      orderBy('date', 'desc')
    );

    // Apply filters
    if (filter?.type && filter.type !== 'all') {
      q = query(q, where('type', '==', filter.type));
    }

    if (filter?.category) {
      q = query(q, where('category', '==', filter.category));
    }

    if (filter?.userId) {
      q = query(q, where('userId', '==', filter.userId));
    }

    if (filter?.dateFrom) {
      q = query(q, where('date', '>=', filter.dateFrom));
    }

    if (filter?.dateTo) {
      q = query(q, where('date', '<=', filter.dateTo));
    }

    // Add pagination
    if (lastDoc) {
      q = query(q, startAfter(lastDoc));
    }

    if (limit) {
      q = query(q, firestoreLimit(limit));
    }

    const querySnapshot = await getDocs(q);
    const expenses: Expense[] = [];
    let newLastDoc = null;

    querySnapshot.forEach((doc) => {
      expenses.push({ id: doc.id, ...doc.data() } as Expense);
      newLastDoc = doc;
    });

    return { expenses, lastDoc: newLastDoc };
  } catch (error) {
    console.error('Error getting expenses:', error);
    throw error;
  }
};

// Get user expenses (for caretakers to see only their expenses)
export const getUserExpenses = async (
  farmId: string,
  userId: string,
  limit?: number,
  lastDoc?: any
): Promise<{ expenses: Expense[]; lastDoc: any }> => {
  try {
    let q = query(
      collection(db, 'expenses'),
      where('farmId', '==', farmId),
      where('userId', '==', userId),
      orderBy('date', 'desc')
    );

    if (lastDoc) {
      q = query(q, startAfter(lastDoc));
    }

    if (limit) {
      q = query(q, firestoreLimit(limit));
    }

    const querySnapshot = await getDocs(q);
    const expenses: Expense[] = [];
    let newLastDoc = null;

    querySnapshot.forEach((doc) => {
      expenses.push({ id: doc.id, ...doc.data() } as Expense);
      newLastDoc = doc;
    });

    return { expenses, lastDoc: newLastDoc };
  } catch (error) {
    console.error('Error getting user expenses:', error);
    throw error;
  }
};

// Update expense
export const updateExpense = async (expenseId: string, updates: Partial<Expense>): Promise<void> => {
  try {
    const docRef = doc(db, 'expenses', expenseId);
    const updateData = {
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    
    await updateDoc(docRef, updateData);
    console.log('Expense updated successfully');
  } catch (error) {
    console.error('Error updating expense:', error);
    throw error;
  }
};

// Delete expense
export const deleteExpense = async (expenseId: string): Promise<void> => {
  try {
    const docRef = doc(db, 'expenses', expenseId);
    await deleteDoc(docRef);
    console.log('Expense deleted successfully');
  } catch (error) {
    console.error('Error deleting expense:', error);
    throw error;
  }
};

// ===== AUTOMATIC EXPENSE TRACKING FUNCTIONS =====

// Create farm-level expense when inventory/machinery is added
export const createFarmExpenseForNewItem = async (
  farmId: string,
  farmName: string,
  itemType: 'inventory' | 'machinery',
  itemId: string,
  itemName: string,
  unitPrice: number,
  quantity: number,
  createdBy: string,
  createdByName: string,
  createdByRole: string
): Promise<string | null> => {
  try {
    if (!unitPrice || unitPrice <= 0 || !quantity || quantity <= 0) {
      console.log('No expense created: invalid price or quantity');
      return null;
    }

    const totalCost = unitPrice * quantity;
    const expenseData = {
      farmId,
      farmName,
      type: 'farm' as const,
      category: itemType,
      amount: totalCost,
      description: `${itemType === 'inventory' ? 'Inventory' : 'Machinery'} purchase: ${itemName}`,
      date: new Date().toISOString().split('T')[0],
      relatedItemId: itemId,
      relatedItemName: itemName,
      relatedItemType: itemType,
      notes: `Automatic farm expense for new ${itemType} item (${quantity} × Rs. ${unitPrice})`,
      createdBy,
      createdByName,
      createdByRole,
    };

    const expenseId = await createExpense(expenseData);
    console.log(`Farm expense created for ${itemType}:`, expenseId);
    return expenseId;
  } catch (error) {
    console.error(`Error creating farm expense for ${itemType}:`, error);
    return null;
  }
};

// Create user expense when request is approved
export const createUserExpenseForApprovedRequest = async (
  farmId: string,
  farmName: string,
  requestId: string,
  userId: string,
  userName: string,
  userRole: string,
  itemType: 'inventory' | 'machinery',
  itemId: string,
  itemName: string,
  quantity: number,
  unitPrice: number,
  approvedBy: string,
  approvedByName: string,
  additionalCost?: number, // For fuel/maintenance costs
  additionalDescription?: string
): Promise<string | null> => {
  try {
    if (!quantity || quantity <= 0) {
      console.log('No user expense created: invalid quantity');
      return null;
    }

    // Allow zero-cost expenses for tracking purposes
    if (!unitPrice || unitPrice < 0) {
      unitPrice = 0;
    }

    const baseCost = unitPrice * quantity;
    const totalCost = baseCost + (additionalCost || 0);

    let description = `${itemType === 'inventory' ? 'Inventory' : 'Machinery'} allocation: ${itemName}`;
    if (additionalDescription) {
      description += ` (${additionalDescription})`;
    }

    const expenseData = {
      farmId,
      farmName,
      type: 'user' as const,
      category: itemType,
      amount: totalCost,
      description,
      date: new Date().toISOString().split('T')[0],
      userId,
      userName,
      userRole,
      relatedItemId: itemId,
      relatedItemName: itemName,
      relatedItemType: itemType,
      requestId,
      notes: `Automatic user expense for approved request (${quantity} × Rs. ${unitPrice}${additionalCost ? ` + Rs. ${additionalCost}` : ''})`,
      createdBy: approvedBy,
      createdByName: approvedByName,
      createdByRole: 'admin', // Assuming admin approves
    };

    const expenseId = await createExpense(expenseData);
    console.log(`User expense created for approved request:`, expenseId);
    return expenseId;
  } catch (error) {
    console.error('Error creating user expense for approved request:', error);
    return null;
  }
};

// Adjust user expense when return is approved
export const adjustUserExpenseForReturn = async (
  farmId: string,
  originalExpenseId: string,
  returnedQuantity: number,
  totalQuantity: number,
  returnCondition: 'good' | 'used' | 'damaged',
  reviewedBy: string,
  reviewedByName: string
): Promise<string | null> => {
  try {
    // Get the original expense
    const originalExpense = await getExpenseById(originalExpenseId);
    if (!originalExpense) {
      console.error('Original expense not found');
      return null;
    }

    // Calculate adjustment based on return condition
    let adjustmentPercentage = 1.0; // Full refund for good condition
    if (returnCondition === 'used') {
      adjustmentPercentage = 0.8; // 80% refund for used items
    } else if (returnCondition === 'damaged') {
      adjustmentPercentage = 0.5; // 50% refund for damaged items
    }

    const returnRatio = returnedQuantity / totalQuantity;
    const adjustmentAmount = originalExpense.amount * returnRatio * adjustmentPercentage;

    // Create adjustment expense (negative amount)
    const adjustmentExpenseData = {
      farmId,
      farmName: originalExpense.farmName,
      type: 'user' as const,
      category: originalExpense.category,
      amount: -adjustmentAmount,
      description: `Return adjustment for: ${originalExpense.description}`,
      date: new Date().toISOString().split('T')[0],
      userId: originalExpense.userId!,
      userName: originalExpense.userName!,
      userRole: originalExpense.userRole!,
      relatedItemId: originalExpense.relatedItemId,
      relatedItemName: originalExpense.relatedItemName,
      relatedItemType: originalExpense.relatedItemType,
      notes: `Return adjustment: ${returnedQuantity}/${totalQuantity} items returned in ${returnCondition} condition (${Math.round(adjustmentPercentage * 100)}% refund)`,
      createdBy: reviewedBy,
      createdByName: reviewedByName,
      createdByRole: 'admin',
    };

    const adjustmentExpenseId = await createExpense(adjustmentExpenseData);
    console.log('Return adjustment expense created:', adjustmentExpenseId);
    return adjustmentExpenseId;
  } catch (error) {
    console.error('Error creating return adjustment expense:', error);
    return null;
  }
};

// Create machinery-specific expense (fuel, maintenance, repair)
export const createMachineryExpense = async (
  farmId: string,
  farmName: string,
  requestId: string,
  userId: string,
  userName: string,
  userRole: string,
  machineryId: string,
  machineryName: string,
  expenseType: 'fuel' | 'maintenance',
  amount: number,
  description: string,
  approvedBy: string,
  approvedByName: string
): Promise<string | null> => {
  try {
    if (!amount || amount <= 0) {
      console.log('No machinery expense created: invalid amount');
      return null;
    }

    const expenseData = {
      farmId,
      farmName,
      type: 'user' as const,
      category: expenseType,
      amount,
      description: `Machinery ${expenseType}: ${machineryName} - ${description}`,
      date: new Date().toISOString().split('T')[0],
      userId,
      userName,
      userRole,
      relatedItemId: machineryId,
      relatedItemName: machineryName,
      relatedItemType: 'machinery' as const,
      requestId,
      notes: `Automatic machinery ${expenseType} expense from approved request`,
      createdBy: approvedBy,
      createdByName: approvedByName,
      createdByRole: 'admin',
    };

    const expenseId = await createExpense(expenseData);
    console.log(`Machinery ${expenseType} expense created:`, expenseId);
    return expenseId;
  } catch (error) {
    console.error(`Error creating machinery ${expenseType} expense:`, error);
    return null;
  }
};

// Get expenses by request ID
export const getExpensesByRequestId = async (farmId: string, requestId: string): Promise<Expense[]> => {
  try {
    const q = query(
      collection(db, 'expenses'),
      where('farmId', '==', farmId),
      where('requestId', '==', requestId)
    );

    const querySnapshot = await getDocs(q);
    const expenses: Expense[] = [];

    querySnapshot.forEach((doc) => {
      expenses.push({ id: doc.id, ...doc.data() } as Expense);
    });

    return expenses;
  } catch (error) {
    console.error('Error getting expenses by request ID:', error);
    return [];
  }
};

// Get expense summary for a farm
export const getExpenseSummary = async (
  farmId: string,
  dateFrom?: string,
  dateTo?: string
): Promise<ExpenseSummary> => {
  try {
    let q = query(
      collection(db, 'expenses'),
      where('farmId', '==', farmId)
    );

    if (dateFrom) {
      q = query(q, where('date', '>=', dateFrom));
    }

    if (dateTo) {
      q = query(q, where('date', '<=', dateTo));
    }

    const querySnapshot = await getDocs(q);
    const expenses: Expense[] = [];

    querySnapshot.forEach((doc) => {
      expenses.push({ id: doc.id, ...doc.data() } as Expense);
    });

    // Calculate summary
    const summary: ExpenseSummary = {
      totalExpenses: 0,
      farmExpenses: 0,
      userExpenses: 0,
      monthlyExpenses: 0,
      yearlyExpenses: 0,
      categoryBreakdown: {},
      userBreakdown: {},
      monthlyTrend: [],
      topCategories: [],
      topUsers: [],
    };

    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const monthlyData: { [key: string]: number } = {};

    expenses.forEach((expense) => {
      const expenseDate = new Date(expense.date);
      const expenseMonth = expenseDate.getMonth();
      const expenseYear = expenseDate.getFullYear();
      const monthKey = `${expenseYear}-${expenseMonth.toString().padStart(2, '0')}`;

      // Total expenses = sum of all inventory and machinery prices (farm expenses only)
      if (expense.type === 'farm' && (expense.category === 'inventory' || expense.category === 'machinery')) {
        summary.totalExpenses += expense.amount;
      }

      // Farm expenses = total price of all user expenses
      if (expense.type === 'user') {
        summary.farmExpenses += expense.amount;
      }

      // User expenses = total of all user expenses
      if (expense.type === 'user') {
        summary.userExpenses += expense.amount;
      }

      // Monthly expenses = total of all user expenses in current month
      if (expense.type === 'user' && expenseMonth === currentMonth && expenseYear === currentYear) {
        summary.monthlyExpenses += expense.amount;
      }

      // Yearly expenses (all expenses)
      if (expenseYear === currentYear) {
        summary.yearlyExpenses += expense.amount;
      }

      // Category breakdown
      if (!summary.categoryBreakdown[expense.category]) {
        summary.categoryBreakdown[expense.category] = 0;
      }
      summary.categoryBreakdown[expense.category] += expense.amount;

      // User breakdown (for user expenses)
      if (expense.type === 'user' && expense.userId) {
        if (!summary.userBreakdown[expense.userId]) {
          summary.userBreakdown[expense.userId] = {
            amount: 0,
            userName: expense.userName || 'Unknown',
            userRole: expense.userRole || 'Unknown',
          };
        }
        summary.userBreakdown[expense.userId].amount += expense.amount;
      }

      // Monthly trend
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = 0;
      }
      monthlyData[monthKey] += expense.amount;
    });

    // Convert monthly data to trend array
    summary.monthlyTrend = Object.entries(monthlyData)
      .map(([month, amount]) => ({ month, amount }))
      .sort((a, b) => a.month.localeCompare(b.month));

    // Top categories
    summary.topCategories = Object.entries(summary.categoryBreakdown)
      .map(([category, amount]) => ({
        category,
        amount,
        percentage: (amount / summary.totalExpenses) * 100,
      }))
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 5);

    // Top users
    summary.topUsers = Object.entries(summary.userBreakdown)
      .map(([userId, data]) => ({
        userId,
        userName: data.userName,
        amount: data.amount,
        percentage: (data.amount / summary.userExpenses) * 100,
      }))
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 5);

    return summary;
  } catch (error) {
    console.error('Error getting expense summary:', error);
    throw error;
  }
};

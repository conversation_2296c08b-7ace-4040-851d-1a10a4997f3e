import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { storage } from './firebase-config';
import * as FileSystem from 'expo-file-system';

// Upload image to Firebase Storage
export const uploadImageToStorage = async (
  imageUri: string,
  folder: 'inventory' | 'machinery',
  itemId: string,
  userId: string
): Promise<string> => {
  try {
    // Read the image file
    const response = await fetch(imageUri);
    const blob = await response.blob();
    
    // Create a unique filename
    const timestamp = Date.now();
    const filename = `${folder}/${userId}/${itemId}_${timestamp}.jpg`;
    
    // Create storage reference
    const storageRef = ref(storage, filename);
    
    // Upload the file
    const snapshot = await uploadBytes(storageRef, blob);
    
    // Get the download URL
    const downloadURL = await getDownloadURL(snapshot.ref);
    
    return downloadURL;
  } catch (error) {
    console.error('Error uploading image to storage:', error);
    throw new Error('Failed to upload image');
  }
};

// Upload multiple images
export const uploadMultipleImages = async (
  imageUris: string[],
  folder: 'inventory' | 'machinery',
  itemId: string,
  userId: string
): Promise<string[]> => {
  try {
    const uploadPromises = imageUris.map((uri, index) => 
      uploadImageToStorage(uri, folder, `${itemId}_${index}`, userId)
    );
    
    const downloadURLs = await Promise.all(uploadPromises);
    return downloadURLs;
  } catch (error) {
    console.error('Error uploading multiple images:', error);
    throw new Error('Failed to upload images');
  }
};

// Delete image from Firebase Storage
export const deleteImageFromStorage = async (imageUrl: string): Promise<void> => {
  try {
    // Extract the path from the URL
    const url = new URL(imageUrl);
    const pathMatch = url.pathname.match(/\/o\/(.+)\?/);
    
    if (!pathMatch) {
      throw new Error('Invalid image URL format');
    }
    
    const imagePath = decodeURIComponent(pathMatch[1]);
    const storageRef = ref(storage, imagePath);
    
    await deleteObject(storageRef);
  } catch (error) {
    console.error('Error deleting image from storage:', error);
    // Don't throw error for deletion failures to avoid blocking other operations
  }
};

// Generate unique item ID
export const generateItemId = (): string => {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Compress image before upload (optional)
export const compressImage = async (imageUri: string, quality: number = 0.8): Promise<string> => {
  try {
    // For now, return the original URI
    // In a production app, you might want to use expo-image-manipulator
    // to compress the image before upload
    return imageUri;
  } catch (error) {
    console.error('Error compressing image:', error);
    return imageUri; // Return original if compression fails
  }
};

// Get image info
export const getImageInfo = async (imageUri: string) => {
  try {
    const info = await FileSystem.getInfoAsync(imageUri);
    return info;
  } catch (error) {
    console.error('Error getting image info:', error);
    return null;
  }
};

// Validate image file
export const validateImage = async (imageUri: string): Promise<{ isValid: boolean; error?: string }> => {
  try {
    const info = await getImageInfo(imageUri);
    
    if (!info || !info.exists) {
      return { isValid: false, error: 'Image file does not exist' };
    }
    
    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (info.size && info.size > maxSize) {
      return { isValid: false, error: 'Image file is too large (max 10MB)' };
    }
    
    return { isValid: true };
  } catch (error) {
    console.error('Error validating image:', error);
    return { isValid: false, error: 'Failed to validate image' };
  }
};

// Create thumbnail URL (if using a service that supports it)
export const createThumbnailUrl = (originalUrl: string, size: number = 200): string => {
  // For Firebase Storage, you might use Firebase Extensions like Resize Images
  // For now, return the original URL
  return originalUrl;
};

// Extract filename from URL
export const extractFilenameFromUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const pathMatch = urlObj.pathname.match(/\/([^\/]+)$/);
    return pathMatch ? pathMatch[1] : 'unknown';
  } catch (error) {
    return 'unknown';
  }
};

// Check if URL is a Firebase Storage URL
export const isFirebaseStorageUrl = (url: string): boolean => {
  return url.includes('firebasestorage.googleapis.com') || url.includes('storage.googleapis.com');
};

// Batch upload with progress tracking
export const uploadImagesWithProgress = async (
  imageUris: string[],
  folder: 'inventory' | 'machinery',
  itemId: string,
  userId: string,
  onProgress?: (progress: number) => void
): Promise<string[]> => {
  try {
    const downloadURLs: string[] = [];
    const total = imageUris.length;
    
    for (let i = 0; i < imageUris.length; i++) {
      const uri = imageUris[i];
      const url = await uploadImageToStorage(uri, folder, `${itemId}_${i}`, userId);
      downloadURLs.push(url);
      
      // Report progress
      if (onProgress) {
        const progress = ((i + 1) / total) * 100;
        onProgress(progress);
      }
    }
    
    return downloadURLs;
  } catch (error) {
    console.error('Error uploading images with progress:', error);
    throw new Error('Failed to upload images');
  }
};

// Clean up temporary images
export const cleanupTempImages = async (imageUris: string[]): Promise<void> => {
  try {
    const deletePromises = imageUris.map(async (uri) => {
      try {
        // Only delete if it's a local file
        if (uri.startsWith('file://')) {
          await FileSystem.deleteAsync(uri, { idempotent: true });
        }
      } catch (error) {
        console.warn('Failed to delete temp image:', uri, error);
      }
    });
    
    await Promise.all(deletePromises);
  } catch (error) {
    console.error('Error cleaning up temp images:', error);
  }
};

// Get storage usage info
export const getStorageUsage = async (userId: string): Promise<{ count: number; totalSize: number }> => {
  // This would require additional Firebase functions to implement
  // For now, return default values
  return { count: 0, totalSize: 0 };
};

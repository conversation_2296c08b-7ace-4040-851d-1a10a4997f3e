import { getStorage, ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { Platform } from 'react-native';

// Initialize Firebase Storage
const storage = getStorage();

/**
 * Upload image to Firebase Storage and return download URL
 * @param imageUri - Local file URI from camera/gallery
 * @param folder - Storage folder (e.g., 'users', 'farms', 'inventory', 'machinery', 'notes')
 * @param fileName - Optional custom filename, if not provided, generates unique name
 * @returns Promise<string> - Download URL from Firebase Storage
 */
export const uploadImageToStorage = async (
  imageUri: string,
  folder: string,
  fileName?: string
): Promise<string> => {
  try {
    console.log(`Uploading image to Storage: ${folder}/${fileName || 'auto-generated'}`);
    
    // Generate unique filename if not provided
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 15);
    const finalFileName = fileName || `image_${timestamp}_${randomId}.jpg`;
    
    // Create storage reference
    const storageRef = ref(storage, `${folder}/${finalFileName}`);
    
    // Convert image URI to blob for upload
    const blob = await uriToBlob(imageUri);
    
    // Upload the blob
    console.log('Starting upload...');
    const snapshot = await uploadBytes(storageRef, blob);
    console.log('Upload completed, getting download URL...');
    
    // Get download URL
    const downloadURL = await getDownloadURL(snapshot.ref);
    console.log('Download URL obtained:', downloadURL);
    
    return downloadURL;
  } catch (error) {
    console.error('Error uploading image to Storage:', error);
    throw new Error(`Failed to upload image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Delete image from Firebase Storage
 * @param imageUrl - Full download URL from Firebase Storage
 */
export const deleteImageFromStorage = async (imageUrl: string): Promise<void> => {
  try {
    if (!imageUrl || !imageUrl.includes('firebase')) {
      console.log('Not a Firebase Storage URL, skipping deletion');
      return;
    }
    
    // Extract storage path from download URL
    const storageRef = ref(storage, imageUrl);
    await deleteObject(storageRef);
    console.log('Image deleted from Storage successfully');
  } catch (error) {
    console.error('Error deleting image from Storage:', error);
    // Don't throw error for deletion failures to avoid blocking other operations
  }
};

/**
 * Convert local file URI to Blob for upload
 * @param uri - Local file URI
 * @returns Promise<Blob>
 */
const uriToBlob = async (uri: string): Promise<Blob> => {
  try {
    if (Platform.OS === 'web') {
      // For web platform
      const response = await fetch(uri);
      return await response.blob();
    } else {
      // For mobile platforms (iOS/Android)
      const response = await fetch(uri);
      return await response.blob();
    }
  } catch (error) {
    console.error('Error converting URI to blob:', error);
    throw new Error('Failed to convert image for upload');
  }
};

/**
 * Upload multiple images to Firebase Storage
 * @param imageUris - Array of local file URIs
 * @param folder - Storage folder
 * @returns Promise<string[]> - Array of download URLs
 */
export const uploadMultipleImages = async (
  imageUris: string[],
  folder: string
): Promise<string[]> => {
  try {
    console.log(`Uploading ${imageUris.length} images to ${folder}`);
    
    const uploadPromises = imageUris.map((uri, index) => 
      uploadImageToStorage(uri, folder, `image_${Date.now()}_${index}.jpg`)
    );
    
    const downloadURLs = await Promise.all(uploadPromises);
    console.log(`Successfully uploaded ${downloadURLs.length} images`);
    
    return downloadURLs;
  } catch (error) {
    console.error('Error uploading multiple images:', error);
    throw error;
  }
};

/**
 * Check if a URL is a Firebase Storage URL
 * @param url - URL to check
 * @returns boolean
 */
export const isFirebaseStorageUrl = (url: string): boolean => {
  return url.includes('firebasestorage.googleapis.com') || url.includes('firebase');
};

/**
 * Upload user profile image
 * @param imageUri - Local file URI
 * @param userId - User ID for filename
 * @returns Promise<string> - Download URL
 */
export const uploadUserProfileImage = async (imageUri: string, userId: string): Promise<string> => {
  return uploadImageToStorage(imageUri, 'users', `profile_${userId}.jpg`);
};

/**
 * Upload farm image
 * @param imageUri - Local file URI
 * @param farmId - Farm ID for filename
 * @returns Promise<string> - Download URL
 */
export const uploadFarmImage = async (imageUri: string, farmId: string): Promise<string> => {
  return uploadImageToStorage(imageUri, 'farms', `farm_${farmId}.jpg`);
};

/**
 * Upload inventory item image
 * @param imageUri - Local file URI
 * @param itemId - Item ID for filename
 * @returns Promise<string> - Download URL
 */
export const uploadInventoryImage = async (imageUri: string, itemId: string): Promise<string> => {
  return uploadImageToStorage(imageUri, 'inventory', `item_${itemId}.jpg`);
};

/**
 * Upload machinery image
 * @param imageUri - Local file URI
 * @param machineryId - Machinery ID for filename
 * @returns Promise<string> - Download URL
 */
export const uploadMachineryImage = async (imageUri: string, machineryId: string): Promise<string> => {
  return uploadImageToStorage(imageUri, 'machinery', `machinery_${machineryId}.jpg`);
};

/**
 * Upload note attachments
 * @param imageUris - Array of local file URIs
 * @param noteId - Note ID for folder organization
 * @returns Promise<string[]> - Array of download URLs
 */
export const uploadNoteImages = async (imageUris: string[], noteId: string): Promise<string[]> => {
  return uploadMultipleImages(imageUris, `notes/${noteId}`);
};

// Export storage instance for advanced usage
export { storage };

import { getInventoryItems } from "./inventory-service";
import { getUsers } from "./user-service";
import { getFarmById } from "./farm-service";
import { getInventoryRequests } from "./request-service";
import { getNotesByFarm } from "./notes-service";
import { getMachineryReport } from "./machinery-service";

// Mock function for export functionality
export const exportReport = async (reportType: string, format: "pdf" | "excel") => {
  // Simulate export process
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ success: true, message: `${reportType} exported as ${format}` });
    }, 1000);
  });
};

// Get comprehensive dashboard analytics
export const getDashboardAnalytics = async (farmId?: string) => {
  try {
    const [inventoryData, usersData, requestsData, machineryData] = await Promise.all([
      getInventoryReport(farmId),
      getUsersReport(farmId),
      getRequestsReport(farmId),
      getMachineryReport(farmId)
    ]);

    // Generate alerts based on data
    const alerts = generateAlerts(inventoryData, machineryData, requestsData);

    return {
      overview: {
        totalItems: inventoryData.summary.totalItems,
        totalMachinery: machineryData.summary.totalMachinery,
        totalUsers: usersData.summary.totalUsers,
        totalRequests: requestsData.summary.totalRequests,
      },
      alerts,
    };
  } catch (error) {
    console.error("Error getting dashboard analytics:", error);
    return {
      overview: { totalItems: 0, totalMachinery: 0, totalUsers: 0, totalRequests: 0 },
      alerts: [],
    };
  }
};

// Generate alerts for immediate attention
const generateAlerts = (inventoryData: any, machineryData: any, requestsData: any) => {
  const alerts = [];

  // Critical inventory alerts
  if (inventoryData.summary.expiringItems > 0) {
    alerts.push({
      type: 'critical',
      title: 'Items Expiring Soon',
      message: `${inventoryData.summary.expiringItems} items will expire within 30 days`,
      action: 'review_inventory'
    });
  }

  // Low stock alerts
  if (inventoryData.summary.lowStockItems > 0) {
    alerts.push({
      type: 'warning',
      title: 'Low Stock Alert',
      message: `${inventoryData.summary.lowStockItems} items are running low on stock`,
      action: 'reorder_items'
    });
  }

  // Machinery maintenance alerts
  if (machineryData.summary.maintenanceDue > 0) {
    alerts.push({
      type: 'warning',
      title: 'Maintenance Due',
      message: `${machineryData.summary.maintenanceDue} machinery items need maintenance`,
      action: 'schedule_maintenance'
    });
  }

  // Pending requests alerts
  if (requestsData.summary.pendingRequests > 10) {
    alerts.push({
      type: 'info',
      title: 'Many Pending Requests',
      message: `${requestsData.summary.pendingRequests} requests are pending approval`,
      action: 'review_requests'
    });
  }

  return alerts;
};

// Get inventory report data for a specific farm
export const getInventoryReport = async (farmId?: string) => {
  try {
    // Get inventory items for the specific farm
    const items = farmId ? await getInventoryItems(farmId) : [];
    
    if (items.length === 0) {
      // Return empty data structure when no items
      return {
        categoryData: [],
        inventoryLevels: {
          labels: [],
          datasets: [{ data: [] }]
        },
        summary: {
          totalItems: 0,
          lowStockItems: 0,
          expiringItems: 0,
          totalValue: 0
        }
      };
    }
    
    // Calculate category data
    const categories: Record<string, number> = {};
    items.forEach(item => {
      const category = item.category || 'Other';
      categories[category] = (categories[category] || 0) + 1;
    });
    
    const categoryData = Object.keys(categories).map((key, index) => {
      const colors = [
        "#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF", 
        "#FF9F40", "#8AC926", "#1982C4", "#6A4C93", "#FF595E"
      ];
      
      return {
        name: key,
        count: categories[key],
        color: colors[index % colors.length],
      };
    });
    
    // Calculate inventory levels for top items
    const topItems = items.slice(0, 6);
    const inventoryLevels = {
      labels: topItems.map(item => item.name.substring(0, 8)),
      datasets: [
        {
          data: topItems.map(item => item.quantity),
        }
      ]
    };
    
    // Calculate summary data
    const lowStockItems = items.filter(item => item.quantity <= (item.minQuantity || 0)).length;
    const today = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(today.getDate() + 30);
    
    const expiringItems = items.filter(item => {
      if (!item.expiryDate) return false;
      const expiryDate = new Date(item.expiryDate);
      return expiryDate <= thirtyDaysFromNow && expiryDate >= today;
    }).length;
    
    // Calculate total value based on quantity and estimated prices
    const totalValue = items.reduce((sum, item) => {
      const basePrices: Record<string, number> = {
        'Seeds': 25,
        'Fertilizers': 45,
        'Tools': 75,
        'Equipment': 150,
        'Pesticides': 35,
        'Supplies': 20
      };
      
      const basePrice = basePrices[item.category] || 30;
      return sum + (item.quantity * basePrice);
    }, 0);
    
    return {
      categoryData,
      inventoryLevels,
      summary: {
        totalItems: items.length,
        lowStockItems,
        expiringItems,
        totalValue: totalValue
      }
    };
  } catch (error) {
    console.error("Error generating inventory report:", error);
    return {
      categoryData: [],
      inventoryLevels: {
        labels: [],
        datasets: [{ data: [] }]
      },
      summary: {
        totalItems: 0,
        lowStockItems: 0,
        expiringItems: 0,
        totalValue: 0
      }
    };
  }
};

// Get requests report data for a specific farm
export const getRequestsReport = async (farmId?: string) => {
  try {
    if (!farmId) {
      return {
        statusData: [],
        summary: {
          totalRequests: 0,
          pendingRequests: 0,
          approvedRequests: 0,
          rejectedRequests: 0
        }
      };
    }

    // Get requests for the specific farm
    const requests = await getInventoryRequests(farmId);
    
    if (requests.length === 0) {
      return {
        statusData: [],
        summary: {
          totalRequests: 0,
          pendingRequests: 0,
          approvedRequests: 0,
          rejectedRequests: 0
        }
      };
    }
    
    // Calculate status data
    const statuses: Record<string, number> = {};
    requests.forEach(request => {
      const status = request.status;
      statuses[status] = (statuses[status] || 0) + 1;
    });
    
    const statusData = Object.keys(statuses).map((key, index) => {
      const colors = ["#FF6384", "#36A2EB", "#FFCE56"];
      
      return {
        name: key.charAt(0).toUpperCase() + key.slice(1),
        count: statuses[key],
        color: colors[index % colors.length],
      };
    });
    
    // Calculate summary data
    const pendingRequests = requests.filter(request => request.status === "pending").length;
    const approvedRequests = requests.filter(request => request.status === "approved").length;
    const rejectedRequests = requests.filter(request => request.status === "rejected").length;
    
    return {
      statusData,
      summary: {
        totalRequests: requests.length,
        pendingRequests,
        approvedRequests,
        rejectedRequests
      }
    };
  } catch (error) {
    console.error("Error generating requests report:", error);
    return {
      statusData: [],
      summary: {
        totalRequests: 0,
        pendingRequests: 0,
        approvedRequests: 0,
        rejectedRequests: 0
      }
    };
  }
};

// Get users report data for a specific farm
export const getUsersReport = async (farmId?: string) => {
  try {
    // Get users
    const users = await getUsers(farmId);
    
    if (users.length === 0) {
      return {
        roleData: [],
        summary: {
          totalUsers: 0,
          owners: 0,
          admins: 0,
          caretakers: 0,
          activeUsers: 0
        }
      };
    }
    
    // Calculate role data
    const roles: Record<string, number> = {};
    users.forEach(user => {
      const role = user.role;
      roles[role] = (roles[role] || 0) + 1;
    });
    
    const roleData = Object.keys(roles).map((key, index) => {
      const colors = ["#FF6384", "#36A2EB", "#FFCE56"];
      
      return {
        name: key.charAt(0).toUpperCase() + key.slice(1),
        count: roles[key],
        color: colors[index % colors.length],
      };
    });
    
    // Calculate summary data
    const admins = users.filter(user => user.role === "admin").length;
    const caretakers = users.filter(user => user.role === "caretaker").length;
    const owners = users.filter(user => user.role === "owner").length;
    const activeUsers = Math.floor(users.length * 0.8); // Assume 80% of users are active
    
    return {
      roleData,
      summary: {
        totalUsers: users.length,
        owners: owners,
        admins: admins,
        caretakers: caretakers,
        activeUsers: activeUsers
      }
    };
  } catch (error) {
    console.error("Error generating users report:", error);
    return {
      roleData: [],
      summary: {
        totalUsers: 0,
        owners: 0,
        admins: 0,
        caretakers: 0,
        activeUsers: 0
      }
    };
  }
};

// Get activities report data for a specific farm
export const getActivitiesReport = async (farmId?: string) => {
  try {
    // Get all data to calculate activities
    const [items, users, requests, notes] = await Promise.all([
      farmId ? getInventoryItems(farmId) : [],
      farmId ? getUsers(farmId) : [],
      farmId ? getInventoryRequests(farmId) : [],
      farmId ? getNotesByFarm(farmId) : []
    ]);
    
    // Calculate activity type data based on actual data
    const currentDate = new Date();
    const thirtyDaysAgo = new Date(currentDate.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    // Count recent activities
    const recentItems = items.filter(item => {
      if (!item.createdAt) return false;
      return new Date(item.createdAt) >= thirtyDaysAgo;
    });
    
    const recentRequests = requests.filter(request => {
      if (!request.createdAt) return false;
      return new Date(request.createdAt) >= thirtyDaysAgo;
    });
    
    const recentNotes = notes.filter(note => {
      if (!note.createdAt) return false;
      return new Date(note.createdAt) >= thirtyDaysAgo;
    });
    
    const approvedRequests = requests.filter(request => {
      if (request.status !== 'approved' || !request.approvedAt) return false;
      return new Date(request.approvedAt) >= thirtyDaysAgo;
    });
    
    const activityTypes: Record<string, number> = {
      'Inventory Created': recentItems.length,
      'Request Created': recentRequests.length,
      'Request Approved': approvedRequests.length,
      'Note Created': recentNotes.length,
      'User Activity': users.length * 3 // Simulate user activity
    };
    
    const activityTypeData = Object.keys(activityTypes).map((key, index) => {
      const colors = ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF"];
      
      return {
        name: key,
        count: activityTypes[key],
        color: colors[index % colors.length],
      };
    });
    
    // Calculate summary data
    const totalActivities = Object.values(activityTypes).reduce((sum, count) => sum + count, 0);
    const todayActivities = Math.floor(totalActivities * 0.1); // Assume 10% happened today
    const weekActivities = Math.floor(totalActivities * 0.4); // Assume 40% happened this week
    
    return {
      activityTypeData,
      summary: {
        totalActivities: totalActivities,
        todayActivities: todayActivities,
        weekActivities: weekActivities,
        monthActivities: totalActivities
      }
    };
  } catch (error) {
    console.error("Error generating activities report:", error);
    return {
      activityTypeData: [],
      summary: {
        totalActivities: 0,
        todayActivities: 0,
        weekActivities: 0,
        monthActivities: 0
      }
    };
  }
};
import { getMachineryByFarm } from './machinery-service';
import { createRequest } from './request-service';

// Define request types
export type RequestType = 'use' | 'maintenance' | 'fuel' | 'inventory';

// Define conversation states
export type ConversationState = 
  | 'detecting_intent'
  | 'selecting_machinery' 
  | 'collecting_fields'
  | 'confirming_request'
  | 'completed';

// Define field types for different request types
export interface RequestField {
  id: string;
  label: string;
  type: 'text' | 'number' | 'date' | 'select';
  required: boolean;
  options?: string[];
  placeholder?: string;
  prefix?: string;
}

// Define conversation context
export interface ConversationContext {
  state: ConversationState;
  requestType: RequestType;
  selectedMachinery?: any;
  currentFieldIndex: number;
  fields: RequestField[];
  collectedData: Record<string, any>;
  farmId: string;
  farmName?: string;
  userId: string;
  userName?: string;
}

// Detect request intent from user message
export const detectRequestIntent = (message: string): { type: RequestType | null; confidence: number } => {
  const lowerMessage = message.toLowerCase();
  
  // Use patterns
  if (lowerMessage.includes('use') || lowerMessage.includes('borrow') || lowerMessage.includes('need')) {
    if (lowerMessage.includes('tractor') || lowerMessage.includes('harvester') || lowerMessage.includes('machinery')) {
      return { type: 'use', confidence: 0.9 };
    }
  }
  
  if (lowerMessage.includes('maintenance') || lowerMessage.includes('repair') || lowerMessage.includes('fix')) {
    return { type: 'maintenance', confidence: 0.85 };
  }
  
  if (lowerMessage.includes('fuel') || lowerMessage.includes('petrol') || lowerMessage.includes('diesel') || lowerMessage.includes('gas')) {
    return { type: 'fuel', confidence: 0.8 };
  }

  // Check for inventory requests
  if (lowerMessage.includes('need') || lowerMessage.includes('request') || lowerMessage.includes('want')) {
    if (lowerMessage.includes('fertilizer') || lowerMessage.includes('seeds') || lowerMessage.includes('pesticide') ||
        lowerMessage.includes('feed') || lowerMessage.includes('medication') || lowerMessage.includes('tools') ||
        lowerMessage.includes('equipment') || lowerMessage.includes('bags') || lowerMessage.includes('kg') ||
        lowerMessage.includes('liters') || lowerMessage.includes('inventory') || lowerMessage.includes('item') ||
        lowerMessage.includes('کھاد') || lowerMessage.includes('بیج') || lowerMessage.includes('کیڑے مار') ||
        lowerMessage.includes('چارہ') || lowerMessage.includes('دوا') || lowerMessage.includes('اوزار')) {
      return { type: 'inventory', confidence: 0.85 };
    }
  }

  return { type: null, confidence: 0 };
};

// Get fields for different request types
export const getRequestFields = (requestType: RequestType): RequestField[] => {
  switch (requestType) {
    case 'use':
      return [
        {
          id: 'reason',
          label: 'What do you need it for?',
          type: 'text',
          required: true,
          placeholder: 'e.g., plowing field, harvesting crops'
        },
        {
          id: 'startDate',
          label: 'When do you need it?',
          type: 'date',
          required: true,
          placeholder: 'Select start date'
        },
        {
          id: 'endDate',
          label: 'Until when?',
          type: 'date',
          required: true,
          placeholder: 'Select end date'
        },
        {
          id: 'fuelAmount',
          label: 'How much fuel do you need? (liters)',
          type: 'number',
          required: false,
          placeholder: 'e.g., 50'
        },
        {
          id: 'litterPrice',
          label: 'What is the fuel price per liter? (Rs)',
          type: 'number',
          required: false,
          placeholder: 'e.g., 280'
        },
        {
          id: 'odometerReading',
          label: 'Current odometer reading',
          type: 'number',
          required: false,
          placeholder: 'Current hours/kilometers'
        },
        {
          id: 'odometerUnit',
          label: 'Odometer unit',
          type: 'select',
          required: false,
          options: ['hours', 'kilometers'],
          placeholder: 'Select unit'
        },
        {
          id: 'urgency',
          label: 'How urgent is this request?',
          type: 'select',
          required: true,
          options: ['low', 'medium', 'high', 'emergency'],
          placeholder: 'Select urgency level'
        },
        {
          id: 'notes',
          label: 'Any additional notes?',
          type: 'text',
          required: false,
          placeholder: 'Optional additional information'
        }
      ];
      
    case 'maintenance':
      return [
        {
          id: 'reason',
          label: 'What maintenance is needed?',
          type: 'text',
          required: true,
          placeholder: 'Describe the issue or maintenance needed'
        },
        {
          id: 'maintenanceType',
          label: 'Type of maintenance',
          type: 'select',
          required: true,
          options: ['routine', 'emergency', 'repair'],
          placeholder: 'Select maintenance type'
        },
        {
          id: 'startDate',
          label: 'When should this be done?',
          type: 'date',
          required: true,
          placeholder: 'Preferred maintenance date'
        },
        {
          id: 'maintenancePrice',
          label: 'Estimated cost (Rs)',
          type: 'number',
          required: false,
          placeholder: 'Expected maintenance cost'
        },
        {
          id: 'odometerReading',
          label: 'Current odometer reading',
          type: 'number',
          required: false,
          placeholder: 'Current hours/kilometers'
        },
        {
          id: 'odometerUnit',
          label: 'Odometer unit',
          type: 'select',
          required: false,
          options: ['hours', 'kilometers'],
          placeholder: 'Select unit'
        },
        {
          id: 'urgency',
          label: 'How urgent is this?',
          type: 'select',
          required: true,
          options: ['low', 'medium', 'high', 'emergency'],
          placeholder: 'Select urgency level'
        },
        {
          id: 'notes',
          label: 'Any additional notes?',
          type: 'text',
          required: false,
          placeholder: 'Optional additional information'
        }
      ];
      
    case 'fuel':
      return [
        {
          id: 'reason',
          label: 'What do you need fuel for?',
          type: 'text',
          required: true,
          placeholder: 'e.g., field work, transportation'
        },
        {
          id: 'fuelAmount',
          label: 'How much fuel needed? (liters)',
          type: 'number',
          required: true,
          placeholder: 'Enter quantity in liters',
        },
        {
          id: 'litterPrice',
          label: 'Price per liter (Rs)',
          type: 'number',
          required: false,
          placeholder: 'Enter price per liter'
        },
        {
          id: 'startDate',
          label: 'When do you need the fuel?',
          type: 'date',
          required: true,
          placeholder: 'Select date'
        },
        {
          id: 'urgency',
          label: 'How urgent is this?',
          type: 'select',
          required: true,
          options: ['low', 'medium', 'high', 'emergency'],
          placeholder: 'Select urgency level'
        },
        {
          id: 'notes',
          label: 'Any additional notes?',
          type: 'text',
          required: false,
          placeholder: 'Optional additional information'
        }
      ];

    case 'inventory':
      return [
        {
          id: 'itemName',
          label: 'What inventory item do you need?',
          type: 'text',
          required: true,
          placeholder: 'e.g., fertilizer, seeds, pesticides'
        },
        {
          id: 'quantity',
          label: 'How much quantity do you need?',
          type: 'number',
          required: true,
          placeholder: 'e.g., 50'
        },
        {
          id: 'unit',
          label: 'What is the unit?',
          type: 'select',
          required: true,
          options: ['bags', 'kg', 'liters', 'tons', 'pieces', 'bottles', 'boxes', 'packets'],
          placeholder: 'Select unit'
        },
        {
          id: 'category',
          label: 'What category does this item belong to?',
          type: 'select',
          required: true,
          options: ['seeds', 'fertilizers', 'pesticides', 'tools', 'equipment', 'feed', 'medication', 'fuel', 'vaccination', 'other'],
          placeholder: 'Select category'
        },
        {
          id: 'reason',
          label: 'Why do you need this item?',
          type: 'text',
          required: true,
          placeholder: 'e.g., for crop planting, pest control'
        },
        {
          id: 'urgency',
          label: 'How urgent is this request?',
          type: 'select',
          required: true,
          options: ['low', 'medium', 'high', 'emergency'],
          placeholder: 'Select urgency level'
        },
        {
          id: 'notes',
          label: 'Any additional notes?',
          type: 'text',
          required: false,
          placeholder: 'Optional additional information'
        }
      ];

    default:
      return [];
  }
};

// Create initial conversation context
export const createConversationContext = (
  requestType: RequestType,
  farmId: string,
  userId: string,
  userName?: string,
  farmName?: string
): ConversationContext => {
  return {
    state: requestType === 'inventory' ? 'collecting_fields' : 'selecting_machinery',
    requestType,
    currentFieldIndex: 0,
    fields: getRequestFields(requestType),
    collectedData: {},
    farmId,
    farmName,
    userId,
    userName
  };
};

// Get available machinery for selection
export const getAvailableMachinery = async (farmId: string, requestType: RequestType) => {
  try {
    const allMachinery = await getMachineryByFarm(farmId);
    
    // Filter based on request type
    if (requestType === 'use') {
      // Only show machinery that's available (not in use)
      return allMachinery.filter(m => m.status === 'available' || m.status === 'working');
    } else {
      // For maintenance and fuel, show all machinery
      return allMachinery;
    }
  } catch (error) {
    console.error('Error fetching machinery:', error);
    return [];
  }
};

// Process field input and move to next step
export const processFieldInput = (
  context: ConversationContext,
  fieldId: string,
  value: string
): ConversationContext => {
  const updatedData = {
    ...context.collectedData,
    [fieldId]: value
  };
  
  const nextFieldIndex = context.currentFieldIndex + 1;
  const isLastField = nextFieldIndex >= context.fields.length;
  
  return {
    ...context,
    collectedData: updatedData,
    currentFieldIndex: nextFieldIndex,
    state: isLastField ? 'confirming_request' : 'collecting_fields'
  };
};

// Submit the request
export const submitConversationalRequest = async (context: ConversationContext): Promise<{ success: boolean; message: string; requestId?: string }> => {
  try {
    console.log('Submitting conversational request with context:', context);

    // Handle inventory requests
    if (context.requestType === 'inventory') {
      if (!context.collectedData.itemName || !context.collectedData.quantity || !context.collectedData.unit || !context.collectedData.category) {
        return { success: false, message: 'Missing required inventory information' };
      }

      const requestData = {
        farmId: context.farmId,
        farmName: context.farmName,
        requestType: 'inventory',
        itemName: context.collectedData.itemName,
        quantity: Number(context.collectedData.quantity),
        unit: context.collectedData.unit,
        category: context.collectedData.category,
        reason: context.collectedData.reason || 'Requested via AI assistant',
        urgency: context.collectedData.urgency || 'medium',
        notes: context.collectedData.notes,
        status: 'pending',
        requestedBy: context.userId,
        requestedByName: context.userName || 'User',
        requestorRole: 'caretaker',
        createdAt: new Date().toISOString(),
        conversational: true
      };

      console.log('Inventory request data being submitted:', requestData);
      const createdRequest = await createRequest(requestData);

      return {
        success: true,
        message: `Your inventory request for ${context.collectedData.itemName} has been submitted successfully!`,
        requestId: createdRequest.id
      };
    }

    // Handle machinery requests
    if (!context.selectedMachinery) {
      return { success: false, message: 'No machinery selected' };
    }

    // Prepare request data based on type
    const requestData = {
      farmId: context.farmId, // Required farm ID
      farmName: context.farmName, // Required farm name
      itemName: context.selectedMachinery.name, // Required item name
      quantity: 1, // Required quantity (machinery is always 1)
      unit: 'unit', // Required unit
      requestType: 'machinery', // Required request type
      requestSubType: context.requestType === 'use' ? 'use' :
                     context.requestType === 'maintenance' ? 'maintenance' : 'fuel',
      machineryId: context.selectedMachinery.id,
      machineryName: context.selectedMachinery.name,
      machineryType: context.selectedMachinery.type || 'tractor',
      status: 'pending',
      requestedBy: context.userId, // Required user ID
      requestedByName: context.userName || 'User', // Use actual user name
      requestorRole: 'caretaker', // Default role for conversational requests
      createdAt: new Date().toISOString(),

      // Map collected data to proper field names
      reason: context.collectedData.reason,
      startDate: context.collectedData.startDate,
      endDate: context.collectedData.endDate,
      fuelAmount: context.collectedData.fuelAmount ? Number(context.collectedData.fuelAmount) : undefined,
      litterPrice: context.collectedData.litterPrice ? Number(context.collectedData.litterPrice) : undefined,
      totalPrice: context.collectedData.fuelAmount && context.collectedData.litterPrice ?
                  Number(context.collectedData.fuelAmount) * Number(context.collectedData.litterPrice) : undefined,
      maintenanceType: context.collectedData.maintenanceType,
      maintenancePrice: context.collectedData.maintenancePrice ? Number(context.collectedData.maintenancePrice) : undefined,
      odometerReading: context.collectedData.odometerReading ? Number(context.collectedData.odometerReading) : undefined,
      odometerUnit: context.collectedData.odometerUnit || 'hours',
      urgency: context.collectedData.urgency || 'medium',
      notes: context.collectedData.notes,

      // Add metadata
      conversational: true
    };

    console.log('Request data being submitted:', requestData);

    const createdRequest = await createRequest(requestData);

    return {
      success: true,
      message: `Your ${context.requestType} request for ${context.selectedMachinery.name} has been submitted successfully!`,
      requestId: createdRequest.id
    };
  } catch (error) {
    console.error('Error submitting conversational request:', error);
    return {
      success: false,
      message: 'Failed to submit request. Please try again.'
    };
  }
};

// Generate response message for current conversation state
export const generateResponseMessage = (context: ConversationContext, machinery?: any[]): string => {
  switch (context.state) {
    case 'selecting_machinery':
      if (machinery && machinery.length > 0) {
        return `I found ${machinery.length} available machinery for your ${context.requestType} request. Please select one from the list below:`;
      } else {
        return `Sorry, no machinery is currently available for ${context.requestType} requests.`;
      }
      
    case 'collecting_fields':
      const currentField = context.fields[context.currentFieldIndex];
      if (currentField) {
        return currentField.label;
      }
      return 'Please provide the required information.';
      
    case 'confirming_request':
      const summary = Object.entries(context.collectedData)
        .map(([key, value]) => `• ${key}: ${value}`)
        .join('\n');

      if (context.requestType === 'inventory') {
        return `Please confirm your inventory request:\n\n${summary}\n\nShould I submit this request?`;
      } else {
        return `Please confirm your ${context.requestType} request for ${context.selectedMachinery?.name}:\n\n${summary}\n\nShould I submit this request?`;
      }
      
    case 'completed':
      return `Your ${context.requestType} request has been submitted successfully!`;
      
    default:
      return 'How can I help you with your request?';
  }
};

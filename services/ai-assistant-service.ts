import { createInventoryItem } from './inventory-service';
import { addMachinery } from './machinery-service';
import { createRequest, getUserRequests, getRequestsForApproval, getRequestsByType } from './request-service';
import { analyzeImage, ImageAnalysisResult } from './image-analysis-service';
import { uploadImageToStorage, generateItemId } from './image-storage-service';
import {
  detectRequestIntent,
  createConversationContext,
  getAvailableMachinery,
  ConversationContext,
  RequestType
} from './conversational-request-service';
import {
  detectBrowseCommand,
  executeBrowseCommand,
  generateBrowseMessage,
  getBrowseSuggestions,
  BrowseResult
} from './browse-service';
import {
  analyzeCommandRelevance,
  handleIrrelevantCommand,
  improveResponseTone,
  ChatResponse
} from './openrouter-chat-service';
import { GuidedFormData } from '@/components/GuidedFormMessage';
import { convertFormDataToSubmission } from './guided-form-service';

// Define supported actions
export type AIAction =
  | 'create_item'
  | 'update_item'
  | 'delete_item'
  | 'create_request'
  | 'update_request'
  | 'delete_request'
  | 'create_machinery'
  | 'update_machinery'
  | 'delete_machinery'
  | 'analyze_image'
  | 'navigate_to_add_screen'
  | 'start_conversational_request'
  | 'show_machinery_selection'
  | 'show_field_input'
  | 'show_browse_items'
  | 'show_request_options'
  | 'show_requests_list'
  | 'get_help'
  | 'invalid_command'
  | 'irrelevant_command'
  | 'improved_response';

// Define AI response structure
export interface AIResponse {
  action: AIAction;
  data?: Record<string, any>;
  error?: string;
  message?: string;
  suggestions?: string[];
  imageAnalysis?: ImageAnalysisResult;
  navigationData?: {
    screen: '/inventory/edit' | '/(app)/machinery/add';
    params: Record<string, any>;
  };
  conversationContext?: ConversationContext;
  machineryList?: any[];
  currentField?: any;
  browseResult?: BrowseResult;
  chatResponse?: ChatResponse;
  isRelevant?: boolean;
  confidence?: number;
}

// Define user roles and their permissions
export interface UserPermissions {
  canCreateInventory: boolean;
  canUpdateInventory: boolean;
  canDeleteInventory: boolean;
  canCreateMachinery: boolean;
  canUpdateMachinery: boolean;
  canDeleteMachinery: boolean;
  canCreateRequests: boolean;
  canApproveRequests: boolean;
}

// Get permissions based on user role
export const getUserPermissions = (role: string): UserPermissions => {
  switch (role) {
    case 'owner':
      return {
        canCreateInventory: true,
        canUpdateInventory: true,
        canDeleteInventory: true,
        canCreateMachinery: true,
        canUpdateMachinery: true,
        canDeleteMachinery: true,
        canCreateRequests: true,
        canApproveRequests: true,
      };
    case 'admin':
      return {
        canCreateInventory: true,
        canUpdateInventory: true,
        canDeleteInventory: false, // Admin can't delete
        canCreateMachinery: true,
        canUpdateMachinery: true,
        canDeleteMachinery: false, // Admin can't delete
        canCreateRequests: true,
        canApproveRequests: true,
      };
    case 'caretaker':
      return {
        canCreateInventory: false,
        canUpdateInventory: false,
        canDeleteInventory: false,
        canCreateMachinery: false,
        canUpdateMachinery: false,
        canDeleteMachinery: false,
        canCreateRequests: true,
        canApproveRequests: false,
      };
    default:
      return {
        canCreateInventory: false,
        canUpdateInventory: false,
        canDeleteInventory: false,
        canCreateMachinery: false,
        canUpdateMachinery: false,
        canDeleteMachinery: false,
        canCreateRequests: false,
        canApproveRequests: false,
      };
  }
};

// Urdu to English category mapping
const categoryMapping: Record<string, string> = {
  'بیج': 'seeds',
  'کھاد': 'fertilizers',
  'کیڑے مار دوا': 'pesticides',
  'اوزار': 'tools',
  'سامان': 'equipment',
  'چارہ': 'feed',
  'دوا': 'medication',
  'ایندھن': 'fuel',
  'ویکسین': 'vaccination',
  'ڈی اے پی': 'fertilizers',
  'یوریا': 'fertilizers',
  'دیگر': 'other',
  'seeds': 'seeds',
  'fertilizers': 'fertilizers',
  'pesticides': 'pesticides',
  'tools': 'tools',
  'equipment': 'equipment',
  'feed': 'feed',
  'medication': 'medication',
  'fuel': 'fuel',
  'vaccination': 'vaccination',
  'other': 'other',
};

// Urdu to English unit mapping
const unitMapping: Record<string, string> = {
  'تھیلے': 'bags',
  'کلو': 'kg',
  'لیٹر': 'liters',
  'ٹن': 'tons',
  'عدد': 'pieces',
  'بوتل': 'bottles',
  'ڈبہ': 'boxes',
  'پیکٹ': 'packets',
  'bags': 'bags',
  'kg': 'kg',
  'liters': 'liters',
  'tons': 'tons',
  'pieces': 'pieces',
  'bottles': 'bottles',
  'boxes': 'boxes',
  'packets': 'packets',
};

// Machinery type mapping
const machineryTypeMapping: Record<string, string> = {
  'ٹریکٹر': 'tractor',
  'کمبائن': 'harvester',
  'بیج بونے والی مشین': 'planter',
  'چھڑکاؤ مشین': 'sprayer',
  'کلٹیویٹر': 'cultivator',
  'کار': 'car',
  'جیپ': 'jeep',
  'موٹر بائیک': 'motorbike',
  'سائیکل': 'bicycle',
  'دیگر': 'other',
  'tractor': 'tractor',
  'harvester': 'harvester',
  'planter': 'planter',
  'sprayer': 'sprayer',
  'cultivator': 'cultivator',
  'car': 'car',
  'jeep': 'jeep',
  'motorbike': 'motorbike',
  'bicycle': 'bicycle',
  'other': 'other',
};

// Request type mapping
const requestTypeMapping: Record<string, string> = {
  'استعمال': 'use',
  'دیکھ بھال': 'maintenance',
  'نئی مشینری': 'newItem',
  'use': 'use',
  'maintenance': 'maintenance',
  'newItem': 'newItem',
};

// Extract numbers from text (supports both English and Urdu numerals)
const extractNumber = (text: string): number | null => {
  // Convert Urdu numerals to English
  const urduToEnglish: Record<string, string> = {
    '۰': '0', '۱': '1', '۲': '2', '۳': '3', '۴': '4',
    '۵': '5', '۶': '6', '۷': '7', '۸': '8', '۹': '9'
  };

  let convertedText = text;
  Object.keys(urduToEnglish).forEach(urdu => {
    convertedText = convertedText.replace(new RegExp(urdu, 'g'), urduToEnglish[urdu]);
  });

  const match = convertedText.match(/\d+/);
  return match ? parseInt(match[0], 10) : null;
};

// Extract text patterns for different languages
const extractItemName = (text: string): string => {
  // Remove common action words and numbers
  const cleanText = text
    .replace(/\d+/g, '') // Remove numbers
    .replace(/(شامل کریں|add|create|بنائیں|کریں)/gi, '') // Remove action words
    .replace(/(تھیلے|کلو|لیٹر|ٹن|عدد|بوتل|ڈبہ|پیکٹ|bags|kg|liters|tons|pieces|bottles|boxes|packets)/gi, '') // Remove units
    .replace(/(میں|in|to|کو|سے|from)/gi, '') // Remove prepositions
    .trim();

  return cleanText || 'Unknown Item';
};

// Parse user command and extract intent
export const parseUserCommand = (command: string, userRole: string): AIResponse => {
  const lowerCommand = command.toLowerCase().trim();
  const permissions = getUserPermissions(userRole);

  // First, check if the command is farm-related
  const relevanceAnalysis = analyzeCommandRelevance(command);

  // If command is clearly irrelevant, handle it with OpenRouter
  if (!relevanceAnalysis.isRelevant && relevanceAnalysis.confidence > 0.7) {
    return {
      action: 'irrelevant_command',
      message: 'Processing your message...',
      data: { originalCommand: command },
      isRelevant: false,
      confidence: relevanceAnalysis.confidence
    };
  }

  // Check for request listing commands first
  const requestListIntent = detectRequestListCommand(command, userRole);
  if (requestListIntent) {
    return requestListIntent;
  }

  // Check for browse commands
  const browseIntent = detectBrowseCommand(command);
  if (browseIntent.type && browseIntent.confidence > 0.8) {
    return {
      action: 'show_browse_items',
      message: `Let me show you the available items.`,
      data: { browseType: browseIntent.type, confidence: browseIntent.confidence }
    };
  }

  // Check for conversational requests (for caretakers)
  const requestIntent = detectRequestIntent(command);
  if (requestIntent.type && requestIntent.confidence > 0.7 && permissions.canCreateRequests) {
    return {
      action: 'start_conversational_request',
      message: `I understand you want to ${requestIntent.type} machinery. Let me help you with that.`,
      data: { requestType: requestIntent.type, confidence: requestIntent.confidence }
    };
  }

  // Check for help requests
  if (lowerCommand.includes('help') || lowerCommand.includes('مدد') || lowerCommand.includes('کیا کر سکتا ہوں')) {
    return {
      action: 'get_help',
      message: getHelpMessage(userRole),
      suggestions: getHelpSuggestions(userRole)
    };
  }

  // Parse inventory creation commands
  if (lowerCommand.includes('شامل کریں') || lowerCommand.includes('add') || lowerCommand.includes('create')) {
    if (lowerCommand.includes('ٹریکٹر') || lowerCommand.includes('tractor') ||
        lowerCommand.includes('مشین') || lowerCommand.includes('machinery')) {
      return parseMachineryCommand(command, permissions);
    } else {
      return parseInventoryCommand(command, permissions);
    }
  }

  // Parse request creation commands (but exclude listing commands)
  if ((lowerCommand.includes('درخواست') || lowerCommand.includes('request') ||
      lowerCommand.includes('چاہیے') || lowerCommand.includes('need')) &&
      !lowerCommand.includes('show') && !lowerCommand.includes('list') &&
      !lowerCommand.includes('view') && !lowerCommand.includes('all') &&
      !lowerCommand.includes('دکھائیں') && !lowerCommand.includes('فہرست') &&
      !lowerCommand.includes('my requests') && !lowerCommand.includes('caretaker requests') &&
      !lowerCommand.includes('machinery requests') && !lowerCommand.includes('inventory requests')) {
    return parseRequestCommand(command, permissions);
  }

  // Parse update commands
  if (lowerCommand.includes('اپڈیٹ') || lowerCommand.includes('update') ||
      lowerCommand.includes('تبدیل') || lowerCommand.includes('change')) {
    return parseUpdateCommand(command, permissions);
  }

  // Parse delete commands
  if (lowerCommand.includes('ڈیلیٹ') || lowerCommand.includes('delete') ||
      lowerCommand.includes('ہٹا') || lowerCommand.includes('remove')) {
    return parseDeleteCommand(command, permissions);
  }

  return {
    action: 'invalid_command',
    error: 'Invalid command',
    message: 'I didn\'t understand that command. Type "help" for available commands.',
    suggestions: getHelpSuggestions(userRole)
  };
};

// Parse inventory creation commands
const parseInventoryCommand = (command: string, permissions: UserPermissions): AIResponse => {
  if (!permissions.canCreateInventory) {
    return {
      action: 'invalid_command',
      error: 'Permission denied',
      message: 'You don\'t have permission to create inventory items. Only owners and admins can create inventory.'
    };
  }

  const quantity = extractNumber(command);
  const itemName = extractItemName(command);

  // Extract unit
  let unit = 'pieces';
  Object.keys(unitMapping).forEach(key => {
    if (command.toLowerCase().includes(key.toLowerCase())) {
      unit = unitMapping[key];
    }
  });

  // Extract category
  let category = 'other';
  Object.keys(categoryMapping).forEach(key => {
    if (command.toLowerCase().includes(key.toLowerCase())) {
      category = categoryMapping[key];
    }
  });

  if (!quantity || quantity <= 0) {
    return {
      action: 'invalid_command',
      error: 'Invalid quantity',
      message: 'Please specify a valid quantity number.'
    };
  }

  return {
    action: 'create_item',
    data: {
      name: itemName,
      quantity: quantity,
      unit: unit,
      category: category
    }
  };
};

// Parse machinery creation commands
const parseMachineryCommand = (command: string, permissions: UserPermissions): AIResponse => {
  if (!permissions.canCreateMachinery) {
    return {
      action: 'invalid_command',
      error: 'Permission denied',
      message: 'You don\'t have permission to create machinery. Only owners and admins can create machinery.'
    };
  }

  const machineryName = extractItemName(command);

  // Extract machinery type
  let type = 'other';
  Object.keys(machineryTypeMapping).forEach(key => {
    if (command.toLowerCase().includes(key.toLowerCase())) {
      type = machineryTypeMapping[key];
    }
  });

  return {
    action: 'create_machinery',
    data: {
      name: machineryName,
      type: type
    }
  };
};

// Parse request creation commands
const parseRequestCommand = (command: string, permissions: UserPermissions): AIResponse => {
  if (!permissions.canCreateRequests) {
    return {
      action: 'invalid_command',
      error: 'Permission denied',
      message: 'You don\'t have permission to create requests.'
    };
  }

  const quantity = extractNumber(command);
  const itemName = extractItemName(command);

  // Extract unit
  let unit = 'pieces';
  Object.keys(unitMapping).forEach(key => {
    if (command.toLowerCase().includes(key.toLowerCase())) {
      unit = unitMapping[key];
    }
  });

  // Determine if it's machinery or inventory request
  const isMachineryRequest = command.toLowerCase().includes('ٹریکٹر') ||
                            command.toLowerCase().includes('tractor') ||
                            command.toLowerCase().includes('مشین') ||
                            command.toLowerCase().includes('machinery');

  if (isMachineryRequest) {
    // Extract request type for machinery
    let requestSubType = 'use';
    Object.keys(requestTypeMapping).forEach(key => {
      if (command.toLowerCase().includes(key.toLowerCase())) {
        requestSubType = requestTypeMapping[key];
      }
    });

    return {
      action: 'create_request',
      data: {
        requestType: 'machinery',
        machineryName: itemName,
        requestSubType: requestSubType,
        reason: 'Requested via AI assistant'
      }
    };
  } else {
    // Extract category for inventory request
    let category = 'other';
    Object.keys(categoryMapping).forEach(key => {
      if (command.toLowerCase().includes(key.toLowerCase())) {
        category = categoryMapping[key];
      }
    });

    if (!quantity || quantity <= 0) {
      return {
        action: 'invalid_command',
        error: 'Invalid quantity',
        message: 'Please specify a valid quantity number for inventory requests.'
      };
    }

    return {
      action: 'create_request',
      data: {
        requestType: 'inventory',
        itemName: itemName,
        quantity: quantity,
        unit: unit,
        category: category,
        reason: 'Requested via AI assistant'
      }
    };
  }
};

// Detect request listing commands
const detectRequestListCommand = (command: string, userRole: string): AIResponse | null => {
  const lowerCommand = command.toLowerCase();

  // Check for request listing keywords
  const requestKeywords = [
    'show requests', 'list requests', 'my requests', 'view requests', 'all requests',
    'درخواستیں دکھائیں', 'میری درخواستیں', 'درخواستوں کی فہرست', 'تمام درخواستیں',
    'machinery requests', 'inventory requests', 'equipment requests',
    'مشینری کی درخواستیں', 'انوینٹری کی درخواستیں',
    'caretaker requests', 'show my', 'show caretaker', 'show all'
  ];

  // Also check for specific patterns
  const listingPatterns = [
    /show\s+all\s+requests?/i,
    /list\s+all\s+requests?/i,
    /view\s+all\s+requests?/i,
    /all\s+requests?/i,
    /show\s+requests?/i,
    /list\s+requests?/i,
    /view\s+requests?/i
  ];

  const hasRequestKeyword = requestKeywords.some(keyword =>
    lowerCommand.includes(keyword.toLowerCase())
  ) || listingPatterns.some(pattern => pattern.test(lowerCommand));



  if (!hasRequestKeyword) {
    return null;
  }

  // Determine request type
  const isMachineryRequest = lowerCommand.includes('machinery') ||
                            lowerCommand.includes('equipment') ||
                            lowerCommand.includes('مشینری') ||
                            lowerCommand.includes('tractor') ||
                            lowerCommand.includes('ٹریکٹر');

  const isInventoryRequest = lowerCommand.includes('inventory') ||
                            lowerCommand.includes('انوینٹری') ||
                            lowerCommand.includes('stock') ||
                            lowerCommand.includes('items');

  // Check for admin-specific request queries
  const isMyRequestQuery = lowerCommand.includes('show my') || lowerCommand.includes('my requests');
  const isCaretakerRequestQuery = lowerCommand.includes('show caretaker') || lowerCommand.includes('caretaker requests');

  // Handle based on user role
  if (userRole === 'admin') {
    // Check if admin is specifically asking for their own requests or caretaker requests
    if (isMyRequestQuery) {
      // Show admin's own requests directly
      if (isMachineryRequest) {
        return {
          action: 'show_requests_list',
          message: 'Here are your machinery requests submitted to owner:',
          data: {
            requestType: 'machinery',
            userType: 'admin_own'
          }
        };
      } else if (isInventoryRequest) {
        return {
          action: 'show_requests_list',
          message: 'Here are your inventory requests submitted to owner:',
          data: {
            requestType: 'inventory',
            userType: 'admin_own'
          }
        };
      } else {
        return {
          action: 'show_requests_list',
          message: 'Here are all your requests submitted to owner:',
          data: {
            requestType: 'all',
            userType: 'admin_own'
          }
        };
      }
    } else if (isCaretakerRequestQuery) {
      // Show caretaker requests for admin approval
      if (isMachineryRequest) {
        return {
          action: 'show_requests_list',
          message: 'Here are machinery requests from caretakers for your approval:',
          data: {
            requestType: 'machinery',
            userType: 'admin_approval'
          }
        };
      } else if (isInventoryRequest) {
        return {
          action: 'show_requests_list',
          message: 'Here are inventory requests from caretakers for your approval:',
          data: {
            requestType: 'inventory',
            userType: 'admin_approval'
          }
        };
      } else {
        return {
          action: 'show_requests_list',
          message: 'Here are all requests from caretakers for your approval:',
          data: {
            requestType: 'all',
            userType: 'admin_approval'
          }
        };
      }
    } else {
      // Admin users get options for "My Requests" or "Caretaker Requests"
      if (isMachineryRequest) {
        return {
          action: 'show_request_options',
          message: 'Which machinery requests would you like to see?',
          data: {
            requestType: 'machinery',
            options: [
              { id: 'my', label: 'My Requests', description: 'Machinery requests I submitted to owner' },
              { id: 'caretaker', label: 'Caretaker Requests', description: 'Machinery requests from caretakers for approval' }
            ]
          }
        };
      } else if (isInventoryRequest) {
        return {
          action: 'show_request_options',
          message: 'Which inventory requests would you like to see?',
          data: {
            requestType: 'inventory',
            options: [
              { id: 'my', label: 'My Requests', description: 'Inventory requests I submitted to owner' },
              { id: 'caretaker', label: 'Caretaker Requests', description: 'Inventory requests from caretakers for approval' }
            ]
          }
        };
      } else {
        // General requests - show both options
        return {
          action: 'show_request_options',
          message: 'Which requests would you like to see?',
          data: {
            requestType: 'all',
            options: [
              { id: 'my', label: 'My Requests', description: 'All requests I submitted to owner' },
              { id: 'caretaker', label: 'Caretaker Requests', description: 'All requests from caretakers for approval' }
            ]
          }
        };
      }
    }
  } else if (userRole === 'caretaker') {
    // Caretaker users see all their requests directly
    if (isMachineryRequest) {
      return {
        action: 'show_requests_list',
        message: 'Here are your machinery requests:',
        data: {
          requestType: 'machinery',
          userType: 'caretaker'
        }
      };
    } else if (isInventoryRequest) {
      return {
        action: 'show_requests_list',
        message: 'Here are your inventory requests:',
        data: {
          requestType: 'inventory',
          userType: 'caretaker'
        }
      };
    } else {
      return {
        action: 'show_requests_list',
        message: 'Here are all your requests:',
        data: {
          requestType: 'all',
          userType: 'caretaker'
        }
      };
    }
  } else if (userRole === 'owner') {
    // Owner users see all requests directly
    if (isMachineryRequest) {
      return {
        action: 'show_requests_list',
        message: 'Here are all machinery requests for approval:',
        data: {
          requestType: 'machinery',
          userType: 'owner'
        }
      };
    } else if (isInventoryRequest) {
      return {
        action: 'show_requests_list',
        message: 'Here are all inventory requests for approval:',
        data: {
          requestType: 'inventory',
          userType: 'owner'
        }
      };
    } else {
      return {
        action: 'show_requests_list',
        message: 'Here are all requests for approval:',
        data: {
          requestType: 'all',
          userType: 'owner'
        }
      };
    }
  }

  return null;
};

// Parse update commands
const parseUpdateCommand = (command: string, permissions: UserPermissions): AIResponse => {
  // For now, return a message that updates need to be done through the UI
  return {
    action: 'invalid_command',
    error: 'Update not supported',
    message: 'Updates are not supported through chat yet. Please use the app interface to update items.',
    suggestions: ['Go to inventory list to update items', 'Go to machinery list to update machinery']
  };
};

// Parse delete commands
const parseDeleteCommand = (command: string, permissions: UserPermissions): AIResponse => {
  // For now, return a message that deletes need to be done through the UI
  return {
    action: 'invalid_command',
    error: 'Delete not supported',
    message: 'Deletions are not supported through chat for safety reasons. Please use the app interface to delete items.',
    suggestions: ['Go to inventory list to delete items', 'Go to machinery list to delete machinery']
  };
};

// Get help message based on user role
const getHelpMessage = (userRole: string): string => {
  const permissions = getUserPermissions(userRole);
  let message = 'Here\'s what I can help you with:\n\n';

  if (permissions.canCreateInventory) {
    message += '📦 Create Inventory:\n';
    message += '• "Add 20 bags of DAP"\n';
    message += '• "20 تھیلے ڈی اے پی شامل کریں"\n\n';
  }

  if (permissions.canCreateMachinery) {
    message += '🚜 Create Machinery:\n';
    message += '• "Add a new tractor MF-260"\n';
    message += '• "نیا ٹریکٹر MF-260 شامل کریں"\n\n';
  }

  if (permissions.canCreateRequests) {
    message += '📋 Create Requests:\n';
    message += '• "Request 10 kg fertilizer"\n';
    message += '• "10 کلو کھاد کی درخواست"\n';
    message += '• "Request tractor for use"\n';
    message += '• "ٹریکٹر استعمال کی درخواست"\n\n';
  }

  message += '💡 Tips:\n';
  message += '• You can type in English or Urdu\n';
  message += '• Be specific with quantities and names\n';
  message += '• I understand common farm terms';

  return message;
};

// Get help suggestions based on user role
const getHelpSuggestions = (userRole: string): string[] => {
  const permissions = getUserPermissions(userRole);
  const suggestions: string[] = [];

  // Browse suggestions (available to all users)
  suggestions.push('Show all machinery');
  suggestions.push('Show all inventory');
  suggestions.push('Show all tractors');

  if (permissions.canCreateInventory) {
    suggestions.push('Add 50 bags of urea');
    suggestions.push('50 تھیلے یوریا شامل کریں');
  }

  if (permissions.canCreateMachinery) {
    suggestions.push('Add new tractor John Deere');
    suggestions.push('نیا ٹریکٹر شامل کریں');
  }

  if (permissions.canCreateRequests) {
    suggestions.push('I want to use a tractor');
    suggestions.push('Request 20 liters diesel');
    suggestions.push('20 لیٹر ڈیزل کی درخواست');
  }

  return suggestions;
};

// Execute AI action
export const executeAIAction = async (
  response: AIResponse,
  userId: string,
  userName: string,
  userRole: string,
  farmId: string,
  farmName: string
): Promise<{ success: boolean; message: string; data?: any }> => {
  try {
    switch (response.action) {
      case 'create_item':
        return await executeCreateItem(response.data!, userId, farmId);

      case 'create_machinery':
        return await executeCreateMachinery(response.data!, userId, farmId);

      case 'create_request':
        return await executeCreateRequest(response.data!, userId, userName, userRole, farmId, farmName);

      case 'get_help':
        return {
          success: true,
          message: response.message || 'Help information provided',
          data: { suggestions: response.suggestions }
        };

      case 'irrelevant_command':
        return await executeIrrelevantCommand(response.data?.originalCommand || '');

      case 'show_request_options':
        return {
          success: true,
          message: response.message || 'Request options available',
          data: response.data
        };

      case 'show_requests_list':
        return await executeShowRequestsList(response.data as { requestType: string; userType: string }, userId, farmId);

      default:
        return {
          success: false,
          message: response.error || 'Action not supported'
        };
    }
  } catch (error) {
    console.error('Error executing AI action:', error);
    return {
      success: false,
      message: 'An error occurred while executing the action'
    };
  }
};

// Execute create inventory item
const executeCreateItem = async (data: Record<string, any>, userId: string, farmId: string) => {
  try {
    const itemData = {
      name: data.name || 'Unknown Item',
      quantity: data.quantity || data.estimatedQuantity || 1,
      unit: data.unit || 'pieces',
      category: data.category || 'other',
      location: farmId,
      minQuantity: data.minStockLevel || 10,
      expiryDate: data.expiryDate || '',
      supplier: data.supplier || '',
      description: data.notes || 'Created via AI assistant',
      price: data.unitPrice || 0,
      purchaseDate: new Date().toISOString()
    };

    const createdItem = await createInventoryItem(itemData, userId);

    return {
      success: true,
      message: `Successfully created ${data.name} with quantity ${data.quantity} ${data.unit}`,
      data: createdItem
    };
  } catch (error) {
    console.error('Error creating inventory item:', error);
    return {
      success: false,
      message: 'Failed to create inventory item'
    };
  }
};

// Execute create machinery
const executeCreateMachinery = async (data: Record<string, any>, userId: string, farmId: string) => {
  try {
    const machineryData = {
      farmId: farmId,
      name: data.name || 'Unknown Machinery',
      type: (data.type as 'tractor' | 'harvester' | 'planter' | 'sprayer' | 'cultivator' | 'other') || 'other',
      model: data.model || 'Unknown',
      year: data.year || data.estimatedYear || new Date().getFullYear(),
      serialNumber: data.serialNumber || data.registrationNumber || 'TBD',
      status: (data.condition === 'working' ? 'working' : data.condition === 'maintenance' ? 'maintenance' : 'working') as 'working' | 'maintenance' | 'malfunction' | 'in_use' | 'in_use_other_farm',
      fuelType: (data.fuelType || 'diesel') as 'diesel' | 'gasoline' | 'electric' | 'hybrid',
      fuelCapacity: data.fuelCapacity || 100,
      currentFuelLevel: data.currentFuelLevel || 100,
      odometerReading: data.odometerReading || 0,
      lastMaintenanceDate: new Date().toISOString(),
      nextMaintenanceDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(), // 90 days from now
      maintenanceIntervalHours: 500,
      notes: data.notes || 'Created via AI assistant',
      createdBy: userId,
      price: data.purchasePrice || 0,
      currentLocation: data.location || ''
    };

    const createdMachinery = await addMachinery(machineryData);

    return {
      success: true,
      message: `Successfully created ${data.name} machinery`,
      data: createdMachinery
    };
  } catch (error) {
    console.error('Error creating machinery:', error);
    return {
      success: false,
      message: 'Failed to create machinery'
    };
  }
};

// Execute create request
const executeCreateRequest = async (
  data: Record<string, any>,
  userId: string,
  userName: string,
  userRole: string,
  farmId: string,
  farmName: string
) => {
  try {
    const requestData: any = {
      farmId: farmId,
      farmName: farmName,
      requestedBy: userId,
      requestedByName: userName,
      requestorRole: userRole,
      status: 'pending',
      requestType: data.requestType,
      reason: data.reason || 'Requested via AI assistant',
      createdAt: new Date().toISOString(),
    };

    if (data.requestType === 'machinery') {
      requestData.machineryName = data.machineryName;
      requestData.requestSubType = data.requestSubType;
      requestData.itemName = data.machineryName; // For compatibility
      requestData.quantity = 1;
      requestData.unit = 'piece';
    } else {
      requestData.itemName = data.itemName;
      requestData.quantity = data.quantity;
      requestData.unit = data.unit;
      requestData.category = data.category;
    }

    const createdRequest = await createRequest(requestData);

    const requestTypeText = data.requestType === 'machinery' ? 'machinery' : 'inventory';

    return {
      success: true,
      message: `Successfully created ${requestTypeText} request for ${data.itemName || data.machineryName}`,
      data: createdRequest
    };
  } catch (error) {
    console.error('Error creating request:', error);
    return {
      success: false,
      message: 'Failed to create request'
    };
  }
};

// Execute irrelevant command using OpenRouter
const executeIrrelevantCommand = async (command: string) => {
  try {
    const chatResponse = await handleIrrelevantCommand(command);

    return {
      success: true,
      message: chatResponse.message,
      data: {
        suggestions: chatResponse.suggestions,
        isRelevant: chatResponse.isRelevant,
        confidence: chatResponse.confidence
      }
    };
  } catch (error) {
    console.error('Error handling irrelevant command:', error);
    return {
      success: true,
      message: "I'm here to help you manage your farm inventory and machinery. What would you like to do today?",
      data: {
        suggestions: [
          'Show all inventory',
          'Show all machinery',
          'Add new inventory item',
          'Create a request',
          'Type "help" for more options'
        ]
      }
    };
  }
};

// Execute show requests list
const executeShowRequestsList = async (
  data: { requestType: string; userType: string },
  userId: string,
  farmId: string
): Promise<{ success: boolean; message: string; data?: any }> => {
  try {
    let requests: any[] = [];
    const { requestType, userType } = data;

    if (userType === 'caretaker') {
      // Get caretaker's own requests
      if (requestType === 'machinery') {
        requests = await getUserRequests(farmId, userId, undefined, 'machinery');
      } else if (requestType === 'inventory') {
        const inventoryRequests = await getUserRequests(farmId, userId, undefined, 'inventory');
        const existingRequests = await getUserRequests(farmId, userId, undefined, 'existing');
        requests = [...inventoryRequests, ...existingRequests];
      } else {
        // All requests
        const inventoryRequests = await getUserRequests(farmId, userId, undefined, 'inventory');
        const existingRequests = await getUserRequests(farmId, userId, undefined, 'existing');
        const machineryRequests = await getUserRequests(farmId, userId, undefined, 'machinery');
        requests = [...inventoryRequests, ...existingRequests, ...machineryRequests];
      }
    } else if (userType === 'admin_own') {
      // Get admin's own requests (submitted to owner)
      if (requestType === 'machinery') {
        requests = await getUserRequests(farmId, userId, undefined, 'machinery');
      } else if (requestType === 'inventory') {
        const inventoryRequests = await getUserRequests(farmId, userId, undefined, 'inventory');
        const existingRequests = await getUserRequests(farmId, userId, undefined, 'existing');
        requests = [...inventoryRequests, ...existingRequests];
      } else {
        // All requests
        const inventoryRequests = await getUserRequests(farmId, userId, undefined, 'inventory');
        const existingRequests = await getUserRequests(farmId, userId, undefined, 'existing');
        const machineryRequests = await getUserRequests(farmId, userId, undefined, 'machinery');
        requests = [...inventoryRequests, ...existingRequests, ...machineryRequests];
      }
    } else if (userType === 'admin_approval') {
      // Get requests for admin approval (from caretakers)
      if (requestType === 'machinery') {
        requests = await getRequestsForApproval(farmId, 'admin', undefined, 'machinery');
      } else if (requestType === 'inventory') {
        const inventoryRequests = await getRequestsForApproval(farmId, 'admin', undefined, 'inventory');
        const existingRequests = await getRequestsForApproval(farmId, 'admin', undefined, 'existing');
        requests = [...inventoryRequests, ...existingRequests];
      } else {
        // All requests
        const inventoryRequests = await getRequestsForApproval(farmId, 'admin', undefined, 'inventory');
        const existingRequests = await getRequestsForApproval(farmId, 'admin', undefined, 'existing');
        const machineryRequests = await getRequestsForApproval(farmId, 'admin', undefined, 'machinery');
        requests = [...inventoryRequests, ...existingRequests, ...machineryRequests];
      }
    } else if (userType === 'owner') {
      // Get requests for owner approval (from admins)
      if (requestType === 'machinery') {
        requests = await getRequestsForApproval(farmId, 'owner', undefined, 'machinery');
      } else if (requestType === 'inventory') {
        const inventoryRequests = await getRequestsForApproval(farmId, 'owner', undefined, 'inventory');
        const existingRequests = await getRequestsForApproval(farmId, 'owner', undefined, 'existing');
        requests = [...inventoryRequests, ...existingRequests];
      } else {
        // All requests
        const inventoryRequests = await getRequestsForApproval(farmId, 'owner', undefined, 'inventory');
        const existingRequests = await getRequestsForApproval(farmId, 'owner', undefined, 'existing');
        const machineryRequests = await getRequestsForApproval(farmId, 'owner', undefined, 'machinery');
        requests = [...inventoryRequests, ...existingRequests, ...machineryRequests];
      }
    }

    // Sort by creation date (newest first)
    requests.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    // Format the response
    let message = '';
    if (requests.length === 0) {
      message = `No ${requestType === 'all' ? '' : requestType + ' '}requests found.`;
    } else {
      message = `Found ${requests.length} ${requestType === 'all' ? '' : requestType + ' '}request${requests.length > 1 ? 's' : ''}:\n\n`;

      requests.slice(0, 10).forEach((request, index) => {
        const statusEmoji = request.status === 'pending' ? '⏳' :
                           request.status === 'approved' ? '✅' : '❌';
        const typeEmoji = request.requestType === 'machinery' ? '🚜' : '📦';

        message += `${index + 1}. ${typeEmoji} ${statusEmoji} ${request.itemName || request.machineryName || 'Unknown Item'}\n`;
        message += `   Status: ${request.status}\n`;
        message += `   Date: ${new Date(request.createdAt).toLocaleDateString()}\n`;
        if (request.quantity) {
          message += `   Quantity: ${request.quantity} ${request.unit || ''}\n`;
        }
        message += '\n';
      });

      if (requests.length > 10) {
        message += `... and ${requests.length - 10} more requests.\n\n`;
      }

      message += 'Tap on any request to view details or take action.';
    }

    return {
      success: true,
      message,
      data: {
        requests: requests.slice(0, 20), // Limit to 20 for performance
        totalCount: requests.length,
        requestType,
        userType
      }
    };

  } catch (error) {
    console.error('Error fetching requests:', error);
    return {
      success: false,
      message: 'Failed to fetch requests. Please try again.'
    };
  }
};

// Process image with AI analysis
export const processAIImage = async (
  imageUri: string,
  message: string | undefined,
  userId: string,
  userName: string,
  userRole: string,
  farmId: string,
  farmName: string
): Promise<{ success: boolean; message: string; data?: any; suggestions?: string[]; imageAnalysis?: ImageAnalysisResult }> => {
  try {
    // Analyze the image
    const analysisResult = await analyzeImage(imageUri, message);

    // Check permissions based on analysis result
    const permissions = getUserPermissions(userRole);

    if (analysisResult.type === 'inventory' && !permissions.canCreateInventory) {
      return {
        success: false,
        message: 'You don\'t have permission to create inventory items. Only owners and admins can create inventory.',
        imageAnalysis: analysisResult
      };
    }

    if (analysisResult.type === 'machinery' && !permissions.canCreateMachinery) {
      return {
        success: false,
        message: 'You don\'t have permission to create machinery items.',
        suggestions: analysisResult.suggestions,
        imageAnalysis: analysisResult
      };
    }

    if (analysisResult.type === 'unknown') {
      return {
        success: false,
        message: analysisResult.description,
        suggestions: analysisResult.suggestions,
        imageAnalysis: analysisResult
      };
    }

    // Prepare navigation data for add screen
    const navigationData = analysisResult.type === 'inventory'
      ? {
          screen: '/inventory/edit' as const,
          params: convertToInventoryParams(analysisResult, imageUri)
        }
      : {
          screen: '/(app)/machinery/add' as const,
          params: convertToMachineryParams(analysisResult, imageUri)
        };

    // Return navigation data for add screen
    return {
      success: true,
      message: `${analysisResult.description}\n\nI'll take you to the add ${analysisResult.type} screen with pre-filled information.`,
      imageAnalysis: analysisResult,
      data: { navigationData }
    };

    // Fallback - just return analysis
    return {
      success: true,
      message: analysisResult.description,
      suggestions: analysisResult.suggestions,
      imageAnalysis: analysisResult
    };

  } catch (error) {
    console.error('Error processing AI image:', error);
    return {
      success: false,
      message: 'Failed to analyze the image. Please try again.'
    };
  }
};

// Process conversational request
export const processConversationalRequest = async (
  message: string,
  userId: string,
  farmId: string,
  userRole: string,
  farmName?: string
): Promise<AIResponse> => {
  try {
    const permissions = getUserPermissions(userRole);

    // Check if user can create requests
    if (!permissions.canCreateRequests) {
      return {
        action: 'invalid_command',
        error: 'You don\'t have permission to create requests.',
        message: 'Only caretakers can create machinery requests.'
      };
    }

    // Detect request intent
    const intent = detectRequestIntent(message);

    if (!intent.type || intent.confidence < 0.7) {
      return {
        action: 'invalid_command',
        message: 'I couldn\'t understand your request. Please try saying something like "I want to use a tractor" or "I need maintenance for harvester".',
        suggestions: [
          'I want to use a tractor',
          'I need maintenance for harvester',
          'I need fuel for machinery'
        ]
      };
    }

    // Create conversation context
    const context = createConversationContext(intent.type, farmId, userId, undefined, farmName);

    // Handle inventory requests differently (no machinery selection needed)
    if (intent.type === 'inventory') {
      return {
        action: 'show_field_input',
        message: `I'll help you create an inventory request. Let's start by collecting the required information.`,
        conversationContext: context,
        currentField: context.fields[0]
      };
    }

    // Get available machinery for machinery requests
    const machinery = await getAvailableMachinery(farmId, intent.type);

    if (machinery.length === 0) {
      return {
        action: 'invalid_command',
        message: `Sorry, no machinery is currently available for ${intent.type} requests.`,
        suggestions: ['Check machinery status', 'Contact farm owner']
      };
    }

    return {
      action: 'show_machinery_selection',
      message: `I found ${machinery.length} available machinery for your ${intent.type} request. Please select one:`,
      conversationContext: context,
      machineryList: machinery
    };

  } catch (error) {
    console.error('Error processing conversational request:', error);
    return {
      action: 'invalid_command',
      error: 'Failed to process request',
      message: 'Sorry, I encountered an error while processing your request. Please try again.'
    };
  }
};

// Process browse command
export const processBrowseCommand = async (
  browseType: string,
  farmId: string
): Promise<AIResponse> => {
  try {
    const result = await executeBrowseCommand(browseType as any, farmId);

    if (result.count === 0) {
      return {
        action: 'invalid_command',
        message: generateBrowseMessage(result),
        suggestions: await getBrowseSuggestions(farmId)
      };
    }

    return {
      action: 'show_browse_items',
      message: generateBrowseMessage(result),
      browseResult: result
    };

  } catch (error) {
    console.error('Error processing browse command:', error);
    return {
      action: 'invalid_command',
      error: 'Failed to browse items',
      message: 'Sorry, I encountered an error while browsing items. Please try again.'
    };
  }
};

// Convert analysis result to inventory navigation parameters
export const convertToInventoryParams = (analysisResult: ImageAnalysisResult, imageUri: string): Record<string, any> => {
  const extractedData = analysisResult.extractedData as any;

  return {
    // Pre-fill from analysis
    name: extractedData?.name || '',
    category: extractedData?.category || '',
    quantity: extractedData?.estimatedQuantity?.toString() || '',
    unit: extractedData?.unit || '',
    unitPrice: extractedData?.unitPrice?.toString() || '',
    supplier: extractedData?.supplier || '',
    description: extractedData?.notes || '',
    minQuantity: extractedData?.minStockLevel?.toString() || '',
    expiryDate: extractedData?.expiryDate || '',
    // Include image
    imageUri: imageUri,
    // Analysis metadata
    analysisConfidence: analysisResult.confidence.toString(),
    analysisDescription: analysisResult.description,
  };
};

// Convert analysis result to machinery navigation parameters
export const convertToMachineryParams = (analysisResult: ImageAnalysisResult, imageUri: string): Record<string, any> => {
  const extractedData = analysisResult.extractedData as any;

  return {
    // Pre-fill from analysis
    name: extractedData?.name || '',
    type: extractedData?.type || '',
    model: extractedData?.model || '',
    year: extractedData?.estimatedYear?.toString() || new Date().getFullYear().toString(),
    registrationNumber: extractedData?.registrationNumber || '',
    status: extractedData?.condition || 'working',
    fuelType: extractedData?.fuelType || '',
    price: extractedData?.purchasePrice?.toString() || '',
    notes: extractedData?.notes || '',
    // Include image
    imageUri: imageUri,
    // Analysis metadata
    analysisConfidence: analysisResult.confidence.toString(),
    analysisDescription: analysisResult.description,
  };
};

// Submit guided form data
export const submitGuidedForm = async (
  formData: GuidedFormData,
  userId: string,
  farmId: string
): Promise<{ success: boolean; message: string; data?: any }> => {
  try {
    const submissionData = convertFormDataToSubmission(formData);

    // Generate unique item ID for image storage
    const itemId = generateItemId();

    // Upload image to Firebase Storage
    let imageUrl = '';
    try {
      imageUrl = await uploadImageToStorage(
        formData.imageUri,
        formData.type,
        itemId,
        userId
      );
      submissionData.imageUrl = imageUrl;
      submissionData.images = [imageUrl]; // For compatibility with existing schemas
    } catch (imageError) {
      console.warn('Failed to upload image, proceeding without image:', imageError);
      // Continue without image rather than failing the entire operation
    }

    if (formData.type === 'inventory') {
      const result = await executeCreateItem(submissionData, userId, farmId);
      return {
        success: result.success,
        message: result.success
          ? `Successfully created inventory item: ${submissionData.name}${imageUrl ? ' with image' : ''}`
          : result.message,
        data: result.data
      };
    } else if (formData.type === 'machinery') {
      const result = await executeCreateMachinery(submissionData, userId, farmId);
      return {
        success: result.success,
        message: result.success
          ? `Successfully created machinery: ${submissionData.name}${imageUrl ? ' with image' : ''}`
          : result.message,
        data: result.data
      };
    }

    return {
      success: false,
      message: 'Unknown form type'
    };
  } catch (error) {
    console.error('Error submitting guided form:', error);
    return {
      success: false,
      message: 'Failed to submit form data'
    };
  }
};

// Main AI assistant function
export const processAICommand = async (
  command: string,
  userId: string,
  userName: string,
  userRole: string,
  farmId: string,
  farmName: string
): Promise<{ success: boolean; message: string; data?: any; suggestions?: string[]; action?: string; browseResult?: any }> => {
  try {
    // Parse the command
    const response = parseUserCommand(command, userRole);

    // Handle browse commands specially
    if (response.action === 'show_browse_items') {
      const browseResult = await processBrowseCommand(
        response.data?.browseType,
        farmId
      );

      return {
        success: browseResult.browseResult ? true : false,
        message: browseResult.message || 'Browse completed',
        action: browseResult.action,
        browseResult: browseResult.browseResult,
        suggestions: browseResult.suggestions
      };
    }

    // If it's just help, invalid command, or irrelevant command, return immediately
    if (response.action === 'get_help' || response.action === 'invalid_command' || response.action === 'irrelevant_command') {
      // For irrelevant commands, execute the OpenRouter handling
      if (response.action === 'irrelevant_command') {
        const result = await executeAIAction(response, userId, userName, userRole, farmId, farmName);
        return {
          success: result.success,
          message: result.message,
          suggestions: result.data?.suggestions,
          action: response.action
        };
      }

      return {
        success: response.action === 'get_help',
        message: response.message || 'Command processed',
        suggestions: response.suggestions,
        action: response.action
      };
    }

    // Handle conversational request start
    if (response.action === 'start_conversational_request') {
      return {
        success: true,
        message: response.message || 'Starting conversational request',
        action: response.action,
        data: response.data
      };
    }

    // Execute other actions
    const result = await executeAIAction(response, userId, userName, userRole, farmId, farmName);

    return {
      success: result.success,
      message: result.message,
      data: result.data,
      action: response.action,
      suggestions: undefined
    };
  } catch (error) {
    console.error('Error processing AI command:', error);
    return {
      success: false,
      message: 'An error occurred while processing your command'
    };
  }
};

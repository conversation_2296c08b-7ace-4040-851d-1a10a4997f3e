import { collection, query, where, getDocs, doc, getDoc, addDoc, updateDoc, deleteDoc, orderBy, limit, Timestamp } from "firebase/firestore";
import { getFirestore } from "firebase/firestore";
import AsyncStorage from "@react-native-async-storage/async-storage";

const db = getFirestore();

// Define InventoryReturn type
export interface InventoryReturn {
  id: string;
  requestId: string;
  caretakerId: string;
  caretakerName: string;
  itemId?: string;
  itemName: string;
  quantityReturned: number;
  quantityApproved: number; // Original approved quantity
  condition: "good" | "used" | "damaged";
  remarks?: string;
  returnedAt: string;
  returnStatus: "pending" | "approved" | "rejected";
  reviewedBy?: string;
  reviewedByName?: string;
  reviewedAt?: string;
  rejectionReason?: string;
  adminDescription?: string; // Admin's description when approving/rejecting
  farmId: string;
  farmName?: string;
  createdAt: string;
  updatedAt?: string;
}

// Define Request type
export interface Request {
  id: string;
  itemId?: string;
  itemName: string;
  quantity: number;
  unit: string;
  farmId: string; // Required farm ID
  farmName?: string;
  requestedBy: string; // User ID
  requestedByName: string; // User name
  requestorRole?: string; // User role
  status: "pending" | "approved" | "rejected";
  reason?: string;
  notes?: string;
  requestType?: "inventory" | "existing" | "machinery"; // inventory = new item, existing = existing item, machinery = machinery request
  category?: string; // For inventory requests
  // Price fields for inventory requests
  unitPrice?: number; // Price per unit in Rs
  totalPrice?: number; // Total price (unitPrice * quantity) in Rs
  purchaseDate?: string; // Expected purchase date
  supplier?: string; // Supplier name
  approvedBy?: string;
  approvedByName?: string;
  approvedAt?: string;
  rejectedBy?: string;
  rejectedByName?: string;
  rejectedAt?: string;
  rejectionReason?: string;
  createdAt: string;
  updatedAt?: string;
  imageUrl?: string;
  isTransferRequest?: boolean; // Added for transfer requests
  
  // Return-related fields
  canReturn?: boolean; // Whether this request can be returned
  hasReturn?: boolean; // Whether this request has a return
  returnId?: string; // ID of the return if exists
  returnStatus?: "pending" | "approved" | "rejected"; // Status of the return
  
  // Machinery-specific fields
  machineryId?: string;
  machineryName?: string;
  machineryType?: string; // "tractor", "harvester", "planter", etc.
  requestSubType?: "use" | "maintenance" | "transfer"; // Sub-types for machinery requests (fuel merged into use)
  targetFarmId?: string; // For inter-farm machinery requests
  targetFarmName?: string;
  startDate?: string; // When machinery is needed
  endDate?: string; // When machinery should be returned
  fuelAmount?: number; // For fuel requests
  litterPrice?: number; // Price per liter in Rs
  totalPrice?: number; // Total fuel cost (fuelAmount * litterPrice)
  maintenanceType?: "routine" | "emergency" | "repair"; // For maintenance requests
  odometerReading?: number; // Current odometer reading
  returnOdometerReading?: number; // Odometer reading when returned
  returnCondition?: string; // Condition when returned
  returnImageUrl?: string; // Image of machinery when returned
  urgency?: "low" | "medium" | "high" | "emergency"; // For machinery requests
}

// Type for Firestore document data
interface FirestoreRequestData {
  itemId?: string;
  itemName?: string;
  quantity?: number;
  unit?: string;
  farmName?: string;
  requestedBy?: string;
  requestedByName?: string;
  requestorRole?: string;
  status?: string;
  reason?: string;
  notes?: string;
  requestType?: string;
  category?: string;
  approvedBy?: string;
  approvedByName?: string;
  approvedAt?: string;
  rejectedBy?: string;
  rejectedByName?: string;
  rejectedAt?: string;
  rejectionReason?: string;
  createdAt?: string;
  updatedAt?: string;
  imageUrl?: string;
  isTransferRequest?: boolean; // Added for transfer requests
  
  // Return-related fields
  canReturn?: boolean;
  hasReturn?: boolean;
  returnId?: string;
  returnStatus?: string;
  
  // Machinery-specific fields
  machineryId?: string;
  machineryName?: string;
  machineryType?: string;
  requestSubType?: string;
  targetFarmId?: string;
  targetFarmName?: string;
  startDate?: string;
  endDate?: string;
  fuelAmount?: number;
  litterPrice?: number;
  totalPrice?: number;
  maintenanceType?: string;
  odometerReading?: number;
  returnOdometerReading?: number;
  returnCondition?: string;
  returnImageUrl?: string;
  urgency?: string;
}

// Type for Firestore inventory return data
interface FirestoreInventoryReturnData {
  requestId?: string;
  caretakerId?: string;
  caretakerName?: string;
  itemId?: string;
  itemName?: string;
  quantityReturned?: number;
  quantityApproved?: number;
  condition?: string;
  remarks?: string;
  returnedAt?: string;
  returnStatus?: string;
  reviewedBy?: string;
  reviewedByName?: string;
  reviewedAt?: string;
  rejectionReason?: string;
  adminDescription?: string;
  farmId?: string;
  farmName?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Get requests for a specific user (their own requests)
export const getUserRequests = async (
  farmId: string,
  userId: string,
  status?: "pending" | "approved" | "rejected",
  requestType?: "inventory" | "existing" | "machinery"
): Promise<Request[]> => {
  try {
    if (!farmId || !userId) {
      throw new Error("Farm ID and User ID are required");
    }

    const requests: Request[] = [];
    
    // Get requests for this specific farm and user
    const requestsRef = collection(db, "farms", farmId, "requests");
    
    // Build query based on filters
    const queryFilters = [where("requestedBy", "==", userId)];
    
    if (status) {
      queryFilters.push(where("status", "==", status));
    }
    
    if (requestType) {
      queryFilters.push(where("requestType", "==", requestType));
    }
    
    const requestsQuery = query(requestsRef, ...queryFilters, orderBy("createdAt", "desc"));
    const requestsSnapshot = await getDocs(requestsQuery);
    
    requestsSnapshot.forEach(docSnapshot => {
      const data = docSnapshot.data() as FirestoreRequestData;
      
      requests.push({
        id: docSnapshot.id,
        itemName: data.itemName || "",
        quantity: data.quantity || 0,
        unit: data.unit || "",
        farmId: farmId,
        farmName: data.farmName,
        requestedBy: data.requestedBy || "",
        requestedByName: data.requestedByName || "",
        requestorRole: data.requestorRole,
        status: (data.status as "pending" | "approved" | "rejected") || "pending",
        reason: data.reason,
        notes: data.notes,
        requestType: data.requestType as "inventory" | "existing" | "machinery" | undefined,
        category: data.category,
        // Price fields
        unitPrice: data.unitPrice,
        totalPrice: data.totalPrice,
        purchaseDate: data.purchaseDate,
        supplier: data.supplier,
        approvedBy: data.approvedBy,
        approvedByName: data.approvedByName,
        approvedAt: data.approvedAt,
        rejectedBy: data.rejectedBy,
        rejectedByName: data.rejectedByName,
        rejectedAt: data.rejectedAt,
        rejectionReason: data.rejectionReason,
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt,
        imageUrl: data.imageUrl,
        itemId: data.itemId,
        isTransferRequest: data.isTransferRequest,
        
        // Return-related fields
        canReturn: data.canReturn,
        hasReturn: data.hasReturn,
        returnId: data.returnId,
        returnStatus: data.returnStatus as "pending" | "approved" | "rejected" | undefined,
        
        // Machinery-specific fields
        machineryId: data.machineryId,
        machineryName: data.machineryName,
        machineryType: data.machineryType,
        requestSubType: data.requestSubType as "use" | "maintenance" | "transfer" | undefined,
        targetFarmId: data.targetFarmId,
        targetFarmName: data.targetFarmName,
        startDate: data.startDate,
        endDate: data.endDate,
        fuelAmount: data.fuelAmount,
        litterPrice: data.litterPrice,
        totalPrice: data.totalPrice,
        maintenanceType: data.maintenanceType as "routine" | "emergency" | "repair" | undefined,
        maintenancePrice: data.maintenancePrice,
        odometerReading: data.odometerReading,
        returnOdometerReading: data.returnOdometerReading,
        returnCondition: data.returnCondition,
        returnImageUrl: data.returnImageUrl,
        urgency: data.urgency as "low" | "medium" | "high" | "emergency" | undefined,
      });
    });
    
    // Cache the results
    const cacheKey = `user_requests_${farmId}_${userId}_${status || "all"}_${requestType || "all"}`;
    await AsyncStorage.setItem(cacheKey, JSON.stringify(requests));
    
    return requests;
  } catch (error) {
    console.error("Error getting user requests:", error);
    
    // Try to get from cache if available
    if (farmId && userId) {
      const cacheKey = `user_requests_${farmId}_${userId}_${status || "all"}_${requestType || "all"}`;
      const cachedRequests = await AsyncStorage.getItem(cacheKey);
      if (cachedRequests) {
        return JSON.parse(cachedRequests);
      }
    }
    
    return [];
  }
};

// Get requests directed to a specific user role (for approval)
export const getRequestsForApproval = async (
  farmId: string,
  approverRole: "admin" | "owner",
  status?: "pending" | "approved" | "rejected",
  requestType?: "inventory" | "existing" | "machinery"
): Promise<Request[]> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }

    const requests: Request[] = [];
    
    // Get requests for this specific farm
    const requestsRef = collection(db, "farms", farmId, "requests");
    
    // Build query based on approver role
    const queryFilters = [];
    
    if (approverRole === "admin") {
      // Admin sees requests from caretakers
      queryFilters.push(where("requestorRole", "==", "caretaker"));
    } else if (approverRole === "owner") {
      // Owner sees requests from admins
      queryFilters.push(where("requestorRole", "==", "admin"));
    }
    
    if (status) {
      queryFilters.push(where("status", "==", status));
    }
    
    if (requestType) {
      queryFilters.push(where("requestType", "==", requestType));
    }
    
    const requestsQuery = query(requestsRef, ...queryFilters, orderBy("createdAt", "desc"));
    const requestsSnapshot = await getDocs(requestsQuery);
    
    requestsSnapshot.forEach(docSnapshot => {
      const data = docSnapshot.data() as FirestoreRequestData;
      
      requests.push({
        id: docSnapshot.id,
        itemName: data.itemName || "",
        quantity: data.quantity || 0,
        unit: data.unit || "",
        farmId: farmId,
        farmName: data.farmName,
        requestedBy: data.requestedBy || "",
        requestedByName: data.requestedByName || "",
        requestorRole: data.requestorRole,
        status: (data.status as "pending" | "approved" | "rejected") || "pending",
        reason: data.reason,
        notes: data.notes,
        requestType: data.requestType as "inventory" | "existing" | "machinery" | undefined,
        category: data.category,
        approvedBy: data.approvedBy,
        approvedByName: data.approvedByName,
        approvedAt: data.approvedAt,
        rejectedBy: data.rejectedBy,
        rejectedByName: data.rejectedByName,
        rejectedAt: data.rejectedAt,
        rejectionReason: data.rejectionReason,
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt,
        imageUrl: data.imageUrl,
        itemId: data.itemId,
        isTransferRequest: data.isTransferRequest,
        
        // Return-related fields
        canReturn: data.canReturn,
        hasReturn: data.hasReturn,
        returnId: data.returnId,
        returnStatus: data.returnStatus as "pending" | "approved" | "rejected" | undefined,
        
        // Machinery-specific fields
        machineryId: data.machineryId,
        machineryName: data.machineryName,
        machineryType: data.machineryType,
        requestSubType: data.requestSubType as "use" | "maintenance" | "transfer" | undefined,
        targetFarmId: data.targetFarmId,
        targetFarmName: data.targetFarmName,
        startDate: data.startDate,
        endDate: data.endDate,
        fuelAmount: data.fuelAmount,
        litterPrice: data.litterPrice,
        totalPrice: data.totalPrice,
        maintenanceType: data.maintenanceType as "routine" | "emergency" | "repair" | undefined,
        maintenancePrice: data.maintenancePrice,
        odometerReading: data.odometerReading,
        returnOdometerReading: data.returnOdometerReading,
        returnCondition: data.returnCondition,
        returnImageUrl: data.returnImageUrl,
        urgency: data.urgency as "low" | "medium" | "high" | "emergency" | undefined,
      });
    });
    
    // Cache the results
    const cacheKey = `approval_requests_${farmId}_${approverRole}_${status || "all"}_${requestType || "all"}`;
    await AsyncStorage.setItem(cacheKey, JSON.stringify(requests));
    
    return requests;
  } catch (error) {
    console.error("Error getting requests for approval:", error);
    
    // Try to get from cache if available
    if (farmId) {
      const cacheKey = `approval_requests_${farmId}_${approverRole}_${status || "all"}_${requestType || "all"}`;
      const cachedRequests = await AsyncStorage.getItem(cacheKey);
      if (cachedRequests) {
        return JSON.parse(cachedRequests);
      }
    }
    
    return [];
  }
};

// Get all inventory requests for a specific farm
export const getInventoryRequests = async (
  farmId: string, // Required farm ID
  userId?: string,
  status?: "pending" | "approved" | "rejected"
): Promise<Request[]> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }

    const requests: Request[] = [];
    
    // Get requests for this specific farm
    const requestsRef = collection(db, "farms", farmId, "requests");
    
    // Build query based on filters
    let requestsQuery: any = requestsRef;
    
    if (userId && status) {
      requestsQuery = query(requestsRef, 
        where("requestedBy", "==", userId),
        where("status", "==", status)
      );
    } else if (userId) {
      requestsQuery = query(requestsRef, where("requestedBy", "==", userId));
    } else if (status) {
      requestsQuery = query(requestsRef, where("status", "==", status));
    }
    
    const requestsSnapshot = await getDocs(requestsQuery);
    
    requestsSnapshot.forEach(docSnapshot => {
      const data = docSnapshot.data() as FirestoreRequestData;
      
      requests.push({
        id: docSnapshot.id,
        itemName: data.itemName || "",
        quantity: data.quantity || 0,
        unit: data.unit || "",
        farmId: farmId,
        farmName: data.farmName,
        requestedBy: data.requestedBy || "",
        requestedByName: data.requestedByName || "",
        requestorRole: data.requestorRole,
        status: (data.status as "pending" | "approved" | "rejected") || "pending",
        reason: data.reason,
        notes: data.notes,
        requestType: data.requestType as "inventory" | "existing" | "machinery" | undefined,
        category: data.category,
        approvedBy: data.approvedBy,
        approvedByName: data.approvedByName,
        approvedAt: data.approvedAt,
        rejectedBy: data.rejectedBy,
        rejectedByName: data.rejectedByName,
        rejectedAt: data.rejectedAt,
        rejectionReason: data.rejectionReason,
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt,
        imageUrl: data.imageUrl,
        itemId: data.itemId,
        isTransferRequest: data.isTransferRequest,
        
        // Return-related fields
        canReturn: data.canReturn,
        hasReturn: data.hasReturn,
        returnId: data.returnId,
        returnStatus: data.returnStatus as "pending" | "approved" | "rejected" | undefined,
        
        // Machinery-specific fields
        machineryId: data.machineryId,
        machineryName: data.machineryName,
        machineryType: data.machineryType,
        requestSubType: data.requestSubType as "use" | "maintenance" | "transfer" | undefined,
        targetFarmId: data.targetFarmId,
        targetFarmName: data.targetFarmName,
        startDate: data.startDate,
        endDate: data.endDate,
        fuelAmount: data.fuelAmount,
        litterPrice: data.litterPrice,
        totalPrice: data.totalPrice,
        maintenanceType: data.maintenanceType as "routine" | "emergency" | "repair" | undefined,
        maintenancePrice: data.maintenancePrice,
        odometerReading: data.odometerReading,
        returnOdometerReading: data.returnOdometerReading,
        returnCondition: data.returnCondition,
        returnImageUrl: data.returnImageUrl,
        urgency: data.urgency as "low" | "medium" | "high" | "emergency" | undefined,
      });
    });
    
    // Sort by createdAt
    requests.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
    
    // Cache the results
    const cacheKey = `requests_${farmId}_${userId || "all"}_${status || "all"}`;
    await AsyncStorage.setItem(cacheKey, JSON.stringify(requests));
    
    return requests;
  } catch (error) {
    console.error("Error getting inventory requests:", error);
    
    // Try to get from cache if available
    if (farmId) {
      const cacheKey = `requests_${farmId}_${userId || "all"}_${status || "all"}`;
      const cachedRequests = await AsyncStorage.getItem(cacheKey);
      if (cachedRequests) {
        return JSON.parse(cachedRequests);
      }
    }
    
    return [];
  }
};

// Alias for getInventoryRequests to maintain backward compatibility
export const getRequests = getInventoryRequests;

// Get all requests across all farms (for owner role)
export const getAllRequests = async (
  userId?: string,
  status?: "pending" | "approved" | "rejected"
): Promise<Request[]> => {
  try {
    const requests: Request[] = [];
    
    // Get all farms first to query their subcollections
    const farmsRef = collection(db, "farms");
    const farmsSnapshot = await getDocs(farmsRef);
    
    // For each farm, query its requests subcollection
    const queryPromises = farmsSnapshot.docs.map(async (farmDoc) => {
      const farmId = farmDoc.id;
      const requestsRef = collection(db, "farms", farmId, "requests");
      
      // Build query based on filters
      let requestsQuery: any = requestsRef;
      const queryFilters = [];
      
      if (userId) {
        queryFilters.push(where("requestedBy", "==", userId));
      }
      
      if (status) {
        queryFilters.push(where("status", "==", status));
      }
      
      if (queryFilters.length > 0) {
        // Apply each filter individually
        if (queryFilters.length === 1) {
          requestsQuery = query(requestsRef, queryFilters[0]);
        } else if (queryFilters.length === 2) {
          requestsQuery = query(requestsRef, queryFilters[0], queryFilters[1]);
        }
      }
      
      const requestsSnapshot = await getDocs(requestsQuery);
      
      requestsSnapshot.forEach(docSnapshot => {
        const data = docSnapshot.data() as FirestoreRequestData;
        
        requests.push({
          id: docSnapshot.id,
          itemName: data.itemName || "",
          quantity: data.quantity || 0,
          unit: data.unit || "",
          farmId: farmId,
          farmName: data.farmName,
          requestedBy: data.requestedBy || "",
          requestedByName: data.requestedByName || "",
          requestorRole: data.requestorRole,
          status: (data.status as "pending" | "approved" | "rejected") || "pending",
          reason: data.reason,
          notes: data.notes,
          requestType: data.requestType as "inventory" | "existing" | "machinery" | undefined,
          category: data.category,
          approvedBy: data.approvedBy,
          approvedByName: data.approvedByName,
          approvedAt: data.approvedAt,
          rejectedBy: data.rejectedBy,
          rejectedByName: data.rejectedByName,
          rejectedAt: data.rejectedAt,
          rejectionReason: data.rejectionReason,
          createdAt: data.createdAt || new Date().toISOString(),
          updatedAt: data.updatedAt,
          imageUrl: data.imageUrl,
          itemId: data.itemId,
          isTransferRequest: data.isTransferRequest,
          
          // Return-related fields
          canReturn: data.canReturn,
          hasReturn: data.hasReturn,
          returnId: data.returnId,
          returnStatus: data.returnStatus as "pending" | "approved" | "rejected" | undefined,
          
          // Machinery-specific fields
          machineryId: data.machineryId,
          machineryName: data.machineryName,
          machineryType: data.machineryType,
          requestSubType: data.requestSubType as "use" | "maintenance" | "transfer" | undefined,
          targetFarmId: data.targetFarmId,
          targetFarmName: data.targetFarmName,
          startDate: data.startDate,
          endDate: data.endDate,
          fuelAmount: data.fuelAmount,
          litterPrice: data.litterPrice,
          totalPrice: data.totalPrice,
          maintenanceType: data.maintenanceType as "routine" | "emergency" | "repair" | undefined,
          maintenancePrice: data.maintenancePrice,
          odometerReading: data.odometerReading,
          returnOdometerReading: data.returnOdometerReading,
          returnCondition: data.returnCondition,
          returnImageUrl: data.returnImageUrl,
          urgency: data.urgency as "low" | "medium" | "high" | "emergency" | undefined,
        });
      });
    });
    
    // Wait for all queries to complete
    await Promise.all(queryPromises);
    
    // Sort by createdAt
    requests.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
    
    return requests;
  } catch (error) {
    console.error("Error getting all requests:", error);
    return [];
  }
};

// Get requests by type and role for a specific farm
export const getRequestsByType = async (
  farmId: string, // Required farm ID
  requestType?: "inventory" | "existing" | "machinery",
  requestorRole?: string,
  status?: "pending" | "approved" | "rejected"
): Promise<Request[]> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }

    const requests: Request[] = [];
    
    // Get requests for this specific farm
    const requestsRef = collection(db, "farms", farmId, "requests");
    
    // Build query based on filters
    let requestsQuery: any = requestsRef;
    const queryFilters = [];
    
    if (requestType) {
      queryFilters.push(where("requestType", "==", requestType));
    }
    
    if (requestorRole) {
      queryFilters.push(where("requestorRole", "==", requestorRole));
    }
    
    if (status) {
      queryFilters.push(where("status", "==", status));
    }
    
    if (queryFilters.length > 0) {
      // Apply each filter individually
      if (queryFilters.length === 1) {
        requestsQuery = query(requestsRef, queryFilters[0]);
      } else if (queryFilters.length === 2) {
        requestsQuery = query(requestsRef, queryFilters[0], queryFilters[1]);
      } else if (queryFilters.length === 3) {
        requestsQuery = query(requestsRef, queryFilters[0], queryFilters[1], queryFilters[2]);
      }
    }
    
    const requestsSnapshot = await getDocs(requestsQuery);
    
    requestsSnapshot.forEach(docSnapshot => {
      const data = docSnapshot.data() as FirestoreRequestData;
      
      const request: Request = {
        id: docSnapshot.id,
        itemName: data.itemName || "",
        quantity: data.quantity || 0,
        unit: data.unit || "",
        farmId: farmId,
        farmName: data.farmName,
        requestedBy: data.requestedBy || "",
        requestedByName: data.requestedByName || "",
        requestorRole: data.requestorRole,
        status: (data.status as "pending" | "approved" | "rejected") || "pending",
        reason: data.reason,
        notes: data.notes,
        requestType: data.requestType as "inventory" | "existing" | "machinery" | undefined,
        category: data.category,
        // Price fields
        unitPrice: data.unitPrice,
        totalPrice: data.totalPrice,
        purchaseDate: data.purchaseDate,
        supplier: data.supplier,
        approvedBy: data.approvedBy,
        approvedByName: data.approvedByName,
        approvedAt: data.approvedAt,
        rejectedBy: data.rejectedBy,
        rejectedByName: data.rejectedByName,
        rejectedAt: data.rejectedAt,
        rejectionReason: data.rejectionReason,
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt,
        imageUrl: data.imageUrl,
        itemId: data.itemId,
        isTransferRequest: data.isTransferRequest,
        
        // Return-related fields
        canReturn: data.canReturn,
        hasReturn: data.hasReturn,
        returnId: data.returnId,
        returnStatus: data.returnStatus as "pending" | "approved" | "rejected" | undefined,
        
        // Machinery-specific fields
        machineryId: data.machineryId,
        machineryName: data.machineryName,
        machineryType: data.machineryType,
        requestSubType: data.requestSubType as "use" | "maintenance" | "transfer" | undefined,
        targetFarmId: data.targetFarmId,
        targetFarmName: data.targetFarmName,
        startDate: data.startDate,
        endDate: data.endDate,
        fuelAmount: data.fuelAmount,
        litterPrice: data.litterPrice,
        totalPrice: data.totalPrice,
        maintenanceType: data.maintenanceType as "routine" | "emergency" | "repair" | undefined,
        maintenancePrice: data.maintenancePrice,
        odometerReading: data.odometerReading,
        returnOdometerReading: data.returnOdometerReading,
        returnCondition: data.returnCondition,
        returnImageUrl: data.returnImageUrl,
        urgency: data.urgency as "low" | "medium" | "high" | "emergency" | undefined,
      };
      requests.push(request);
    });
    
    console.log(`Found ${requests.length} requests with type=${requestType}, role=${requestorRole}, status=${status || 'any'}`);
    
    // Sort by createdAt
    requests.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
    
    return requests;
  } catch (error) {
    console.error("Error getting requests by type:", error);
    return [];
  }
};

// Get request by ID
export const getRequestById = async (requestId: string, farmId: string): Promise<Request | null> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }

    // Try to get the request from the specified farm
    const requestRef = doc(db, "farms", farmId, "requests", requestId);
    const requestDoc = await getDoc(requestRef);
    
    if (requestDoc.exists()) {
      const data = requestDoc.data() as FirestoreRequestData;
      
      const request: Request = {
        id: requestDoc.id,
        itemName: data.itemName || "",
        quantity: data.quantity || 0,
        unit: data.unit || "",
        farmId: farmId,
        farmName: data.farmName,
        requestedBy: data.requestedBy || "",
        requestedByName: data.requestedByName || "",
        requestorRole: data.requestorRole,
        status: (data.status as "pending" | "approved" | "rejected") || "pending",
        reason: data.reason,
        notes: data.notes,
        requestType: data.requestType as "inventory" | "existing" | "machinery" | undefined,
        category: data.category,
        // Price fields
        unitPrice: data.unitPrice,
        totalPrice: data.totalPrice,
        purchaseDate: data.purchaseDate,
        supplier: data.supplier,
        approvedBy: data.approvedBy,
        approvedByName: data.approvedByName,
        approvedAt: data.approvedAt,
        rejectedBy: data.rejectedBy,
        rejectedByName: data.rejectedByName,
        rejectedAt: data.rejectedAt,
        rejectionReason: data.rejectionReason,
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt,
        imageUrl: data.imageUrl,
        itemId: data.itemId,
        isTransferRequest: data.isTransferRequest,
        
        // Return-related fields
        canReturn: data.canReturn,
        hasReturn: data.hasReturn,
        returnId: data.returnId,
        returnStatus: data.returnStatus as "pending" | "approved" | "rejected" | undefined,
        
        // Machinery-specific fields
        machineryId: data.machineryId,
        machineryName: data.machineryName,
        machineryType: data.machineryType,
        requestSubType: data.requestSubType as "use" | "maintenance" | "transfer" | undefined,
        targetFarmId: data.targetFarmId,
        targetFarmName: data.targetFarmName,
        startDate: data.startDate,
        endDate: data.endDate,
        fuelAmount: data.fuelAmount,
        litterPrice: data.litterPrice,
        totalPrice: data.totalPrice,
        maintenanceType: data.maintenanceType as "routine" | "emergency" | "repair" | undefined,
        maintenancePrice: data.maintenancePrice,
        odometerReading: data.odometerReading,
        returnOdometerReading: data.returnOdometerReading,
        returnCondition: data.returnCondition,
        returnImageUrl: data.returnImageUrl,
        urgency: data.urgency as "low" | "medium" | "high" | "emergency" | undefined,
      };
      return request;
    }
    
    return null;
  } catch (error) {
    console.error("Error getting request by ID:", error);
    return null;
  }
};

// Create a new request
export const createRequest = async (requestData: Partial<Request>): Promise<Request> => {
  try {
    if (!requestData.farmId) {
      throw new Error("Farm ID is required");
    }
    
    const farmId = requestData.farmId;
    
    // Create the new request with all required fields
    const newRequestData: Record<string, any> = {
      farmId: farmId,
      itemName: requestData.itemName || "",
      quantity: requestData.quantity || 0,
      unit: requestData.unit || "",
      farmName: requestData.farmName,
      requestedBy: requestData.requestedBy || "",
      requestedByName: requestData.requestedByName || "",
      requestorRole: requestData.requestorRole,
      status: requestData.status || "pending",
      reason: requestData.reason,
      notes: requestData.notes,
      requestType: requestData.requestType,
      category: requestData.category,
      approvedBy: requestData.approvedBy,
      approvedByName: requestData.approvedByName,
      approvedAt: requestData.approvedAt,
      rejectedBy: requestData.rejectedBy,
      rejectedByName: requestData.rejectedByName,
      rejectedAt: requestData.rejectedAt,
      rejectionReason: requestData.rejectionReason,
      createdAt: requestData.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      imageUrl: requestData.imageUrl,
      itemId: requestData.itemId,
      isTransferRequest: requestData.isTransferRequest,
      
      // Return-related fields
      canReturn: requestData.canReturn || false,
      hasReturn: requestData.hasReturn || false,
      returnId: requestData.returnId,
      returnStatus: requestData.returnStatus,
      
      // Machinery-specific fields
      machineryId: requestData.machineryId,
      machineryName: requestData.machineryName,
      machineryType: requestData.machineryType,
      requestSubType: requestData.requestSubType,
      targetFarmId: requestData.targetFarmId,
      targetFarmName: requestData.targetFarmName,
      startDate: requestData.startDate,
      endDate: requestData.endDate,
      fuelAmount: requestData.fuelAmount,
      litterPrice: requestData.litterPrice,
      totalPrice: requestData.totalPrice,
      maintenanceType: requestData.maintenanceType,
      maintenancePrice: requestData.maintenancePrice,
      odometerReading: requestData.odometerReading,
      returnOdometerReading: requestData.returnOdometerReading,
      returnCondition: requestData.returnCondition,
      returnImageUrl: requestData.returnImageUrl,
      urgency: requestData.urgency,
    };
    
    // Remove undefined values
    const cleanedRequestData: Record<string, any> = {};
    Object.entries(newRequestData).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        cleanedRequestData[key] = value;
      }
    });
    
    // Add to Firestore
    const requestsRef = collection(db, "farms", farmId, "requests");
    const docRef = await addDoc(requestsRef, cleanedRequestData);
    
    const createdRequest: Request = {
      id: docRef.id,
      itemName: cleanedRequestData.itemName || "",
      quantity: cleanedRequestData.quantity || 0,
      unit: cleanedRequestData.unit || "",
      farmId: farmId,
      farmName: cleanedRequestData.farmName,
      requestedBy: cleanedRequestData.requestedBy || "",
      requestedByName: cleanedRequestData.requestedByName || "",
      requestorRole: cleanedRequestData.requestorRole,
      status: cleanedRequestData.status as "pending" | "approved" | "rejected",
      reason: cleanedRequestData.reason,
      notes: cleanedRequestData.notes,
      requestType: cleanedRequestData.requestType as "inventory" | "existing" | "machinery" | undefined,
      category: cleanedRequestData.category,
      approvedBy: cleanedRequestData.approvedBy,
      approvedByName: cleanedRequestData.approvedByName,
      approvedAt: cleanedRequestData.approvedAt,
      rejectedBy: cleanedRequestData.rejectedBy,
      rejectedByName: cleanedRequestData.rejectedByName,
      rejectedAt: cleanedRequestData.rejectedAt,
      rejectionReason: cleanedRequestData.rejectionReason,
      createdAt: cleanedRequestData.createdAt,
      updatedAt: cleanedRequestData.updatedAt,
      imageUrl: cleanedRequestData.imageUrl,
      itemId: cleanedRequestData.itemId,
      isTransferRequest: cleanedRequestData.isTransferRequest,
      
      // Return-related fields
      canReturn: cleanedRequestData.canReturn,
      hasReturn: cleanedRequestData.hasReturn,
      returnId: cleanedRequestData.returnId,
      returnStatus: cleanedRequestData.returnStatus,
      
      // Machinery-specific fields
      machineryId: cleanedRequestData.machineryId,
      machineryName: cleanedRequestData.machineryName,
      machineryType: cleanedRequestData.machineryType,
      requestSubType: cleanedRequestData.requestSubType,
      targetFarmId: cleanedRequestData.targetFarmId,
      targetFarmName: cleanedRequestData.targetFarmName,
      startDate: cleanedRequestData.startDate,
      endDate: cleanedRequestData.endDate,
      fuelAmount: cleanedRequestData.fuelAmount,
      litterPrice: cleanedRequestData.litterPrice,
      totalPrice: cleanedRequestData.totalPrice,
      maintenanceType: cleanedRequestData.maintenanceType,
      maintenancePrice: cleanedRequestData.maintenancePrice,
      odometerReading: cleanedRequestData.odometerReading,
      returnOdometerReading: cleanedRequestData.returnOdometerReading,
      returnCondition: cleanedRequestData.returnCondition,
      returnImageUrl: cleanedRequestData.returnImageUrl,
      urgency: cleanedRequestData.urgency,
    };
    
    return createdRequest;
  } catch (error) {
    console.error("Error creating request:", error);
    throw error;
  }
};

// Create machinery request (for caretaker -> admin flow)
export const createMachineryRequest = async (requestData: Partial<Request>): Promise<Request> => {
  try {
    if (!requestData.farmId) {
      throw new Error("Farm ID is required");
    }
    
    // Set request type to machinery and ensure proper flow
    const machineryRequestData = {
      ...requestData,
      requestType: "machinery" as const,
      // Caretaker requests go to admin first
      status: "pending" as const,
    };
    
    return await createRequest(machineryRequestData);
  } catch (error) {
    console.error("Error creating machinery request:", error);
    throw error;
  }
};

// Update a request
export const updateRequest = async (requestId: string, requestData: Partial<Request>, farmId: string): Promise<Request> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }
    
    // Get the current request
    const requestRef = doc(db, "farms", farmId, "requests", requestId);
    const requestDoc = await getDoc(requestRef);
    
    if (!requestDoc.exists()) {
      throw new Error("Request not found");
    }
    
    const currentData = requestDoc.data() as FirestoreRequestData;
    
    // Create the updated request with all fields
    const updatedRequestData: Record<string, any> = {
      farmId: farmId,
      itemName: requestData.itemName !== undefined ? requestData.itemName : currentData.itemName,
      quantity: requestData.quantity !== undefined ? requestData.quantity : currentData.quantity,
      unit: requestData.unit !== undefined ? requestData.unit : currentData.unit,
      farmName: requestData.farmName !== undefined ? requestData.farmName : currentData.farmName,
      requestedBy: requestData.requestedBy !== undefined ? requestData.requestedBy : currentData.requestedBy,
      requestedByName: requestData.requestedByName !== undefined ? requestData.requestedByName : currentData.requestedByName,
      requestorRole: requestData.requestorRole !== undefined ? requestData.requestorRole : currentData.requestorRole,
      status: requestData.status !== undefined ? requestData.status : currentData.status,
      reason: requestData.reason !== undefined ? requestData.reason : currentData.reason,
      notes: requestData.notes !== undefined ? requestData.notes : currentData.notes,
      requestType: requestData.requestType !== undefined ? requestData.requestType : currentData.requestType,
      category: requestData.category !== undefined ? requestData.category : currentData.category,
      approvedBy: requestData.approvedBy !== undefined ? requestData.approvedBy : currentData.approvedBy,
      approvedByName: requestData.approvedByName !== undefined ? requestData.approvedByName : currentData.approvedByName,
      approvedAt: requestData.approvedAt !== undefined ? requestData.approvedAt : currentData.approvedAt,
      rejectedBy: requestData.rejectedBy !== undefined ? requestData.rejectedBy : currentData.rejectedBy,
      rejectedByName: requestData.rejectedByName !== undefined ? requestData.rejectedByName : currentData.rejectedByName,
      rejectedAt: requestData.rejectedAt !== undefined ? requestData.rejectedAt : currentData.rejectedAt,
      rejectionReason: requestData.rejectionReason !== undefined ? requestData.rejectionReason : currentData.rejectionReason,
      updatedAt: new Date().toISOString(),
      imageUrl: requestData.imageUrl !== undefined ? requestData.imageUrl : currentData.imageUrl,
      itemId: requestData.itemId !== undefined ? requestData.itemId : currentData.itemId,
      isTransferRequest: requestData.isTransferRequest !== undefined ? requestData.isTransferRequest : currentData.isTransferRequest,
      
      // Return-related fields
      canReturn: requestData.canReturn !== undefined ? requestData.canReturn : currentData.canReturn,
      hasReturn: requestData.hasReturn !== undefined ? requestData.hasReturn : currentData.hasReturn,
      returnId: requestData.returnId !== undefined ? requestData.returnId : currentData.returnId,
      returnStatus: requestData.returnStatus !== undefined ? requestData.returnStatus : currentData.returnStatus,
      
      // Machinery-specific fields
      machineryId: requestData.machineryId !== undefined ? requestData.machineryId : currentData.machineryId,
      machineryName: requestData.machineryName !== undefined ? requestData.machineryName : currentData.machineryName,
      machineryType: requestData.machineryType !== undefined ? requestData.machineryType : currentData.machineryType,
      requestSubType: requestData.requestSubType !== undefined ? requestData.requestSubType : currentData.requestSubType,
      targetFarmId: requestData.targetFarmId !== undefined ? requestData.targetFarmId : currentData.targetFarmId,
      targetFarmName: requestData.targetFarmName !== undefined ? requestData.targetFarmName : currentData.targetFarmName,
      startDate: requestData.startDate !== undefined ? requestData.startDate : currentData.startDate,
      endDate: requestData.endDate !== undefined ? requestData.endDate : currentData.endDate,
      fuelAmount: requestData.fuelAmount !== undefined ? requestData.fuelAmount : currentData.fuelAmount,
      litterPrice: requestData.litterPrice !== undefined ? requestData.litterPrice : currentData.litterPrice,
      totalPrice: requestData.totalPrice !== undefined ? requestData.totalPrice : currentData.totalPrice,
      maintenanceType: requestData.maintenanceType !== undefined ? requestData.maintenanceType : currentData.maintenanceType,
      maintenancePrice: requestData.maintenancePrice !== undefined ? requestData.maintenancePrice : currentData.maintenancePrice,
      odometerReading: requestData.odometerReading !== undefined ? requestData.odometerReading : currentData.odometerReading,
      returnOdometerReading: requestData.returnOdometerReading !== undefined ? requestData.returnOdometerReading : currentData.returnOdometerReading,
      returnCondition: requestData.returnCondition !== undefined ? requestData.returnCondition : currentData.returnCondition,
      returnImageUrl: requestData.returnImageUrl !== undefined ? requestData.returnImageUrl : currentData.returnImageUrl,
      urgency: requestData.urgency !== undefined ? requestData.urgency : currentData.urgency,
    };
    
    // Remove undefined values
    const cleanedRequestData: Record<string, any> = {};
    Object.entries(updatedRequestData).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        cleanedRequestData[key] = value;
      }
    });
    
    // Update in Firestore
    await updateDoc(requestRef, cleanedRequestData);
    
    const result: Request = {
      id: requestId,
      itemName: cleanedRequestData.itemName || "",
      quantity: cleanedRequestData.quantity || 0,
      unit: cleanedRequestData.unit || "",
      farmId: farmId,
      farmName: cleanedRequestData.farmName,
      requestedBy: cleanedRequestData.requestedBy || "",
      requestedByName: cleanedRequestData.requestedByName || "",
      requestorRole: cleanedRequestData.requestorRole,
      status: (cleanedRequestData.status) as "pending" | "approved" | "rejected" || "pending",
      reason: cleanedRequestData.reason,
      notes: cleanedRequestData.notes,
      requestType: (cleanedRequestData.requestType) as "inventory" | "existing" | "machinery" | undefined,
      category: cleanedRequestData.category,
      approvedBy: cleanedRequestData.approvedBy,
      approvedByName: cleanedRequestData.approvedByName,
      approvedAt: cleanedRequestData.approvedAt,
      rejectedBy: cleanedRequestData.rejectedBy,
      rejectedByName: cleanedRequestData.rejectedByName,
      rejectedAt: cleanedRequestData.rejectedAt,
      rejectionReason: cleanedRequestData.rejectionReason,
      createdAt: currentData.createdAt || new Date().toISOString(),
      updatedAt: cleanedRequestData.updatedAt,
      imageUrl: cleanedRequestData.imageUrl,
      itemId: cleanedRequestData.itemId,
      isTransferRequest: cleanedRequestData.isTransferRequest,
      
      // Return-related fields
      canReturn: cleanedRequestData.canReturn,
      hasReturn: cleanedRequestData.hasReturn,
      returnId: cleanedRequestData.returnId,
      returnStatus: cleanedRequestData.returnStatus as "pending" | "approved" | "rejected" | undefined,
      
      // Machinery-specific fields
      machineryId: cleanedRequestData.machineryId,
      machineryName: cleanedRequestData.machineryName,
      machineryType: cleanedRequestData.machineryType,
      requestSubType: (cleanedRequestData.requestSubType) as "use" | "maintenance" | "transfer" | undefined,
      targetFarmId: cleanedRequestData.targetFarmId,
      targetFarmName: cleanedRequestData.targetFarmName,
      startDate: cleanedRequestData.startDate,
      endDate: cleanedRequestData.endDate,
      fuelAmount: cleanedRequestData.fuelAmount,
      litterPrice: cleanedRequestData.litterPrice,
      totalPrice: cleanedRequestData.totalPrice,
      maintenanceType: (cleanedRequestData.maintenanceType) as "routine" | "emergency" | "repair" | undefined,
      maintenancePrice: cleanedRequestData.maintenancePrice,
      odometerReading: cleanedRequestData.odometerReading,
      returnOdometerReading: cleanedRequestData.returnOdometerReading,
      returnCondition: cleanedRequestData.returnCondition,
      returnImageUrl: cleanedRequestData.returnImageUrl,
      urgency: (cleanedRequestData.urgency) as "low" | "medium" | "high" | "emergency" | undefined,
    };
    
    return result;
  } catch (error) {
    console.error("Error updating request:", error);
    throw error;
  }
};

// Delete a request
export const deleteRequest = async (requestId: string, farmId: string): Promise<boolean> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }
    
    // Delete from Firestore
    const requestRef = doc(db, "farms", farmId, "requests", requestId);
    await deleteDoc(requestRef);
    
    return true;
  } catch (error) {
    console.error("Error deleting request:", error);
    throw error;
  }
};

// Update request status (approve/reject)
export const updateRequestStatus = async (
  requestId: string, 
  status: 'approved' | 'rejected', 
  farmId: string, 
  rejectionReason?: string
): Promise<Request> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }

    const requestRef = doc(db, "farms", farmId, "requests", requestId);
    const requestDoc = await getDoc(requestRef);
    
    if (!requestDoc.exists()) {
      throw new Error("Request not found");
    }

    const data = requestDoc.data() as FirestoreRequestData;
    
    // Update request status
    const updateData: Record<string, any> = {
      status,
      updatedAt: new Date().toISOString(),
    };

    if (status === 'approved') {
      updateData.approvedAt = new Date().toISOString();
      // Enable return for approved inventory requests
      if (data.requestType === 'inventory' || data.requestType === 'existing') {
        updateData.canReturn = true;
      }
      // Note: approvedBy and approvedByName should be set by the caller
    } else if (status === 'rejected') {
      updateData.rejectedAt = new Date().toISOString();
      if (rejectionReason) {
        updateData.rejectionReason = rejectionReason;
      }
      // Note: rejectedBy and rejectedByName should be set by the caller
    }
    
    await updateDoc(requestRef, updateData);
    
    const result: Request = {
      id: requestDoc.id,
      itemName: data.itemName || "",
      quantity: data.quantity || 0,
      unit: data.unit || "",
      farmId: farmId,
      farmName: data.farmName,
      requestedBy: data.requestedBy || "",
      requestedByName: data.requestedByName || "",
      requestorRole: data.requestorRole,
      status: status,
      reason: data.reason,
      notes: data.notes,
      requestType: data.requestType as "inventory" | "existing" | "machinery" | undefined,
      category: data.category,
      approvedBy: data.approvedBy,
      approvedByName: data.approvedByName,
      approvedAt: updateData.approvedAt || data.approvedAt,
      rejectedBy: data.rejectedBy,
      rejectedByName: data.rejectedByName,
      rejectedAt: updateData.rejectedAt || data.rejectedAt,
      rejectionReason: updateData.rejectionReason || data.rejectionReason,
      createdAt: data.createdAt || new Date().toISOString(),
      updatedAt: updateData.updatedAt,
      imageUrl: data.imageUrl,
      itemId: data.itemId,
      isTransferRequest: data.isTransferRequest,
      
      // Return-related fields
      canReturn: updateData.canReturn || data.canReturn,
      hasReturn: data.hasReturn,
      returnId: data.returnId,
      returnStatus: data.returnStatus as "pending" | "approved" | "rejected" | undefined,
      
      // Machinery-specific fields
      machineryId: data.machineryId,
      machineryName: data.machineryName,
      machineryType: data.machineryType,
      requestSubType: data.requestSubType as "use" | "maintenance" | "transfer" | undefined,
      targetFarmId: data.targetFarmId,
      targetFarmName: data.targetFarmName,
      startDate: data.startDate,
      endDate: data.endDate,
      fuelAmount: data.fuelAmount,
      litterPrice: data.litterPrice,
      totalPrice: data.totalPrice,
      maintenanceType: data.maintenanceType as "routine" | "emergency" | "repair" | undefined,
      maintenancePrice: data.maintenancePrice,
      odometerReading: data.odometerReading,
      returnOdometerReading: data.returnOdometerReading,
      returnCondition: data.returnCondition,
      returnImageUrl: data.returnImageUrl,
      urgency: data.urgency as "low" | "medium" | "high" | "emergency" | undefined,
    };
    
    return result;
  } catch (error) {
    console.error("Error updating request status:", error);
    throw error;
  }
};

// Approve a request
export const approveRequest = async (requestId: string, approvedBy: string, approvedByName: string, farmId: string): Promise<Request> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }

    const requestRef = doc(db, "farms", farmId, "requests", requestId);
    const requestDoc = await getDoc(requestRef);
    
    if (!requestDoc.exists()) {
      throw new Error("Request not found");
    }

    const data = requestDoc.data() as FirestoreRequestData;
    
    // Update request status first
    const updateData = {
      status: "approved",
      approvedBy,
      approvedByName,
      approvedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      // Enable return for approved inventory requests
      canReturn: (data.requestType === 'inventory' || data.requestType === 'existing') ? true : (data.canReturn || false),
    };
    
    await updateDoc(requestRef, updateData);

    // Create allocation for approved requests
    try {
      const { createAllocation } = await import('./allocation-service');
      const { getFarmById } = await import('./farm-service');
      const { getUserById } = await import('./user-service');

      const farm = await getFarmById(farmId);
      const requestedUser = await getUserById(data.requestedBy);
      const approverUser = await getUserById(approvedBy);

      if (farm && requestedUser && approverUser) {
        // Determine allocation type and item details
        let allocationType: 'inventory' | 'machinery';
        let itemId: string;
        let itemName: string;
        let quantity: number | undefined;
        let unit: string | undefined;

        if (data.requestType === 'inventory' || data.requestType === 'existing') {
          allocationType = 'inventory';
          itemId = data.itemId || data.id || `temp_inventory_${Date.now()}`;
          itemName = data.itemName || 'Unknown Item';
          quantity = data.quantity || 1;
          unit = data.unit || 'units';
        } else if (data.requestType === 'machinery') {
          allocationType = 'machinery';
          itemId = data.machineryId || data.id || `temp_machinery_${Date.now()}`;
          itemName = data.machineryName || 'Unknown Machinery';
          quantity = 1; // Machinery typically has quantity of 1
          unit = 'unit';
        } else {
          // Skip allocation creation for other request types
          console.log('Skipping allocation creation for request type:', data.requestType);
          return;
        }

        const allocationData = {
          farmId: farmId,
          farmName: farm.name || 'Unknown Farm',
          type: allocationType,
          itemId: itemId,
          itemName: itemName,
          quantity: quantity,
          unit: unit,
          returnedQuantity: 0,
          remainingQuantity: quantity,
          allocatedTo: data.requestedBy || 'unknown',
          allocatedToName: requestedUser.displayName || requestedUser.email || 'Unknown User',
          allocatedToRole: requestedUser.role || 'unknown',
          allocatedBy: approvedBy,
          allocatedByName: approverUser.displayName || approverUser.email || 'Unknown User',
          allocatedByRole: approverUser.role || 'unknown',
          allocationDate: new Date().toISOString(),
          expectedReturnDate: data.endDate,
          requestId: requestId,
          allocationNotes: `Allocated from approved request: ${data.reason || 'No reason provided'}`,
          // Add rate information if available
          dailyRate: data.dailyRate,
          weeklyRate: data.weeklyRate,
          monthlyRate: data.monthlyRate,
          rateType: data.rateType,
          customRate: data.customRate,
        };

        await createAllocation(allocationData);
        console.log('Allocation created for approved request:', requestId);

        // Create user expense for approved request
        try {
          const { createUserExpenseForApprovedRequest, createMachineryExpense } = await import('./expense-service');

          if (allocationType === 'inventory') {
            // Always fetch inventory item to check if it's consumable
            let inventoryItem = null;
            let unitPrice = data.price;

            try {
              if (data.requestType === 'existing' && data.itemId) {
                // For existing items, get item details from inventory
                const { getInventoryItemById } = await import('./inventory-service');
                inventoryItem = await getInventoryItemById(data.itemId, farmId);

                // Get price from inventory item if not in request
                if ((!unitPrice || unitPrice <= 0) && inventoryItem && inventoryItem.price && inventoryItem.price > 0) {
                  unitPrice = inventoryItem.price;
                  console.log(`Got price from inventory item: Rs. ${unitPrice}`);
                }
              } else if (data.requestType === 'inventory') {
                // For new inventory requests, try to find similar items by name and category
                if (!unitPrice || unitPrice <= 0) {
                  const { getInventoryItems } = await import('./inventory-service');
                  const allItems = await getInventoryItems(farmId);
                  const similarItem = allItems.find(item =>
                    item.name.toLowerCase() === data.itemName?.toLowerCase() &&
                    item.category === data.category &&
                    item.price && item.price > 0
                  );
                  if (similarItem) {
                    inventoryItem = similarItem; // Use similar item for consumable check
                    unitPrice = similarItem.price;
                    console.log(`Got price from similar item: Rs. ${unitPrice}`);
                  }
                }
              }
            } catch (priceError) {
              console.error('Error getting item details:', priceError);
            }

            // Check if item is consumable before creating expense
            // For existing items: use actual inventory item's isConsumable property
            // For new items: default to true (consumable) unless we found a similar non-consumable item
            const isConsumable = inventoryItem ? (inventoryItem.isConsumable !== false) : true;

            console.log(`Item: ${itemName}, isConsumable: ${isConsumable}, unitPrice: ${unitPrice}`);

            if (isConsumable) {
              // Create expense only for consumable items
              if (unitPrice && unitPrice > 0) {
                await createUserExpenseForApprovedRequest(
                  farmId,
                  farm.name,
                  requestId,
                  data.requestedBy,
                  requestedUser.displayName || requestedUser.email || 'Unknown User',
                  requestedUser.role || 'unknown',
                  'inventory',
                  itemId,
                  itemName,
                  quantity || 1,
                  unitPrice,
                  approvedBy,
                  approverUser.displayName || approverUser.email || 'Unknown User'
                );
                console.log(`✅ EXPENSE CREATED for consumable item "${itemName}": Rs. ${unitPrice * (quantity || 1)}`);
              } else {
                // Create a zero-cost expense entry for tracking purposes
                console.log('No price found, creating zero-cost expense for consumable item tracking');
                await createUserExpenseForApprovedRequest(
                  farmId,
                  farm.name,
                  requestId,
                  data.requestedBy,
                  requestedUser.displayName || requestedUser.email || 'Unknown User',
                  requestedUser.role || 'unknown',
                  'inventory',
                  itemId,
                  itemName,
                  quantity || 1,
                  0, // Zero cost
                  approvedBy,
                  approverUser.displayName || approverUser.email || 'Unknown User'
                );
                console.log(`✅ ZERO-COST EXPENSE CREATED for consumable item "${itemName}" (tracking purposes)`);
              }
            } else {
              console.log(`❌ NO EXPENSE CREATED for non-consumable item "${itemName}" (isConsumable: false, will be returned)`);
            }
          } else if (allocationType === 'machinery') {
            // For machinery requests, check if there are additional costs
            if (data.requestSubType === 'use' && data.totalPrice && data.totalPrice > 0) {
              // For use requests, create fuel expense using totalPrice
              await createMachineryExpense(
                farmId,
                farm.name,
                requestId,
                data.requestedBy,
                requestedUser.displayName || requestedUser.email || 'Unknown User',
                requestedUser.role || 'unknown',
                itemId,
                itemName,
                'fuel',
                data.totalPrice,
                `Fuel for machinery use: ${data.fuelAmount || 0}L × Rs ${data.litterPrice || 0}/L`,
                approvedBy,
                approverUser.displayName || approverUser.email || 'Unknown User'
              );
            } else if ((data.requestSubType as any) === 'fuel' && (data as any).price && (data as any).price > 0) {
              // Legacy fuel requests (backward compatibility)
              await createMachineryExpense(
                farmId,
                farm.name,
                requestId,
                data.requestedBy,
                requestedUser.displayName || requestedUser.email || 'Unknown User',
                requestedUser.role || 'unknown',
                itemId,
                itemName,
                'fuel',
                (data as any).price,
                data.reason || 'Fuel request (legacy)',
                approvedBy,
                approverUser.displayName || approverUser.email || 'Unknown User'
              );
            } else if (data.requestSubType === 'maintenance' && data.maintenancePrice && data.maintenancePrice > 0) {
              // For maintenance requests, create maintenance expense using maintenancePrice
              await createMachineryExpense(
                farmId,
                farm.name,
                requestId,
                data.requestedBy,
                requestedUser.displayName || requestedUser.email || 'Unknown User',
                requestedUser.role || 'unknown',
                itemId,
                itemName,
                'maintenance',
                data.maintenancePrice,
                data.reason || 'Maintenance request',
                approvedBy,
                approverUser.displayName || approverUser.email || 'Unknown User'
              );
            }
          }
        } catch (expenseError) {
          console.error('Error creating expense for approved request:', expenseError);
          // Don't fail the approval if expense creation fails
        }
      }
    } catch (allocationError) {
      console.error('Error creating allocation for approved request:', allocationError);
      // Don't fail the approval if allocation creation fails
    }

    // Handle machinery status update with better error handling
    if (data.requestType === 'machinery' && (data.requestSubType === 'use' || data.requestSubType === 'maintenance') && data.machineryName) {
      try {
        console.log("Attempting to update machinery status for request:", requestId, "subType:", data.requestSubType);

        // Import machinery service functions
        const machineryService = await import("./machinery-service");
        const { getMachineryByFarm, updateMachineryStatus } = machineryService;

        // Get machinery list for the farm
        const machineryList = await getMachineryByFarm(farmId);
        console.log("Found machinery list:", machineryList.length, "items");
        
        // Find the machinery by name (case-insensitive and flexible matching)
        const machinery = machineryList.find(m => {
          if (!m.name || !data.machineryName) return false;
          
          const machineryNameLower = m.name.toLowerCase().trim();
          const requestNameLower = data.machineryName.toLowerCase().trim();
          
          // Exact match
          if (machineryNameLower === requestNameLower) return true;
          
          // Partial match (contains)
          if (machineryNameLower.includes(requestNameLower) || requestNameLower.includes(machineryNameLower)) return true;
          
          return false;
        });
        
        if (machinery) {
          console.log("Found matching machinery:", machinery.name, "ID:", machinery.id);
          
          // Update machinery status to in_use
          await updateMachineryStatus(machinery.id, farmId, 'in_use');
          
          // Update the request with machinery ID
          await updateDoc(requestRef, {
            machineryId: machinery.id,
            updatedAt: new Date().toISOString(),
          });
          
          console.log(`Successfully updated machinery ${machinery.name} status to in_use for ${data.requestSubType} request`);
        } else {
          console.warn(`No machinery found with name: ${data.machineryName}`);
          console.log("Available machinery names:", machineryList.map(m => m.name));
        }
      } catch (machineryError) {
        console.error("Error updating machinery status after request approval:", machineryError);
        // Don't throw error here, request approval should still succeed
        // The machinery status can be updated manually if needed
      }
    }
    
    // Handle inventory update with better error handling
    if ((data.requestType === 'inventory' || data.requestType === 'existing') && data.itemId && data.quantity) {
      try {
        console.log("Attempting to update inventory for request:", requestId);
        
        // Import inventory service functions
        const inventoryService = await import("./inventory-service");
        const { getInventoryItemById, updateInventoryItem } = inventoryService;
        
        // Get current inventory item
        const inventoryItem = await getInventoryItemById(data.itemId, farmId);
        
        if (inventoryItem) {
          // Reduce inventory quantity
          const newQuantity = Math.max(0, inventoryItem.quantity - (data.quantity || 0));
          
          await updateInventoryItem(data.itemId, {
            quantity: newQuantity,
            location: farmId,
          }, approvedBy);
          
          console.log(`Successfully updated inventory: ${inventoryItem.name} quantity reduced from ${inventoryItem.quantity} to ${newQuantity}`);
        } else {
          console.warn(`No inventory item found with ID: ${data.itemId}`);
        }
      } catch (inventoryError) {
        console.error("Error updating inventory after request approval:", inventoryError);
        // Don't throw error here, request approval should still succeed
        // The inventory can be updated manually if needed
      }
    }
    
    const result: Request = {
      id: requestDoc.id,
      itemName: data.itemName || "",
      quantity: data.quantity || 0,
      unit: data.unit || "",
      farmId: farmId,
      farmName: data.farmName,
      requestedBy: data.requestedBy || "",
      requestedByName: data.requestedByName || "",
      requestorRole: data.requestorRole,
      status: "approved",
      reason: data.reason,
      notes: data.notes,
      requestType: data.requestType as "inventory" | "existing" | "machinery" | undefined,
      category: data.category,
      approvedBy,
      approvedByName,
      approvedAt: updateData.approvedAt,
      rejectedBy: data.rejectedBy,
      rejectedByName: data.rejectedByName,
      rejectedAt: data.rejectedAt,
      rejectionReason: data.rejectionReason,
      createdAt: data.createdAt || new Date().toISOString(),
      updatedAt: updateData.updatedAt,
      imageUrl: data.imageUrl,
      itemId: data.itemId,
      isTransferRequest: data.isTransferRequest,
      
      // Return-related fields
      canReturn: updateData.canReturn,
      hasReturn: data.hasReturn,
      returnId: data.returnId,
      returnStatus: data.returnStatus as "pending" | "approved" | "rejected" | undefined,
      
      // Machinery-specific fields
      machineryId: data.machineryId,
      machineryName: data.machineryName,
      machineryType: data.machineryType,
      requestSubType: data.requestSubType as "use" | "maintenance" | "transfer" | undefined,
      targetFarmId: data.targetFarmId,
      targetFarmName: data.targetFarmName,
      startDate: data.startDate,
      endDate: data.endDate,
      fuelAmount: data.fuelAmount,
      litterPrice: data.litterPrice,
      totalPrice: data.totalPrice,
      maintenanceType: data.maintenanceType as "routine" | "emergency" | "repair" | undefined,
      maintenancePrice: data.maintenancePrice,
      odometerReading: data.odometerReading,
      returnOdometerReading: data.returnOdometerReading,
      returnCondition: data.returnCondition,
      returnImageUrl: data.returnImageUrl,
      urgency: data.urgency as "low" | "medium" | "high" | "emergency" | undefined,
    };
    
    return result;
  } catch (error) {
    console.error("Error approving request:", error);
    throw error;
  }
};

// Reject a request
export const rejectRequest = async (requestId: string, rejectedBy: string, rejectedByName: string, rejectionReason: string, farmId: string): Promise<Request> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }

    const requestRef = doc(db, "farms", farmId, "requests", requestId);
    const requestDoc = await getDoc(requestRef);
    
    if (!requestDoc.exists()) {
      throw new Error("Request not found");
    }

    const data = requestDoc.data() as FirestoreRequestData;
    
    // Update request status
    const updateData = {
      status: "rejected",
      rejectedBy,
      rejectedByName,
      rejectedAt: new Date().toISOString(),
      rejectionReason,
      updatedAt: new Date().toISOString(),
    };
    
    await updateDoc(requestRef, updateData);
    
    const result: Request = {
      id: requestDoc.id,
      itemName: data.itemName || "",
      quantity: data.quantity || 0,
      unit: data.unit || "",
      farmId: farmId,
      farmName: data.farmName,
      requestedBy: data.requestedBy || "",
      requestedByName: data.requestedByName || "",
      requestorRole: data.requestorRole,
      status: "rejected",
      reason: data.reason,
      notes: data.notes,
      requestType: data.requestType as "inventory" | "existing" | "machinery" | undefined,
      category: data.category,
      approvedBy: data.approvedBy,
      approvedByName: data.approvedByName,
      approvedAt: data.approvedAt,
      rejectedBy,
      rejectedByName,
      rejectedAt: updateData.rejectedAt,
      rejectionReason,
      createdAt: data.createdAt || new Date().toISOString(),
      updatedAt: updateData.updatedAt,
      imageUrl: data.imageUrl,
      itemId: data.itemId,
      isTransferRequest: data.isTransferRequest,
      
      // Return-related fields
      canReturn: data.canReturn,
      hasReturn: data.hasReturn,
      returnId: data.returnId,
      returnStatus: data.returnStatus as "pending" | "approved" | "rejected" | undefined,
      
      // Machinery-specific fields
      machineryId: data.machineryId,
      machineryName: data.machineryName,
      machineryType: data.machineryType,
      requestSubType: data.requestSubType as "use" | "maintenance" | "transfer" | undefined,
      targetFarmId: data.targetFarmId,
      targetFarmName: data.targetFarmName,
      startDate: data.startDate,
      endDate: data.endDate,
      fuelAmount: data.fuelAmount,
      litterPrice: data.litterPrice,
      totalPrice: data.totalPrice,
      maintenanceType: data.maintenanceType as "routine" | "emergency" | "repair" | undefined,
      maintenancePrice: data.maintenancePrice,
      odometerReading: data.odometerReading,
      returnOdometerReading: data.returnOdometerReading,
      returnCondition: data.returnCondition,
      returnImageUrl: data.returnImageUrl,
      urgency: data.urgency as "low" | "medium" | "high" | "emergency" | undefined,
    };
    
    return result;
  } catch (error) {
    console.error("Error rejecting request:", error);
    throw error;
  }
};

// ===== INVENTORY RETURN FUNCTIONS =====

// Create inventory return
export const createInventoryReturn = async (returnData: {
  requestId: string;
  caretakerId: string;
  caretakerName: string;
  itemId?: string;
  itemName: string;
  quantityReturned: number;
  quantityApproved: number;
  condition: "good" | "used" | "damaged";
  remarks?: string;
  farmId: string;
  farmName?: string;
}): Promise<InventoryReturn> => {
  try {
    if (!returnData.farmId) {
      throw new Error("Farm ID is required");
    }
    
    const farmId = returnData.farmId;
    
    // Create the new return data object, filtering out undefined values
    const newReturnData: Record<string, any> = {
      requestId: returnData.requestId,
      caretakerId: returnData.caretakerId,
      caretakerName: returnData.caretakerName,
      itemName: returnData.itemName,
      quantityReturned: returnData.quantityReturned,
      quantityApproved: returnData.quantityApproved,
      condition: returnData.condition,
      returnedAt: new Date().toISOString(),
      returnStatus: "pending",
      farmId: farmId,
      createdAt: new Date().toISOString(),
    };
    
    // Only add itemId if it exists and is not undefined
    if (returnData.itemId && returnData.itemId.trim()) {
      newReturnData.itemId = returnData.itemId.trim();
    }
    
    // Only add optional fields if they have values
    if (returnData.remarks && returnData.remarks.trim()) {
      newReturnData.remarks = returnData.remarks.trim();
    }
    
    if (returnData.farmName && returnData.farmName.trim()) {
      newReturnData.farmName = returnData.farmName.trim();
    }
    
    // Add to Firestore
    const returnsRef = collection(db, "farms", farmId, "inventoryReturns");
    const docRef = await addDoc(returnsRef, newReturnData);
    
    // Update the original request to mark it has a return
    const requestRef = doc(db, "farms", farmId, "requests", returnData.requestId);
    await updateDoc(requestRef, {
      hasReturn: true,
      returnId: docRef.id,
      returnStatus: "pending",
      updatedAt: new Date().toISOString(),
    });
    
    const createdReturn: InventoryReturn = {
      id: docRef.id,
      requestId: newReturnData.requestId,
      caretakerId: newReturnData.caretakerId,
      caretakerName: newReturnData.caretakerName,
      itemId: newReturnData.itemId,
      itemName: newReturnData.itemName,
      quantityReturned: newReturnData.quantityReturned,
      quantityApproved: newReturnData.quantityApproved,
      condition: newReturnData.condition,
      remarks: newReturnData.remarks,
      returnedAt: newReturnData.returnedAt,
      returnStatus: newReturnData.returnStatus,
      farmId: newReturnData.farmId,
      farmName: newReturnData.farmName,
      createdAt: newReturnData.createdAt,
    };
    
    return createdReturn;
  } catch (error) {
    console.error("Error creating inventory return:", error);
    throw error;
  }
};

// Get inventory returns for a farm
export const getInventoryReturns = async (
  farmId: string,
  status?: "pending" | "approved" | "rejected"
): Promise<InventoryReturn[]> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }

    const returns: InventoryReturn[] = [];
    
    // Get returns for this specific farm
    const returnsRef = collection(db, "farms", farmId, "inventoryReturns");
    
    // Build query based on filters
    let returnsQuery: any = returnsRef;
    
    if (status) {
      returnsQuery = query(returnsRef, where("returnStatus", "==", status));
    }
    
    const returnsSnapshot = await getDocs(returnsQuery);
    
    returnsSnapshot.forEach(docSnapshot => {
      const data = docSnapshot.data() as FirestoreInventoryReturnData;
      
      returns.push({
        id: docSnapshot.id,
        requestId: data.requestId || "",
        caretakerId: data.caretakerId || "",
        caretakerName: data.caretakerName || "",
        itemId: data.itemId,
        itemName: data.itemName || "",
        quantityReturned: data.quantityReturned || 0,
        quantityApproved: data.quantityApproved || 0,
        condition: (data.condition as "good" | "used" | "damaged") || "good",
        remarks: data.remarks,
        returnedAt: data.returnedAt || new Date().toISOString(),
        returnStatus: (data.returnStatus as "pending" | "approved" | "rejected") || "pending",
        reviewedBy: data.reviewedBy,
        reviewedByName: data.reviewedByName,
        reviewedAt: data.reviewedAt,
        rejectionReason: data.rejectionReason,
        adminDescription: data.adminDescription,
        farmId: farmId,
        farmName: data.farmName,
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt,
      });
    });
    
    // Sort by createdAt
    returns.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
    
    return returns;
  } catch (error) {
    console.error("Error getting inventory returns:", error);
    return [];
  }
};

// Get inventory return by ID
export const getInventoryReturnById = async (returnId: string, farmId: string): Promise<InventoryReturn | null> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }

    const returnRef = doc(db, "farms", farmId, "inventoryReturns", returnId);
    const returnDoc = await getDoc(returnRef);
    
    if (returnDoc.exists()) {
      const data = returnDoc.data() as FirestoreInventoryReturnData;
      
      return {
        id: returnDoc.id,
        requestId: data.requestId || "",
        caretakerId: data.caretakerId || "",
        caretakerName: data.caretakerName || "",
        itemId: data.itemId,
        itemName: data.itemName || "",
        quantityReturned: data.quantityReturned || 0,
        quantityApproved: data.quantityApproved || 0,
        condition: (data.condition as "good" | "used" | "damaged") || "good",
        remarks: data.remarks,
        returnedAt: data.returnedAt || new Date().toISOString(),
        returnStatus: (data.returnStatus as "pending" | "approved" | "rejected") || "pending",
        reviewedBy: data.reviewedBy,
        reviewedByName: data.reviewedByName,
        reviewedAt: data.reviewedAt,
        rejectionReason: data.rejectionReason,
        adminDescription: data.adminDescription,
        farmId: farmId,
        farmName: data.farmName,
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt,
      };
    }
    
    return null;
  } catch (error) {
    console.error("Error getting inventory return by ID:", error);
    return null;
  }
};

// Approve inventory return
export const approveInventoryReturn = async (
  returnId: string,
  farmId: string,
  reviewedBy: string,
  reviewedByName: string,
  adminDescription?: string,
  updateInventory: boolean = true
): Promise<InventoryReturn> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }

    const returnRef = doc(db, "farms", farmId, "inventoryReturns", returnId);
    const returnDoc = await getDoc(returnRef);
    
    if (!returnDoc.exists()) {
      throw new Error("Return not found");
    }

    const returnData = returnDoc.data() as FirestoreInventoryReturnData;
    
    // Update return status
    const updateData: Record<string, any> = {
      returnStatus: "approved",
      reviewedBy,
      reviewedByName,
      reviewedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    if (adminDescription && adminDescription.trim()) {
      updateData.adminDescription = adminDescription.trim();
    }
    
    await updateDoc(returnRef, updateData);
    
    // Update the original request
    const requestRef = doc(db, "farms", farmId, "requests", returnData.requestId || "");
    await updateDoc(requestRef, {
      returnStatus: "approved",
      updatedAt: new Date().toISOString(),
    });

    // CRITICAL FIX: Update allocation status when inventory return is approved
    try {
      const { updateAllocation, getAllocationsByRequestId } = await import('./allocation-service');

      // Find the allocation for this request
      const allocations = await getAllocationsByRequestId(farmId, returnData.requestId || '');

      if (allocations.length > 0) {
        const allocation = allocations[0]; // Should only be one allocation per request

        // Calculate new quantities
        const currentReturnedQty = allocation.returnedQuantity || 0;
        const newReturnedQuantity = currentReturnedQty + returnData.quantityReturned;
        const newRemainingQuantity = (allocation.quantity || 0) - newReturnedQuantity;

        // Determine new status
        let newStatus: 'allocated' | 'partially_returned' | 'returned' = 'allocated';
        if (newRemainingQuantity <= 0) {
          newStatus = 'returned';
        } else if (newReturnedQuantity > 0) {
          newStatus = 'partially_returned';
        }

        // Prepare update data, filtering out undefined values
        const updateData: Record<string, any> = {
          status: newStatus,
          returnedQuantity: newReturnedQuantity,
          remainingQuantity: newRemainingQuantity,
          actualReturnDate: newStatus === 'returned' ? new Date().toISOString() : allocation.actualReturnDate,
          condition: returnData.condition,
          updatedAt: new Date().toISOString(),
        };

        // Only add returnNotes if it has a value
        if (returnData.remarks && returnData.remarks.trim()) {
          updateData.returnNotes = returnData.remarks.trim();
        }

        // Update allocation status
        await updateAllocation(allocation.id, updateData);

        console.log(`Updated allocation ${allocation.id} status to '${newStatus}' after inventory return approval`);
      } else {
        console.warn(`No allocation found for inventory return requestId: ${returnData.requestId}`);
      }
    } catch (allocationError) {
      console.error('Error updating allocation status after inventory return approval:', allocationError);
      // Don't fail the return approval if allocation update fails
    }
    
    // Update inventory if condition is good or used and updateInventory is true
    if (updateInventory && (returnData.condition === "good" || returnData.condition === "used") && returnData.itemId) {
      try {
        const { getInventoryItemById, updateInventoryItem } = await import("./inventory-service");

        // Get current inventory item
        const inventoryItem = await getInventoryItemById(returnData.itemId, farmId);

        if (inventoryItem) {
          const newQuantity = inventoryItem.quantity + (returnData.quantityReturned || 0);

          await updateInventoryItem(returnData.itemId, {
            quantity: newQuantity,
            location: farmId,
          }, reviewedBy);

          console.log(`Updated inventory: ${inventoryItem.name} quantity increased from ${inventoryItem.quantity} to ${newQuantity} (condition: ${returnData.condition})`);
        }
      } catch (error) {
        console.error("Error updating inventory after return approval:", error);
        // Don't throw error here, return approval should still succeed
      }
    }

    // Create expense adjustment for approved return
    try {
      const { adjustUserExpenseForReturn } = await import('./expense-service');

      // Find the original expense for this request
      const { getExpensesByFarm } = await import('./expense-service');
      const expensesResult = await getExpensesByFarm(farmId, {
        type: 'user',
        userId: returnData.caretakerId,
      });

      const userExpense = expensesResult.expenses.find(expense =>
        expense.requestId === returnData.requestId &&
        expense.category === 'inventory'
      );

      if (userExpense) {
        await adjustUserExpenseForReturn(
          farmId,
          userExpense.id,
          returnData.quantityReturned || 0,
          returnData.quantityApproved || 0,
          returnData.condition,
          reviewedBy,
          reviewedByName
        );
        console.log('Expense adjustment created for approved return');
      }
    } catch (expenseError) {
      console.error('Error creating expense adjustment for return:', expenseError);
      // Don't fail the return approval if expense adjustment fails
    }

    const result: InventoryReturn = {
      id: returnDoc.id,
      requestId: returnData.requestId || "",
      caretakerId: returnData.caretakerId || "",
      caretakerName: returnData.caretakerName || "",
      itemId: returnData.itemId,
      itemName: returnData.itemName || "",
      quantityReturned: returnData.quantityReturned || 0,
      quantityApproved: returnData.quantityApproved || 0,
      condition: (returnData.condition as "good" | "used" | "damaged") || "good",
      remarks: returnData.remarks,
      returnedAt: returnData.returnedAt || new Date().toISOString(),
      returnStatus: "approved",
      reviewedBy,
      reviewedByName,
      reviewedAt: updateData.reviewedAt,
      rejectionReason: returnData.rejectionReason,
      adminDescription: updateData.adminDescription || returnData.adminDescription,
      farmId: farmId,
      farmName: returnData.farmName,
      createdAt: returnData.createdAt || new Date().toISOString(),
      updatedAt: updateData.updatedAt,
    };
    
    return result;
  } catch (error) {
    console.error("Error approving inventory return:", error);
    throw error;
  }
};

// Reject inventory return
export const rejectInventoryReturn = async (
  returnId: string,
  farmId: string,
  reviewedBy: string,
  reviewedByName: string,
  rejectionReason: string,
  adminDescription?: string
): Promise<InventoryReturn> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }

    const returnRef = doc(db, "farms", farmId, "inventoryReturns", returnId);
    const returnDoc = await getDoc(returnRef);
    
    if (!returnDoc.exists()) {
      throw new Error("Return not found");
    }

    const returnData = returnDoc.data() as FirestoreInventoryReturnData;
    
    // Update return status
    const updateData: Record<string, any> = {
      returnStatus: "rejected",
      reviewedBy,
      reviewedByName,
      reviewedAt: new Date().toISOString(),
      rejectionReason: rejectionReason.trim(),
      updatedAt: new Date().toISOString(),
    };
    
    if (adminDescription && adminDescription.trim()) {
      updateData.adminDescription = adminDescription.trim();
    }
    
    await updateDoc(returnRef, updateData);
    
    // Update the original request
    const requestRef = doc(db, "farms", farmId, "requests", returnData.requestId || "");
    await updateDoc(requestRef, {
      returnStatus: "rejected",
      updatedAt: new Date().toISOString(),
    });
    
    const result: InventoryReturn = {
      id: returnDoc.id,
      requestId: returnData.requestId || "",
      caretakerId: returnData.caretakerId || "",
      caretakerName: returnData.caretakerName || "",
      itemId: returnData.itemId,
      itemName: returnData.itemName || "",
      quantityReturned: returnData.quantityReturned || 0,
      quantityApproved: returnData.quantityApproved || 0,
      condition: (returnData.condition as "good" | "used" | "damaged") || "good",
      remarks: returnData.remarks,
      returnedAt: returnData.returnedAt || new Date().toISOString(),
      returnStatus: "rejected",
      reviewedBy,
      reviewedByName,
      reviewedAt: updateData.reviewedAt,
      rejectionReason: updateData.rejectionReason,
      adminDescription: updateData.adminDescription || returnData.adminDescription,
      farmId: farmId,
      farmName: returnData.farmName,
      createdAt: returnData.createdAt || new Date().toISOString(),
      updatedAt: updateData.updatedAt,
    };
    
    return result;
  } catch (error) {
    console.error("Error rejecting inventory return:", error);
    throw error;
  }
};

// Update request with calculated price information
export const updateRequestPrice = async (
  requestId: string,
  farmId: string,
  unitPrice: number,
  totalPrice: number
): Promise<void> => {
  try {
    const requestRef = doc(db, "farms", farmId, "requests", requestId);

    await updateDoc(requestRef, {
      unitPrice: unitPrice,
      totalPrice: totalPrice,
      updatedAt: new Date().toISOString(),
    });

    console.log("Request price updated successfully");
  } catch (error) {
    console.error("Error updating request price:", error);
    throw error;
  }
};
// OpenRouter Chat Service for handling irrelevant/ambiguous commands
// Uses OpenRouter API to provide intelligent responses for non-farm related queries

// OpenRouter configuration
const OPENROUTER_API_KEY = '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';
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

export interface ChatResponse {
  message: string;
  isRelevant: boolean;
  suggestions?: string[];
  confidence: number;
}

// Analyze if a command is farm-related or irrelevant
export const analyzeCommandRelevance = (command: string): { isRelevant: boolean; confidence: number } => {
  const lowerCommand = command.toLowerCase().trim();
  
  // Farm-related keywords
  const farmKeywords = [
    // English
    'farm', 'inventory', 'machinery', 'tractor', 'harvester', 'seed', 'fertilizer', 
    'pesticide', 'crop', 'livestock', 'cattle', 'chicken', 'feed', 'fuel', 'diesel',
    'planter', 'sprayer', 'cultivator', 'equipment', 'tool', 'barn', 'field',
    'request', 'add', 'create', 'update', 'delete', 'show', 'list', 'help',
    'maintenance', 'repair', 'use', 'borrow', 'need', 'want', 'get',
    // Urdu
    'کھیت', 'فارم', 'ٹریکٹر', 'مشین', 'بیج', 'کھاد', 'کیڑے مار', 'فصل',
    'مویشی', 'گائے', 'مرغی', 'چارہ', 'ایندھن', 'ڈیزل', 'اوزار', 'سامان',
    'درخواست', 'شامل', 'بنانا', 'اپڈیٹ', 'ڈیلیٹ', 'دکھانا', 'فہرست', 'مدد',
    'دیکھ بھال', 'مرمت', 'استعمال', 'چاہیے', 'ضرورت'
  ];

  // Check for farm-related keywords
  const farmKeywordCount = farmKeywords.filter(keyword => 
    lowerCommand.includes(keyword)
  ).length;

  // If contains farm keywords, it's relevant
  if (farmKeywordCount > 0) {
    return { isRelevant: true, confidence: Math.min(0.7 + (farmKeywordCount * 0.1), 0.95) };
  }

  // Check for common irrelevant patterns
  const irrelevantPatterns = [
    /^(hi|hello|hey|سلام|آداب)/i,
    /weather|موسم/i,
    /time|وقت|ٹائم/i,
    /news|خبر/i,
    /joke|مذاق|لطیفہ/i,
    /game|کھیل/i,
    /movie|فلم/i,
    /music|موسیقی/i,
    /food|کھانا/i,
    /sports|کھیل/i,
    /politics|سیاست/i,
    /religion|مذہب/i,
    /personal|ذاتی/i,
    /love|محبت/i,
    /relationship|رشتہ/i,
    /school|سکول/i,
    /college|کالج/i,
    /university|یونیورسٹی/i,
    /job|نوکری/i,
    /money|پیسہ/i,
    /shopping|خریداری/i,
    /travel|سفر/i,
    /health|صحت/i,
    /doctor|ڈاکٹر/i,
    /medicine|دوا/i,
    /hospital|ہسپتال/i
  ];

  // Check for irrelevant patterns
  const hasIrrelevantPattern = irrelevantPatterns.some(pattern => 
    pattern.test(lowerCommand)
  );

  if (hasIrrelevantPattern) {
    return { isRelevant: false, confidence: 0.8 };
  }

  // If no clear indicators, assume it might be relevant but with low confidence
  return { isRelevant: true, confidence: 0.3 };
};

// Handle irrelevant commands with OpenRouter
export const handleIrrelevantCommand = async (command: string): Promise<ChatResponse> => {
  try {
    const systemPrompt = `You are a helpful AI assistant for a farm inventory management app. The user has asked something that is not directly related to farm management, inventory, or machinery.

Please provide a polite, helpful response that:
1. Acknowledges their question
2. Gently redirects them back to farm-related topics
3. Suggests some farm management tasks they could try
4. Keeps the tone friendly and professional
5. Responds in the same language as the user (English or Urdu)

If the user is greeting you, respond warmly and introduce yourself as a farm management assistant.

Keep responses concise (2-3 sentences max) and always end with a farm-related suggestion.`;

    const userPrompt = `User message: "${command}"

Please respond appropriately and redirect to farm management topics.`;

    // Call OpenRouter API
    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://farm-inventory-app.com',
        'X-Title': 'Farm Inventory Control App',
      },
      body: JSON.stringify({
        model: "anthropic/claude-3.5-sonnet",
        messages: [
          {
            role: "system",
            content: systemPrompt
          },
          {
            role: "user",
            content: userPrompt
          }
        ],
        max_tokens: 200,
        temperature: 0.7
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const aiMessage = data.choices[0]?.message?.content || '';

    return {
      message: aiMessage,
      isRelevant: false,
      suggestions: [
        'Show all inventory',
        'Show all machinery', 
        'Add new inventory item',
        'Create a request',
        'Help with farm management'
      ],
      confidence: 0.9
    };

  } catch (error) {
    console.error('Error handling irrelevant command with OpenRouter:', error);
    
    // Fallback response
    return {
      message: "I'm here to help you manage your farm inventory and machinery. What would you like to do today?",
      isRelevant: false,
      suggestions: [
        'Show all inventory',
        'Show all machinery',
        'Add new inventory item',
        'Create a request',
        'Type "help" for more options'
      ],
      confidence: 0.5
    };
  }
};

// Improve response tone and quality using OpenRouter
export const improveResponseTone = async (originalResponse: string, userMessage: string): Promise<string> => {
  try {
    const systemPrompt = `You are an expert at improving the tone and quality of AI assistant responses for a farm inventory management app.

Your task is to take an existing response and make it:
1. More friendly and conversational
2. Clearer and easier to understand
3. More helpful and actionable
4. Appropriate for the farm management context
5. Maintain the same language as the original (English or Urdu)

Keep the core information the same but improve the presentation and tone.`;

    const userPrompt = `Original user message: "${userMessage}"

Original response: "${originalResponse}"

Please improve this response to be more friendly, clear, and helpful while maintaining the same core information.`;

    // Call OpenRouter API
    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://farm-inventory-app.com',
        'X-Title': 'Farm Inventory Control App',
      },
      body: JSON.stringify({
        model: "anthropic/claude-3.5-sonnet",
        messages: [
          {
            role: "system",
            content: systemPrompt
          },
          {
            role: "user",
            content: userPrompt
          }
        ],
        max_tokens: 300,
        temperature: 0.3
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const improvedResponse = data.choices[0]?.message?.content || originalResponse;

    return improvedResponse;

  } catch (error) {
    console.error('Error improving response tone with OpenRouter:', error);
    return originalResponse; // Return original if improvement fails
  }
};

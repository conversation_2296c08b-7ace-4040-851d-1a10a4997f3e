{"expo": {"name": "Farm Inventory Control App", "slug": "farm-inventory-control-app-s0altyt", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "app.rork.farm-inventory-control-app-s0altyt", "buildNumber": "1"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.farm.inventory", "versionCode": 1}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": [["expo-router", {"origin": "https://rork.app/"}], "expo-web-browser", "expo-font"], "experiments": {"typedRoutes": true}, "runtimeVersion": {"policy": "appVersion"}, "updates": {"fallbackToCacheTimeout": 0, "url": "https://u.expo.dev/0834d24d-5bd5-4cf0-9511-f90b5719b4d3", "checkAutomatically": "ON_LOAD", "channel": "production"}}}
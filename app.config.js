export default ({ config }) => {
    const BRANCH = process.env.EXPO_BRANCH || 'development';
    const APP_VERSION = "1.0.0"; // 👈 Change this for each public app release
    const IOS_BUILD_NUMBER = "1"; // 👈 Increment for each iOS store upload
    const ANDROID_VERSION_CODE = 1; // 👈 Increment for each Android store upload
  
    return {
      ...config,
      name: "Farm Inventory Control App",
      slug: "farm-inventory-control-app-s0altyt",
      version: APP_VERSION, // 👈 This is the version users will see
      orientation: "portrait",
      icon: "./assets/images/icon.png",
      scheme: "myapp",
      userInterfaceStyle: "automatic",
      newArchEnabled: true,
      splash: {
        image: "./assets/images/splash-icon.png",
        resizeMode: "contain",
        backgroundColor: "#ffffff"
      },
      ios: {
        supportsTablet: true,
        bundleIdentifier: "app.rork.farm-inventory-control-app-s0altyt",
        buildNumber: IOS_BUILD_NUMBER // 👈 Must be a string
      },
      android: {
        adaptiveIcon: {
          foregroundImage: "./assets/images/adaptive-icon.png",
          backgroundColor: "#ffffff"
        },
        package: "com.farm.inventory",
        versionCode: ANDROID_VERSION_CODE // 👈 Must be an integer
      },
      web: {
        favicon: "./assets/images/favicon.png"
      },
      plugins: [
        [
          "expo-router",
          {
            origin: "https://rork.app/"
          }
        ],
        "expo-web-browser",
        "expo-font"
      ],
      experiments: {
        typedRoutes: true
      },
      updates: {
        fallbackToCacheTimeout: 0,
        url: "https://u.expo.dev/0834d24d-5bd5-4cf0-9511-f90b5719b4d3", // ✅ your real project ID
        checkAutomatically: "ON_LOAD",
        channel: BRANCH
      },
      extra: {
        eas: {
          projectId: "0834d24d-5bd5-4cf0-9511-f90b5719b4d3"
        },
        branch: BRANCH,
        appVersion: APP_VERSION // ✅ You can access this inside the app
      }
    };
  };
  
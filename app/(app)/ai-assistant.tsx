import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Alert,
  SafeAreaView,
  StatusBar,
  Dimensions,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Stack, router } from 'expo-router';
import { <PERSON><PERSON><PERSON><PERSON>, Bot, Trash2 } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

import { ChatMessage as ChatMessageComponent, ChatMessageProps } from '@/components/ChatMessage';
import { ChatInput, ChatInputRef } from '@/components/ChatInput';
import { TypingIndicator } from '@/components/TypingIndicator';
import { BrowseItemsMessage } from '@/components/BrowseItemsMessage';

import { useAuth } from '@/context/auth-context';
import { useTheme } from '@/context/theme-context';
import { useLanguage } from '@/context/language-context';
import { useFarm } from '@/context/farm-context';

import { processAICommand, processAIImage, processConversationalRequest, processBrowseCommand } from '@/services/ai-assistant-service';
import {
  ConversationContext,
  processFieldInput,
  submitConversationalRequest,
  generateResponseMessage,
  getAvailableMachinery,
  createConversationContext
} from '@/services/conversational-request-service';
import { BrowseResult } from '@/services/browse-service';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Utility function to generate unique message IDs
const generateMessageId = (prefix: string = 'msg') => {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
};

interface ChatMessage extends ChatMessageProps {
  id: string;
}

export default function AIAssistantScreen() {
  const { user } = useAuth();
  const { colors } = useTheme();
  const { isRTL, t } = useLanguage();
  const { selectedFarm } = useFarm();
  
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [activeConversation, setActiveConversation] = useState<ConversationContext | null>(null);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [selectedItemType, setSelectedItemType] = useState<'machinery' | 'inventory' | null>(null);
  const [lastRequestType, setLastRequestType] = useState<string>('all');
  const flatListRef = useRef<FlatList>(null);
  const chatInputRef = useRef<ChatInputRef>(null);

  // Initialize with welcome message
  useEffect(() => {
    const welcomeMessage: ChatMessage = {
      id: 'welcome',
      message: getWelcomeMessage(),
      isUser: false,
      timestamp: new Date(),
      success: undefined,
      suggestions: getInitialSuggestions(),
    };
    setMessages([welcomeMessage]);
  }, [user?.role]);

  const getWelcomeMessage = () => {
    const role = user?.role || 'caretaker';
    const userName = user?.displayName || t('chat.user');
    
    let message = `${t('chat.welcome')} ${userName}! 👋\n\n`;
    message += `${t('chat.iAmYourAssistant')}\n\n`;
    
    if (role === 'owner') {
      message += `${t('chat.ownerCapabilities')}:\n`;
      message += `• ${t('chat.createInventory')}\n`;
      message += `• ${t('chat.createMachinery')}\n`;
      message += `• ${t('chat.createRequests')}\n`;
    } else if (role === 'admin') {
      message += `${t('chat.adminCapabilities')}:\n`;
      message += `• ${t('chat.createInventory')}\n`;
      message += `• ${t('chat.createMachinery')}\n`;
      message += `• ${t('chat.createRequests')}\n`;
    } else {
      message += `${t('chat.caretakerCapabilities')}:\n`;
      message += `• ${t('chat.createRequests')}\n`;
    }
    
    message += `\n${t('chat.typeHelpForMore')}`;
    
    return message;
  };

  const getInitialSuggestions = () => {
    const role = user?.role || 'caretaker';
    const suggestions: string[] = [];
    
    if (role === 'owner' || role === 'admin') {
      suggestions.push('Add 50 bags of urea');
      suggestions.push('50 تھیلے یوریا شامل کریں');
      suggestions.push('Add new tractor');
      suggestions.push('نیا ٹریکٹر شامل کریں');
    }
    
    suggestions.push('Request 20 liters diesel');
    suggestions.push('20 لیٹر ڈیزل کی درخواست');
    suggestions.push('Help');
    suggestions.push('مدد');
    
    return suggestions;
  };

  const handleSendMessage = async (messageText: string) => {
    if (!user || !selectedFarm) {
      Alert.alert(t('common.error'), t('chat.noFarmSelected'));
      return;
    }

    // Add user message
    const userMessage: ChatMessage = {
      id: generateMessageId('user'),
      message: messageText,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    // Scroll to bottom
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);

    try {
      // Check for machinery request type selection
      if (selectedItem && selectedItemType === 'machinery') {
        const lowerText = messageText.toLowerCase();

        if (lowerText.includes('request for use') || lowerText.includes('use')) {
          // Start machinery use request
          await startMachineryRequest('use', selectedItem);
          setSelectedItem(null);
          setSelectedItemType(null);
          setIsTyping(false);
          return;
        } else if (lowerText.includes('request maintenance') || lowerText.includes('maintenance')) {
          // Start machinery maintenance request
          await startMachineryRequest('maintenance', selectedItem);
          setSelectedItem(null);
          setSelectedItemType(null);
          setIsTyping(false);
          return;
        } else if (lowerText.includes('request fuel') || lowerText.includes('fuel')) {
          // Start machinery fuel request
          await startMachineryRequest('fuel', selectedItem);
          setSelectedItem(null);
          setSelectedItemType(null);
          setIsTyping(false);
          return;
        } else if (lowerText.includes('get more details') || lowerText.includes('details')) {
          // Show machinery details
          const detailsMessage: ChatMessage = {
            id: `details-${Date.now()}-${Math.random()}`,
            message: `Details for ${selectedItem.name}:\n• Type: ${selectedItem.type}\n• Model: ${selectedItem.model || 'N/A'}\n• Status: ${selectedItem.status}\n• Fuel Level: ${selectedItem.currentFuelLevel || 'N/A'}%\n• Location: ${selectedItem.currentLocation || 'N/A'}`,
            isUser: false,
            timestamp: new Date(),
            success: true,
          };
          setMessages(prev => [...prev, detailsMessage]);
          setSelectedItem(null);
          setSelectedItemType(null);
          setIsTyping(false);
          return;
        }
      }

      // Check for inventory request type selection
      if (selectedItem && selectedItemType === 'inventory') {
        const lowerText = messageText.toLowerCase();

        if (lowerText.includes('request this item') || lowerText.includes('request')) {
          // Start inventory request with pre-filled data
          await startInventoryRequest(selectedItem);
          setSelectedItem(null);
          setSelectedItemType(null);
          setIsTyping(false);
          return;
        } else if (lowerText.includes('check availability') || lowerText.includes('availability')) {
          // Show inventory availability
          const availabilityMessage: ChatMessage = {
            id: generateMessageId('availability'),
            message: `Availability for ${selectedItem.name}:\n• Current Stock: ${selectedItem.quantity || 0} ${selectedItem.unit || 'units'}\n• Minimum Stock: ${selectedItem.minQuantity || 'Not set'}\n• Status: ${selectedItem.quantity > (selectedItem.minQuantity || 0) ? 'In Stock' : 'Low Stock'}\n• Location: ${selectedItem.location || 'Not specified'}`,
            isUser: false,
            timestamp: new Date(),
            success: true,
          };
          setMessages(prev => [...prev, availabilityMessage]);
          setSelectedItem(null);
          setSelectedItemType(null);
          setIsTyping(false);
          return;
        } else if (lowerText.includes('get more details') || lowerText.includes('details')) {
          // Show inventory details
          const detailsMessage: ChatMessage = {
            id: generateMessageId('inv-details'),
            message: `Details for ${selectedItem.name}:\n• Category: ${selectedItem.category || 'N/A'}\n• Quantity: ${selectedItem.quantity || 0} ${selectedItem.unit || 'units'}\n• Price: Rs ${selectedItem.price || 'N/A'}/${selectedItem.unit || 'unit'}\n• Supplier: ${selectedItem.supplier || 'N/A'}\n• Location: ${selectedItem.location || 'N/A'}\n• Expiry: ${selectedItem.expiryDate ? new Date(selectedItem.expiryDate).toLocaleDateString() : 'N/A'}`,
            isUser: false,
            timestamp: new Date(),
            success: true,
          };
          setMessages(prev => [...prev, detailsMessage]);
          setSelectedItem(null);
          setSelectedItemType(null);
          setIsTyping(false);
          return;
        }
      }

      // Check if we have an active conversation and need to collect field input
      if (activeConversation && activeConversation.state === 'collecting_fields') {
        const currentField = activeConversation.fields[activeConversation.currentFieldIndex];
        if (currentField) {
          // Process the user's response as field input
          let updatedContext = processFieldInput(activeConversation, currentField.id, messageText);

          // For inventory requests, skip pre-filled fields (unit and category)
          if (updatedContext.requestType === 'inventory' && updatedContext.state === 'collecting_fields') {
            while (updatedContext.currentFieldIndex < updatedContext.fields.length) {
              const nextField = updatedContext.fields[updatedContext.currentFieldIndex];
              if (nextField && updatedContext.collectedData[nextField.id] !== undefined) {
                // Skip this field as it's already pre-filled
                updatedContext = {
                  ...updatedContext,
                  currentFieldIndex: updatedContext.currentFieldIndex + 1
                };
                if (updatedContext.currentFieldIndex >= updatedContext.fields.length) {
                  updatedContext.state = 'confirming_request';
                  break;
                }
              } else {
                break;
              }
            }
          }

          setActiveConversation(updatedContext);

          // Check if we need to collect more fields or confirm
          if (updatedContext.state === 'confirming_request') {
            // Show confirmation
            const confirmationMessage: ChatMessage = {
              id: (Date.now() + 1).toString(),
              message: generateResponseMessage(updatedContext),
              isUser: false,
              timestamp: new Date(),
              suggestions: ['Yes, submit request', 'No, cancel'],
            };
            setMessages(prev => [...prev, confirmationMessage]);
          } else {
            // Show next field
            const nextField = updatedContext.fields[updatedContext.currentFieldIndex];
            if (nextField) {
              const aiMessage: ChatMessage = {
                id: (Date.now() + 1).toString(),
                message: nextField.label,
                isUser: false,
                timestamp: new Date(),
                currentField: nextField,
              };
              setMessages(prev => [...prev, aiMessage]);
            }
          }
          setIsTyping(false);
          return;
        }
      }

      // Check for conversational request confirmation
      if (activeConversation && activeConversation.state === 'confirming_request') {
        if (messageText.toLowerCase().includes('yes') || messageText.toLowerCase().includes('submit')) {
          // Submit the request
          const result = await submitConversationalRequest(activeConversation);

          const responseMessage: ChatMessage = {
            id: (Date.now() + 1).toString(),
            message: result.message,
            isUser: false,
            timestamp: new Date(),
            success: result.success,
          };

          setMessages(prev => [...prev, responseMessage]);
          setActiveConversation(null);

          if (result.success) {
            Alert.alert(t('common.success'), result.message);
          }
          setIsTyping(false);
          return;
        } else if (messageText.toLowerCase().includes('no') || messageText.toLowerCase().includes('cancel')) {
          handleRequestCancel();
          setIsTyping(false);
          return;
        }
      }

      // Check for admin request option selection
      if (user.role === 'admin' && (messageText.includes('My Requests') || messageText.includes('Caretaker Requests'))) {
        const isMyRequests = messageText.includes('My Requests');
        const requestType = lastRequestType || 'all'; // Use the last requested type or default to all

        let requestsResult;
        if (isMyRequests) {
          // Show admin's own requests (submitted to owner)
          requestsResult = await processAICommand(
            `show my ${requestType} requests`,
            user.uid,
            user.displayName || 'User',
            user.role,
            selectedFarm.id,
            selectedFarm.name
          );
        } else {
          // Show caretaker requests (for admin approval)
          requestsResult = await processAICommand(
            `show caretaker ${requestType} requests`,
            user.uid,
            user.displayName || 'User',
            user.role,
            selectedFarm.id,
            selectedFarm.name
          );
        }

        if (requestsResult.action === 'show_requests_list') {
          const aiMessage: ChatMessage = {
            id: generateMessageId('admin-requests'),
            message: requestsResult.message,
            isUser: false,
            timestamp: new Date(),
            success: requestsResult.success,
            requestsList: requestsResult.data,
          };

          setMessages(prev => [...prev, aiMessage]);
        } else {
          // Fallback message
          const aiMessage: ChatMessage = {
            id: generateMessageId('admin-requests-error'),
            message: requestsResult.message || 'Unable to fetch requests at this time.',
            isUser: false,
            timestamp: new Date(),
            success: false,
          };

          setMessages(prev => [...prev, aiMessage]);
        }

        setIsTyping(false);
        return;
      }

      // Process AI command
      const result = await processAICommand(
        messageText,
        user.uid,
        user.displayName || 'User',
        user.role,
        selectedFarm.id,
        selectedFarm.name
      );

      // Handle browse command
      if (result.action === 'show_browse_items') {
        const aiMessage: ChatMessage = {
          id: generateMessageId('browse'),
          message: result.message,
          isUser: false,
          timestamp: new Date(),
          success: result.success,
          browseResult: result.browseResult,
          suggestions: result.suggestions,
        };

        setMessages(prev => [...prev, aiMessage]);
      }
      // Handle request options (for admin users)
      else if (result.action === 'show_request_options') {
        const options = result.data?.options || [];
        const suggestions = options.map((option: any) => option.label);

        // Track the request type for later use
        if (result.data?.requestType) {
          setLastRequestType(result.data.requestType);
        }

        const aiMessage: ChatMessage = {
          id: generateMessageId('request-options'),
          message: result.message,
          isUser: false,
          timestamp: new Date(),
          success: result.success,
          suggestions: suggestions,
          requestOptions: result.data,
        };

        setMessages(prev => [...prev, aiMessage]);
      }
      // Handle requests list
      else if (result.action === 'show_requests_list') {
        const aiMessage: ChatMessage = {
          id: generateMessageId('requests-list'),
          message: result.message,
          isUser: false,
          timestamp: new Date(),
          success: result.success,
          requestsList: result.data,
        };

        setMessages(prev => [...prev, aiMessage]);
      }
      // Handle conversational request start
      else if (result.action === 'start_conversational_request') {
        const conversationalResult = await processConversationalRequest(
          messageText,
          user.uid,
          selectedFarm.id,
          user.role || 'caretaker',
          selectedFarm.name
        );

        if (conversationalResult.action === 'show_machinery_selection') {
          setActiveConversation(conversationalResult.conversationContext!);

          const aiMessage: ChatMessage = {
            id: (Date.now() + 1).toString(),
            message: conversationalResult.message || 'Processing request...',
            isUser: false,
            timestamp: new Date(),
            machineryList: conversationalResult.machineryList,
            conversationContext: conversationalResult.conversationContext,
          };

          setMessages(prev => [...prev, aiMessage]);
        } else if (conversationalResult.action === 'show_field_input') {
          setActiveConversation(conversationalResult.conversationContext!);

          const aiMessage: ChatMessage = {
            id: (Date.now() + 1).toString(),
            message: conversationalResult.message || 'Let\'s collect the required information.',
            isUser: false,
            timestamp: new Date(),
            currentField: conversationalResult.currentField,
          };

          setMessages(prev => [...prev, aiMessage]);
        } else {
          // Handle error case
          const aiMessage: ChatMessage = {
            id: generateMessageId('error'),
            message: conversationalResult.message || 'Error processing request',
            isUser: false,
            timestamp: new Date(),
            success: false,
            suggestions: conversationalResult.suggestions,
          };

          setMessages(prev => [...prev, aiMessage]);
        }
      } else {
        // Add regular AI response
        const aiMessage: ChatMessage = {
          id: generateMessageId('ai'),
          message: result.message,
          isUser: false,
          timestamp: new Date(),
          success: result.success,
          suggestions: result.suggestions,
        };

        setMessages(prev => [...prev, aiMessage]);
      }

      // Show success/error alert for actions
      if (result.success && !result.suggestions) {
        // This was an action, not just help
        Alert.alert(t('common.success'), result.message);
      }

    } catch (error) {
      console.error('Error processing AI command:', error);

      const errorMessage: ChatMessage = {
        id: generateMessageId('cmd-error'),
        message: t('chat.errorProcessingCommand'),
        isUser: false,
        timestamp: new Date(),
        success: false,
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);

      // Scroll to bottom after AI response
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  };

  const handleSendImage = async (imageUri: string, messageText?: string) => {
    if (!user || !selectedFarm) {
      Alert.alert(t('common.error'), t('chat.noFarmSelected'));
      return;
    }

    // Add user message with image
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      message: messageText || t('chat.imageUploaded'),
      isUser: true,
      timestamp: new Date(),
      imageUri: imageUri,
    };

    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    // Scroll to bottom
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);

    try {
      // Process AI image
      const result = await processAIImage(
        imageUri,
        messageText,
        user.uid,
        user.displayName || 'User',
        user.role,
        selectedFarm.id,
        selectedFarm.name
      );

      // Add AI response with image analysis
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        message: result.message,
        isUser: false,
        timestamp: new Date(),
        success: result.success,
        suggestions: result.suggestions,
        imageAnalysis: result.imageAnalysis,
      };

      setMessages(prev => [...prev, aiMessage]);

      // Navigate to add screen if navigation data is present
      if (result.data?.navigationData && result.success) {
        setTimeout(() => {
          router.push({
            pathname: result.data.navigationData.screen,
            params: result.data.navigationData.params
          });
        }, 1000); // Small delay to show the message first
      }

      // Show success/error alert for actions
      if (result.success && result.imageAnalysis && result.imageAnalysis.type !== 'unknown') {
        Alert.alert(
          t('common.success'),
          result.message,
          [
            {
              text: t('common.ok'),
              onPress: () => {
                if (result.data?.navigationData) {
                  router.push({
                    pathname: result.data.navigationData.screen,
                    params: result.data.navigationData.params
                  });
                }
              }
            }
          ]
        );
      }

    } catch (error) {
      console.error('Error processing AI image:', error);

      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        message: t('chat.errorProcessingCommand'),
        isUser: false,
        timestamp: new Date(),
        success: false,
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);

      // Scroll to bottom after AI response
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  };

  const handleSuggestionPress = (suggestion: string) => {
    // Auto-populate the text input and send the message
    if (chatInputRef.current) {
      chatInputRef.current.setText(suggestion);
    }
    // Small delay to show the text in input, then send and clear
    setTimeout(() => {
      handleSendMessage(suggestion);
      // Clear the input after sending
      setTimeout(() => {
        if (chatInputRef.current) {
          chatInputRef.current.setText('');
        }
      }, 100);
    }, 100);
  };

  // Conversational request handlers
  const handleMachinerySelect = async (machinery: any) => {
    if (!activeConversation || !user || !selectedFarm) return;

    // Update conversation context with selected machinery
    const updatedContext = {
      ...activeConversation,
      selectedMachinery: machinery,
      state: 'collecting_fields' as const
    };

    setActiveConversation(updatedContext);

    // Add user selection message
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      message: `Selected: ${machinery.name}`,
      isUser: true,
      timestamp: new Date(),
    };

    // Add AI response with first field
    const currentField = updatedContext.fields[0];
    const aiMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      message: currentField ? currentField.label : 'Let\'s collect the required information.',
      isUser: false,
      timestamp: new Date(),
      currentField: currentField,
    };

    setMessages(prev => [...prev, userMessage, aiMessage]);
  };

  // Date selection handler
  const handleDateSelect = (date: string) => {
    if (!activeConversation) return;

    // Process the date as field input
    handleSendMessage(date);
  };

  // Request press handler
  const handleRequestPress = (requestId: string, farmId: string) => {
    console.log('Request pressed:', requestId, farmId);
    router.push(`/request/${requestId}?farmId=${farmId}`);
  };

  // Field input is now handled through main chat input via handleSendMessage

  const handleRequestCancel = () => {
    Alert.alert(
      t('common.confirm'),
      t('chat.cancelRequestConfirm'),
      [
        { text: t('common.no'), style: 'cancel' },
        {
          text: t('common.yes'),
          style: 'destructive',
          onPress: () => {
            setActiveConversation(null);
            const cancelMessage: ChatMessage = {
              id: Date.now().toString(),
              message: t('chat.requestCancelled'),
              isUser: false,
              timestamp: new Date(),
            };
            setMessages(prev => [...prev, cancelMessage]);
          },
        },
      ]
    );
  };

  const clearChat = () => {
    Alert.alert(
      t('chat.clearChat'),
      t('chat.clearChatConfirm'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.clear'),
          style: 'destructive',
          onPress: () => {
            const welcomeMessage: ChatMessage = {
              id: 'welcome-' + Date.now(),
              message: getWelcomeMessage(),
              isUser: false,
              timestamp: new Date(),
              suggestions: getInitialSuggestions(),
            };
            setMessages([welcomeMessage]);
          },
        },
      ]
    );
  };

  // Start machinery request flow
  const startMachineryRequest = async (requestType: 'use' | 'maintenance' | 'fuel', machinery: any) => {
    if (!user || !selectedFarm) return;

    try {
      // Create conversation context for the specific machinery
      const context = createConversationContext(requestType, selectedFarm.id, user.uid, user.displayName || 'User', selectedFarm.name);
      context.selectedMachinery = machinery;
      context.state = 'collecting_fields';

      setActiveConversation(context);

      // Get the first field to collect
      const firstField = context.fields[0];

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        message: `Great! I'll help you create a ${requestType} request for ${machinery.name}. ${firstField ? firstField.label : 'Let\'s get started.'}`,
        isUser: false,
        timestamp: new Date(),
        currentField: firstField,
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error starting machinery request:', error);
      Alert.alert(t('common.error'), 'Failed to start request. Please try again.');
    }
  };

  // Start inventory request flow
  const startInventoryRequest = async (inventoryItem: any) => {
    if (!user || !selectedFarm) return;

    try {
      // Create conversation context for inventory request
      const context = createConversationContext('inventory', selectedFarm.id, user.uid, user.displayName || 'User', selectedFarm.name);

      // Pre-fill data from the selected inventory item
      context.collectedData = {
        itemName: inventoryItem.name,
        category: inventoryItem.category,
        unit: inventoryItem.unit || 'pieces'
      };

      // Start from quantity field since we already have item name, unit, and category
      context.currentFieldIndex = 1; // Start from quantity field (skip itemName)
      context.state = 'collecting_fields';

      setActiveConversation(context);

      // Get the current field to collect (quantity)
      const currentField = context.fields[context.currentFieldIndex];

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        message: `Great! I'll help you create an inventory request for ${inventoryItem.name}. ${currentField ? currentField.label : 'Let\'s collect the required information.'}`,
        isUser: false,
        timestamp: new Date(),
        currentField: currentField,
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error starting inventory request:', error);
      Alert.alert(t('common.error'), 'Failed to start request. Please try again.');
    }
  };

  // Browse item selection handler
  const handleItemSelect = async (item: any, itemType: 'machinery' | 'inventory') => {
    if (!user || !selectedFarm) return;

    // Store selected item context
    setSelectedItem(item);
    setSelectedItemType(itemType);

    // Add user selection message
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      message: `Selected: ${item.name}`,
      isUser: true,
      timestamp: new Date(),
    };

    // Create appropriate suggestions based on item type
    let suggestions: string[] = [];
    let message = '';

    if (itemType === 'machinery') {
      message = `You selected "${item.name}". What would you like to do with this machinery?`;
      suggestions = [
        'Request for use',
        'Request maintenance',
        'Request fuel',
        'Get more details'
      ];
    } else {
      message = `You selected "${item.name}". What would you like to do with this inventory item?`;
      suggestions = [
        'Request this item',
        'Check availability',
        'Get more details'
      ];
    }

    // Add AI response asking for request type
    const aiMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      message: message,
      isUser: false,
      timestamp: new Date(),
      suggestions: suggestions,
    };

    setMessages(prev => [...prev, userMessage, aiMessage]);
  };

  const renderMessage = ({ item }: { item: ChatMessageProps }) => (
    <ChatMessageComponent
      {...item}
      onSuggestionPress={handleSuggestionPress}
      onMachinerySelect={handleMachinerySelect}
      onRequestCancel={handleRequestCancel}
      onItemSelect={handleItemSelect}
      onDateSelect={handleDateSelect}
      onRequestPress={handleRequestPress}
    />
  );

  const renderHeader = () => (
    <LinearGradient
      colors={[colors.primary, colors.primaryDark]}
      style={styles.header}
    >
      <SafeAreaView style={styles.safeAreaHeader}>
        <View style={[styles.headerContent, isRTL && styles.rtlHeaderContent]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color="#fff" />
          </TouchableOpacity>

          <View style={[styles.headerInfo, isRTL && styles.rtlHeaderInfo]}>
            <View style={styles.botIconContainer}>
              <Bot size={24} color="#fff" />
            </View>
            <View style={styles.headerText}>
              <Text style={styles.headerTitle}>
                {t('chat.aiAssistant')}
              </Text>
              <Text style={styles.headerSubtitle}>
                {selectedFarm?.name || t('chat.noFarm')}
              </Text>
            </View>
          </View>

          <TouchableOpacity
            style={styles.clearButton}
            onPress={clearChat}
          >
            <Trash2 size={20} color="#fff" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary} />
      <Stack.Screen options={{ headerShown: false }} />

      {renderHeader()}

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
          ListFooterComponent={isTyping ? <TypingIndicator /> : null}
        />

        <ChatInput
          ref={chatInputRef}
          onSendMessage={handleSendMessage}
          onSendImage={handleSendImage}
          disabled={isTyping}
          placeholder={t('chat.typeMessage')}
        />
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    paddingBottom: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  safeAreaHeader: {
    paddingTop: Platform.OS === 'android' ? 8 : 0,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  rtlHeaderContent: {
    flexDirection: 'row-reverse',
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  rtlHeaderInfo: {
    flexDirection: 'row-reverse',
  },
  botIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerText: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 2,
  },
  clearButton: {
    padding: 8,
    marginLeft: 8,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: 16,
    flexGrow: 1,
  },
});

import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, TextInput, TouchableOpacity, Alert, ActivityIndicator, KeyboardAvoidingView, Platform } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter } from "expo-router";
import DateTimePicker from '@react-native-community/datetimepicker';
import { useAuth } from "@/context/auth-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { updateUserProfile } from "@/services/user-service";
import { User, Mail, Phone, Calendar, FileText, MapPin, Save, X } from "lucide-react-native";

export default function ProfileDetailsScreen() {
  const { user, refreshUserData } = useAuth();
  const { colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const router = useRouter();
  
  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [formData, setFormData] = useState({
    displayName: user?.displayName || "",
    phone: user?.phone || user?.phoneNumber || "",
    bio: user?.bio || "",
    gender: user?.gender || "",
    dateOfBirth: user?.dateOfBirth || "",
    cnic: user?.cnic || "",
    address: user?.address || "",
  });

  // Get role-based colors
  const getRoleColors = () => {
    switch (user?.role) {
      case 'owner':
        return {
          primary: '#2E7D32',
          secondary: '#4CAF50',
        };
      case 'admin':
        return {
          primary: '#1976D2',
          secondary: '#2196F3',
        };
      case 'caretaker':
        return {
          primary: '#FF9800',
          secondary: '#FFC107',
        };
      default:
        return {
          primary: colors.primary,
          secondary: colors.primary,
        };
    }
  };

  const roleColors = getRoleColors();

  // CNIC formatting function
  const formatCNIC = (text) => {
    // Remove all non-digits
    const digits = text.replace(/\D/g, '');

    // Apply CNIC format: 12345-1234567-1
    if (digits.length <= 5) {
      return digits;
    } else if (digits.length <= 12) {
      return `${digits.slice(0, 5)}-${digits.slice(5)}`;
    } else {
      return `${digits.slice(0, 5)}-${digits.slice(5, 12)}-${digits.slice(12, 13)}`;
    }
  };

  // Date picker functions
  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      const formattedDate = selectedDate.toLocaleDateString('en-GB'); // DD/MM/YYYY format
      setFormData({ ...formData, dateOfBirth: formattedDate });
    }
  };

  const getDateFromString = (dateString) => {
    if (!dateString) return new Date();
    const parts = dateString.split('/');
    if (parts.length === 3) {
      // DD/MM/YYYY format
      return new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
    }
    return new Date();
  };

  useEffect(() => {
    if (user) {
      setFormData({
        displayName: user.displayName || "",
        phone: user.phone || user.phoneNumber || "",
        bio: user.bio || "",
        gender: user.gender || "",
        dateOfBirth: user.dateOfBirth || "",
        cnic: user.cnic || "",
        address: user.address || "",
      });
    }
  }, [user]);

  const handleSaveProfile = async () => {
    if (!user) return;

    setLoading(true);
    try {
      console.log("💾 Saving detailed profile changes:", {
        oldName: user.displayName,
        newName: formData.displayName,
        phone: formData.phone,
        bio: formData.bio,
        gender: formData.gender,
        dateOfBirth: formData.dateOfBirth,
        cnic: formData.cnic,
        address: formData.address
      });

      await updateUserProfile(user.uid, {
        name: formData.displayName,
        displayName: formData.displayName,
        phone: formData.phone,
        phoneNumber: formData.phone,
        bio: formData.bio,
        gender: formData.gender,
        dateOfBirth: formData.dateOfBirth,
        cnic: formData.cnic,
        address: formData.address,
      });

      console.log("🔄 Refreshing user data after detailed profile update...");
      await refreshUserData();

      console.log("✅ Detailed profile updated and user data refreshed");
      setIsEditing(false);
      Alert.alert(t('common.success'), t('profile.profileUpdated'));
    } catch (error) {
      console.error('❌ Error updating detailed profile:', error);
      Alert.alert(t('common.error'), t('profile.updateError'));
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      displayName: user?.displayName || "",
      phone: user?.phone || user?.phoneNumber || "",
      bio: user?.bio || "",
      gender: user?.gender || "",
      dateOfBirth: user?.dateOfBirth || "",
      cnic: user?.cnic || "",
      address: user?.address || "",
    });
    setIsEditing(false);
  };

  if (!user) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={roleColors.primary} />
      </View>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t("settings.profileDetails"),
          headerStyle: { backgroundColor: roleColors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { color: 'white', fontWeight: '600' },
        }}
      />

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
        {/* Personal Information Card */}
        <View style={[styles.card, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {t("profile.personalInformation")}
          </Text>
          
          {/* Name */}
          <View style={[styles.infoRow, isRTL && styles.infoRowRTL]}>
            <View style={[styles.infoRowLeft, isRTL && styles.infoRowLeftRTL]}>
              <User size={18} color={colors.textSecondary} />
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                {t("profile.name")}
              </Text>
            </View>
            {isEditing ? (
              <TextInput
                style={[styles.infoInput, { 
                  color: colors.text, 
                  borderColor: roleColors.primary + '40',
                  backgroundColor: colors.inputBackground,
                }]}
                value={formData.displayName}
                onChangeText={(text) => setFormData({ ...formData, displayName: text })}
                placeholder={t("common.name")}
                placeholderTextColor={colors.textSecondary}
              />
            ) : (
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {user?.displayName || t("profile.notSet")}
              </Text>
            )}
          </View>

          {/* Email (Read-only) */}
          <View style={[styles.infoRow, isRTL && styles.infoRowRTL]}>
            <View style={[styles.infoRowLeft, isRTL && styles.infoRowLeftRTL]}>
              <Mail size={18} color={colors.textSecondary} />
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                {t("profile.email")}
              </Text>
            </View>
            <Text style={[styles.infoValue, { color: colors.text }]}>
              {user?.email}
            </Text>
          </View>

          {/* Phone */}
          <View style={[styles.infoRow, isRTL && styles.infoRowRTL]}>
            <View style={[styles.infoRowLeft, isRTL && styles.infoRowLeftRTL]}>
              <Phone size={18} color={colors.textSecondary} />
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                {t("profile.phone")}
              </Text>
            </View>
            {isEditing ? (
              <TextInput
                style={[styles.infoInput, { 
                  color: colors.text, 
                  borderColor: roleColors.primary + '40',
                  backgroundColor: colors.inputBackground,
                }]}
                value={formData.phone}
                onChangeText={(text) => setFormData({ ...formData, phone: text })}
                placeholder={t("common.phone")}
                placeholderTextColor={colors.textSecondary}
                keyboardType="phone-pad"
              />
            ) : (
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {user?.phone || t("profile.notSet")}
              </Text>
            )}
          </View>

          {/* Gender */}
          <View style={[styles.infoRow, isRTL && styles.infoRowRTL]}>
            <View style={[styles.infoRowLeft, isRTL && styles.infoRowLeftRTL]}>
              <User size={18} color={colors.textSecondary} />
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                {t("profile.gender")}
              </Text>
            </View>
            {isEditing ? (
              <TextInput
                style={[styles.infoInput, { 
                  color: colors.text, 
                  borderColor: roleColors.primary + '40',
                  backgroundColor: colors.inputBackground,
                }]}
                value={formData.gender}
                onChangeText={(text) => setFormData({ ...formData, gender: text })}
                placeholder={t("profile.gender")}
                placeholderTextColor={colors.textSecondary}
              />
            ) : (
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {user?.gender || t("profile.notProvided")}
              </Text>
            )}
          </View>

          {/* Date of Birth */}
          <View style={[styles.infoRow, isRTL && styles.infoRowRTL]}>
            <View style={[styles.infoRowLeft, isRTL && styles.infoRowLeftRTL]}>
              <Calendar size={18} color={colors.textSecondary} />
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                {t("profile.dateOfBirth")}
              </Text>
            </View>
            {isEditing ? (
              <TouchableOpacity
                style={[styles.infoInput, {
                  borderColor: roleColors.primary + '40',
                  backgroundColor: colors.background,
                  justifyContent: 'center',
                }]}
                onPress={() => setShowDatePicker(true)}
              >
                <Text style={[styles.datePickerText, {
                  color: formData.dateOfBirth ? colors.text : colors.textSecondary
                }]}>
                  {formData.dateOfBirth || "DD/MM/YYYY"}
                </Text>
              </TouchableOpacity>
            ) : (
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {user?.dateOfBirth || t("profile.notProvided")}
              </Text>
            )}
          </View>

          {/* Date Picker Modal */}
          {showDatePicker && (
            <DateTimePicker
              value={getDateFromString(formData.dateOfBirth)}
              mode="date"
              display={Platform.OS === 'ios' ? 'spinner' : 'default'}
              onChange={handleDateChange}
              maximumDate={new Date()}
              minimumDate={new Date(1900, 0, 1)}
            />
          )}

          {/* CNIC */}
          <View style={[styles.infoRow, isRTL && styles.infoRowRTL]}>
            <View style={[styles.infoRowLeft, isRTL && styles.infoRowLeftRTL]}>
              <FileText size={18} color={colors.textSecondary} />
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                {t("profile.cnic")}
              </Text>
            </View>
            {isEditing ? (
              <TextInput
                style={[styles.infoInput, {
                  color: colors.text,
                  borderColor: roleColors.primary + '40',
                  backgroundColor: colors.background,
                }]}
                value={formData.cnic}
                onChangeText={(text) => {
                  const formattedCNIC = formatCNIC(text);
                  setFormData({ ...formData, cnic: formattedCNIC });
                }}
                placeholder="12345-1234567-1"
                placeholderTextColor={colors.textSecondary}
                keyboardType="numeric"
                maxLength={15} // 13 digits + 2 hyphens
              />
            ) : (
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {user?.cnic || t("profile.notProvided")}
              </Text>
            )}
          </View>

          {/* Address */}
          <View style={[styles.infoRow, styles.addressRow, isRTL && styles.infoRowRTL]}>
            <View style={[styles.infoRowLeft, isRTL && styles.infoRowLeftRTL]}>
              <MapPin size={18} color={colors.textSecondary} />
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                {t("profile.address")}
              </Text>
            </View>
            {isEditing ? (
              <TextInput
                style={[styles.addressInput, { 
                  color: colors.text, 
                  borderColor: roleColors.primary + '40',
                  backgroundColor: colors.background,
                }]}
                value={formData.address}
                onChangeText={(text) => setFormData({ ...formData, address: text })}
                placeholder={t("profile.address")}
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            ) : (
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {user?.address || t("profile.notProvided")}
              </Text>
            )}
          </View>

          {/* Bio */}
          <View style={[styles.infoRow, styles.bioRow, isRTL && styles.infoRowRTL]}>
            <View style={[styles.infoRowLeft, isRTL && styles.infoRowLeftRTL]}>
              <FileText size={18} color={colors.textSecondary} />
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                {t("common.bio")}
              </Text>
            </View>
            {isEditing ? (
              <TextInput
                style={[styles.bioInput, { 
                  color: colors.text, 
                  borderColor: roleColors.primary + '40',
                  backgroundColor: colors.background,
                }]}
                value={formData.bio}
                onChangeText={(text) => setFormData({ ...formData, bio: text })}
                placeholder={t("common.bio")}
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            ) : (
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {user?.bio || t("profile.notProvided")}
              </Text>
            )}
          </View>
        </View>
        </ScrollView>

        {/* Bottom Action Buttons */}
        <View style={[styles.bottomActions, { backgroundColor: colors.background, borderTopColor: colors.border }]}>
          {isEditing ? (
            <View style={styles.editActions}>
              <TouchableOpacity
                style={[styles.cancelButton, { backgroundColor: colors.background, borderColor: colors.border }]}
                onPress={handleCancel}
                disabled={loading}
              >
                <Text style={[styles.cancelButtonText, { color: colors.text }]}>
                  {t("common.cancel")}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.saveButton, { backgroundColor: roleColors.primary }]}
                onPress={handleSaveProfile}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator size={20} color="white" />
                ) : (
                  <Text style={styles.saveButtonText}>{t("common.save")}</Text>
                )}
              </TouchableOpacity>
            </View>
          ) : (
            <TouchableOpacity
              style={[styles.editButton, { backgroundColor: roleColors.primary }]}
              onPress={() => setIsEditing(true)}
            >
              <Text style={styles.editButtonText}>{t('common.edit')}</Text>
            </TouchableOpacity>
          )}
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100, // Extra space for bottom buttons
  },

  card: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 20,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  infoRowRTL: {
    flexDirection: 'row-reverse',
  },
  addressRow: {
    alignItems: 'flex-start',
  },
  bioRow: {
    alignItems: 'flex-start',
    borderBottomWidth: 0,
  },
  infoRowLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  infoRowLeftRTL: {
    flexDirection: 'row-reverse',
  },
  infoLabel: {
    fontSize: 15,
    fontWeight: '500',
    marginLeft: 12,
  },
  infoValue: {
    fontSize: 15,
    fontWeight: '600',
    flex: 1,
    textAlign: 'right',
  },
  infoInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 15,
    textAlign: 'right',
  },
  addressInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 15,
    minHeight: 80,
    textAlign: 'right',
  },
  bioInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 15,
    minHeight: 100,
    textAlign: 'left',
  },
  datePickerText: {
    fontSize: 15,
    paddingVertical: 2,
  },
  bottomActions: {
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 34 : 16,
    borderTopWidth: 1,
  },
  editActions: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  editButton: {
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  editButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});

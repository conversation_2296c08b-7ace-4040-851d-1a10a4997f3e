import { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Alert, ActivityIndicator, Image, Linking, Dimensions } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, router, useLocalSearchParams } from "expo-router";
import { getFarmById, deleteFarm, Farm } from "@/services/farm-service";
import { getUsers, User, getUserNameById, getUsersByFarm } from "@/services/user-service";
import { MapPin, Edit, Trash2, Users, Package, Calendar, ArrowLeft, Phone, Mail, Camera, Map, Ruler, Tag, User as UserIcon, Clock, CheckCircle, AlertCircle } from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { useFarm } from "@/context/farm-context";
import { useAuth } from "@/context/auth-context";
import { useLanguage } from "@/context/language-context";

const { width } = Dimensions.get('window');

export default function FarmDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { theme, colors } = useTheme();
  const { refreshFarms } = useFarm();
  const { user } = useAuth();
  const { t } = useLanguage();
  const isDarkMode = theme === "dark";
  
  const [farm, setFarm] = useState<Farm | null>(null);
  const [caretakers, setCaretakers] = useState<User[]>([]);
  const [ownerName, setOwnerName] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [loadingCaretakers, setLoadingCaretakers] = useState(true);
  const [loadingOwner, setLoadingOwner] = useState(true);

  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";

  useEffect(() => {
    if (id) {
      loadFarmData();
    }
  }, [id]);

  const loadFarmData = async () => {
    try {
      setLoading(true);
      const farmData = await getFarmById(id);
      if (farmData) {
        setFarm(farmData);
        
        // Load owner name
        if (farmData.ownerUid || farmData.ownerId) {
          loadOwnerName(farmData.ownerUid || farmData.ownerId || "");
        } else {
          setLoadingOwner(false);
        }
        
        // Load caretakers for this farm
        loadCaretakers(farmData.id);
      } else {
        Alert.alert(t('common.error'), t('common.farmNotFound'), [
          { text: t('common.ok'), onPress: () => router.back() }
        ]);
      }
    } catch (error) {
      console.error("Error loading farm:", error);
      Alert.alert(t('common.error'), t('farm.loadError'));
    } finally {
      setLoading(false);
    }
  };

  const loadOwnerName = async (ownerId: string) => {
    try {
      setLoadingOwner(true);
      const name = await getUserNameById(ownerId);
      setOwnerName(name);
    } catch (error) {
      console.error("Error loading owner name:", error);
      setOwnerName(t('common.unknownOwner'));
    } finally {
      setLoadingOwner(false);
    }
  };

  const loadCaretakers = async (farmId: string) => {
    try {
      setLoadingCaretakers(true);
      const farmCaretakers = await getUsersByFarm(farmId);
      setCaretakers(farmCaretakers);
      console.log(`Loaded ${farmCaretakers.length} caretakers for farm ${farmId}`);
    } catch (error) {
      console.error("Error loading caretakers:", error);
      setCaretakers([]);
    } finally {
      setLoadingCaretakers(false);
    }
  };

  const handleEditFarm = () => {
    router.push({
      pathname: "/(app)/(owner)/farms/edit",
      params: { farmId: id }
    });
  };

  const handleDeleteFarm = () => {
    if (!farm) return;
    
    Alert.alert(
      t('common.deleteFarm'),
      t('common.deleteFarmConfirmation', { farmName: farm.name }),
      [
        { text: t('common.cancel'), style: "cancel" },
        {
          text: t('common.delete'),
          style: "destructive",
          onPress: async () => {
            try {
              await deleteFarm(id, user?.role);
              Alert.alert(t('common.success'), t('common.farmDeletedSuccessfully'), [
                { text: t('common.ok'), onPress: () => {
                  refreshFarms();
                  router.back();
                }}
              ]);
            } catch (error) {
              console.error("Error deleting farm:", error);
              Alert.alert(t('common.error'), t('common.failedToDeleteFarm'));
            }
          }
        }
      ]
    );
  };

  const handleOpenMap = () => {
    if (!farm?.location) return;
    
    let latitude, longitude;
    if (typeof farm.location === 'object' && farm.location) {
      latitude = farm.location.latitude;
      longitude = farm.location.longitude;
    }
    
    if (latitude && longitude) {
      const url = `https://maps.google.com/?q=${latitude},${longitude}`;
      Linking.openURL(url).catch(() => {
        Alert.alert(t('common.error'), t('common.couldNotOpenMaps'));
      });
    }
  };

  const getLocationDisplay = (farm: Farm): string => {
    if (farm.locationData && typeof farm.locationData === 'object' && farm.locationData) {
      return farm.locationData.address || t('common.notSet');
    }
    
    if (farm.location) {
      if (typeof farm.location === 'object' && farm.location) {
        return farm.location.address || t('common.notSet');
      }
      if (typeof farm.location === 'string') {
        return farm.location;
      }
    }
    
    return t('common.notSet');
  };

  const formatDate = (dateString: string | any): string => {
    try {
      let date: Date;
      if (typeof dateString === 'string') {
        date = new Date(dateString);
      } else if (dateString && typeof dateString === 'object' && dateString.toDate) {
        // Firestore timestamp
        date = dateString.toDate();
      } else {
        return t('common.unknown');
      }
      
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      return t('common.unknown');
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'active':
        return '#4CAF50';
      case 'inactive':
        return '#FF9800';
      default:
        return secondaryTextColor;
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'active':
        return CheckCircle;
      case 'inactive':
        return AlertCircle;
      default:
        return AlertCircle;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
        <Stack.Screen
          options={{
            title: t('common.farmDetails'),
            headerStyle: { backgroundColor: colors.primary },
            headerTintColor: "#fff",
            headerTitleStyle: { fontWeight: "bold" },
            headerLeft: () => (
              <TouchableOpacity
                style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)", marginLeft: 10 }]}
                onPress={() => router.push("/(app)/(owner)/farms")}
              >
                <ArrowLeft size={24} color="#fff" />
              </TouchableOpacity>
            ),
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: secondaryTextColor }]}>
            {t('common.loadingFarmDetails')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!farm) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
        <Stack.Screen
          options={{
            title: t('common.farmDetails'),
            headerStyle: { backgroundColor: colors.primary },
            headerTintColor: "#fff",
            headerTitleStyle: { fontWeight: "bold" },
            headerLeft: () => (
              <TouchableOpacity
                style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)", marginLeft: 10 }]}
                onPress={() => router.push("/(app)/(owner)/farms")}
              >
                <ArrowLeft size={24} color="#fff" />
              </TouchableOpacity>
            ),
          }}
        />
        <View style={styles.errorContainer}>
          <AlertCircle size={64} color={secondaryTextColor} />
          <Text style={[styles.errorTitle, { color: textColor }]}>
            {t('common.farmNotFound')}
          </Text>
          <Text style={[styles.errorText, { color: secondaryTextColor }]}>
            {t('common.farmNotFoundMessage')}
          </Text>
          <TouchableOpacity 
            style={[styles.backButton, { backgroundColor: colors.primary }]}
            onPress={() => router.back()}
          >
            <ArrowLeft size={20} color="#fff" />
            <Text style={styles.backButtonText}>{t('common.goBack')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const StatusIcon = getStatusIcon(farm.status);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: farm.name,
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: "#fff",
          headerTitleStyle: { fontWeight: "bold" },
          headerLeft: () => (
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)", marginLeft: 10 }]}
              onPress={() => router.push("/(app)/(owner)/farms")}
            >
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
          headerRight: user?.role === 'owner' ? () => (
            <View style={styles.headerActions}>
              <TouchableOpacity
                style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)" }]}
                onPress={handleEditFarm}
              >
                <Edit size={20} color="#fff" />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)" }]}
                onPress={handleDeleteFarm}
              >
                <Trash2 size={20} color="#fff" />
              </TouchableOpacity>
            </View>
          ) : undefined,
        }}
      />
      
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Farm Photo */}
        {farm.photoURL && (
          <View style={styles.photoContainer}>
            <Image source={{ uri: farm.photoURL }} style={styles.farmPhoto} />
            <View style={[styles.photoOverlay, { backgroundColor: 'rgba(0, 0, 0, 0.3)' }]}>
              <View style={styles.photoInfo}>
                <Text style={styles.farmNameOverlay}>{farm.name}</Text>
                <View style={styles.statusContainer}>
                  <StatusIcon size={16} color={getStatusColor(farm.status)} />
                  <Text style={[styles.statusText, { color: getStatusColor(farm.status) }]}>
                    {farm.status?.charAt(0).toUpperCase() + farm.status?.slice(1) || t('common.unknown')}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        )}

        {/* Basic Information */}
        <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>{t('common.basicInformation')}</Text>
          
          <View style={styles.infoGrid}>
            <View style={styles.infoItem}>
              <View style={[styles.infoIcon, { backgroundColor: colors.primary + "20" }]}>
                <Tag size={20} color={colors.primary} />
              </View>
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>{t('common.farmType')}</Text>
                <Text style={[styles.infoValue, { color: textColor }]}>
                  {farm.type?.charAt(0).toUpperCase() + farm.type?.slice(1) || t('common.notSpecified')}
                </Text>
              </View>
            </View>

            <View style={styles.infoItem}>
              <View style={[styles.infoIcon, { backgroundColor: colors.primary + "20" }]}>
                <Ruler size={20} color={colors.primary} />
              </View>
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>{t('common.size')}</Text>
                <Text style={[styles.infoValue, { color: textColor }]}>
                  {farm.size} {farm.sizeUnit || 'acres'}
                </Text>
              </View>
            </View>

            {farm.description && (
              <View style={[styles.infoItem, styles.fullWidth]}>
                <View style={[styles.infoIcon, { backgroundColor: colors.primary + "20" }]}>
                  <Camera size={20} color={colors.primary} />
                </View>
                <View style={styles.infoContent}>
                  <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>{t('common.description')}</Text>
                  <Text style={[styles.infoValue, { color: textColor }]}>
                    {farm.description}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>

        {/* Location */}
        <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>{t('common.location')}</Text>
          
          <View style={styles.locationContainer}>
            <View style={styles.locationInfo}>
              <MapPin size={20} color={colors.primary} />
              <Text style={[styles.locationText, { color: textColor }]}>
                {getLocationDisplay(farm)}
              </Text>
            </View>
            
            {farm.location && typeof farm.location === 'object' && farm.location?.latitude && farm.location?.longitude && (
              <>
                <TouchableOpacity 
                  style={[styles.mapButton, { backgroundColor: colors.primary }]}
                  onPress={handleOpenMap}
                >
                  <Map size={16} color="#fff" />
                  <Text style={styles.mapButtonText}>{t('common.openInMaps')}</Text>
                </TouchableOpacity>
                
                <View style={styles.coordinatesContainer}>
                  <Text style={[styles.coordinatesLabel, { color: secondaryTextColor }]}>{t('common.coordinates')}:</Text>
                  <Text style={[styles.coordinatesText, { color: textColor }]}>
                    {farm.location?.latitude}, {farm.location?.longitude}
                  </Text>
                </View>
              </>
            )}
          </View>
        </View>

        {/* Statistics */}
        <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>{t('common.statistics')}</Text>
          
          <View style={styles.statsGrid}>
            <View style={[styles.statCard, { backgroundColor: colors.primary + "10", borderColor: colors.primary + "30" }]}>
              <Users size={24} color={colors.primary} />
              <Text style={[styles.statNumber, { color: colors.primary }]}>
                {caretakers.length || 0}
              </Text>
              <Text style={[styles.statLabel, { color: textColor }]}>{t('common.caretakers')}</Text>
            </View>
            
            <View style={[styles.statCard, { backgroundColor: "#1976D2" + "10", borderColor: "#1976D2" + "30" }]}>
              <Package size={24} color="#1976D2" />
              <Text style={[styles.statNumber, { color: "#1976D2" }]}>
                {farm.inventoryCount || 0}
              </Text>
              <Text style={[styles.statLabel, { color: textColor }]}>{t('common.inventoryItems')}</Text>
            </View>
          </View>
        </View>

        {/* Caretakers */}
        {farm.caretakerIds && farm.caretakerIds.length > 0 && (
          <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
            <Text style={[styles.sectionTitle, { color: textColor }]}>{t('common.assignedCaretakers')}</Text>
            
            {loadingCaretakers ? (
              <ActivityIndicator size="small" color={colors.primary} style={styles.loadingCaretakers} />
            ) : caretakers.length > 0 ? (
              <View style={styles.caretakersList}>
                {caretakers.map((caretaker) => (
                  <View key={caretaker.uid} style={[styles.caretakerCard, { borderColor }]}>
                    <View style={[styles.caretakerAvatar, { backgroundColor: colors.primary + "20" }]}>
                      <Text style={[styles.caretakerInitials, { color: colors.primary }]}>
                        {caretaker.displayName?.substring(0, 2).toUpperCase() || caretaker.name?.substring(0, 2).toUpperCase() || "??"}
                      </Text>
                    </View>
                    <View style={styles.caretakerInfo}>
                      <Text style={[styles.caretakerName, { color: textColor }]}>
                        {caretaker.displayName || caretaker.name}
                      </Text>
                      <Text style={[styles.caretakerEmail, { color: secondaryTextColor }]}>
                        {caretaker.email}
                      </Text>
                      {caretaker.phone && (
                        <Text style={[styles.caretakerPhone, { color: secondaryTextColor }]}>
                          {caretaker.phone}
                        </Text>
                      )}
                    </View>
                    <View style={styles.caretakerActions}>
                      {caretaker.email && (
                        <TouchableOpacity 
                          style={[styles.actionButton, { backgroundColor: "#1976D2" + "20" }]}
                          onPress={() => Linking.openURL(`mailto:${caretaker.email}`)}
                        >
                          <Mail size={16} color="#1976D2" />
                        </TouchableOpacity>
                      )}
                      {caretaker.phone && (
                        <TouchableOpacity 
                          style={[styles.actionButton, { backgroundColor: "#4CAF50" + "20" }]}
                          onPress={() => Linking.openURL(`tel:${caretaker.phone}`)}
                        >
                          <Phone size={16} color="#4CAF50" />
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                ))}
              </View>
            ) : (
              <Text style={[styles.noCaretakers, { color: secondaryTextColor }]}>
                {t('common.noCaretakersFound')}
              </Text>
            )}
          </View>
        )}

        {/* Metadata */}
        <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>{t('common.farmInformation')}</Text>
          
          <View style={styles.metadataGrid}>
            <View style={styles.metadataItem}>
              <Calendar size={16} color={secondaryTextColor} />
              <View style={styles.metadataContent}>
                <Text style={[styles.metadataLabel, { color: secondaryTextColor }]}>{t('common.created')}</Text>
                <Text style={[styles.metadataValue, { color: textColor }]}>
                  {formatDate(farm.createdAt)}
                </Text>
              </View>
            </View>
            
            {farm.updatedAt && (
              <View style={styles.metadataItem}>
                <Clock size={16} color={secondaryTextColor} />
                <View style={styles.metadataContent}>
                  <Text style={[styles.metadataLabel, { color: secondaryTextColor }]}>{t('common.lastUpdated')}</Text>
                  <Text style={[styles.metadataValue, { color: textColor }]}>
                    {formatDate(farm.updatedAt)}
                  </Text>
                </View>
              </View>
            )}
            
            <View style={styles.metadataItem}>
              <UserIcon size={16} color={secondaryTextColor} />
              <View style={styles.metadataContent}>
                <Text style={[styles.metadataLabel, { color: secondaryTextColor }]}>{t('common.owner')}</Text>
                {loadingOwner ? (
                  <ActivityIndicator size="small" color={colors.primary} />
                ) : (
                  <Text style={[styles.metadataValue, { color: textColor }]} numberOfLines={1}>
                    {ownerName || t('common.unknownOwner')}
                  </Text>
                )}
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 16,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 24,
    lineHeight: 22,
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  backButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  headerActions: {
    flexDirection: "row",
    gap: 8,
  },
  headerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  photoContainer: {
    position: "relative",
    height: 200,
  },
  farmPhoto: {
    width: "100%",
    height: "100%",
  },
  photoOverlay: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
  },
  photoInfo: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
  },
  farmNameOverlay: {
    color: "#fff",
    fontSize: 24,
    fontWeight: "bold",
    flex: 1,
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
    marginLeft: 4,
  },
  section: {
    margin: 16,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  infoGrid: {
    gap: 16,
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  fullWidth: {
    width: "100%",
  },
  infoIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    fontWeight: "500",
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: "600",
  },
  locationContainer: {
    gap: 12,
  },
  locationInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  locationText: {
    fontSize: 16,
    marginLeft: 8,
    flex: 1,
  },
  mapButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignSelf: "flex-start",
  },
  mapButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 6,
  },
  coordinatesContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  coordinatesLabel: {
    fontSize: 14,
    marginRight: 8,
  },
  coordinatesText: {
    fontSize: 14,
    fontFamily: "monospace",
  },
  statsGrid: {
    flexDirection: "row",
    gap: 12,
  },
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
    borderWidth: 1,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "bold",
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: "500",
    marginTop: 4,
    textAlign: "center",
  },
  loadingCaretakers: {
    marginVertical: 20,
  },
  caretakersList: {
    gap: 12,
  },
  caretakerCard: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  caretakerAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  caretakerInitials: {
    fontSize: 18,
    fontWeight: "bold",
  },
  caretakerInfo: {
    flex: 1,
  },
  caretakerName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  caretakerEmail: {
    fontSize: 14,
    marginBottom: 2,
  },
  caretakerPhone: {
    fontSize: 14,
  },
  caretakerActions: {
    flexDirection: "row",
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  noCaretakers: {
    textAlign: "center",
    fontSize: 14,
    fontStyle: "italic",
    marginVertical: 20,
  },
  metadataGrid: {
    gap: 12,
  },
  metadataItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  metadataContent: {
    marginLeft: 12,
    flex: 1,
  },
  metadataLabel: {
    fontSize: 12,
    fontWeight: "500",
    marginBottom: 2,
  },
  metadataValue: {
    fontSize: 14,
    fontWeight: "600",
  },
});
import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, ActivityIndicator, RefreshControl, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useLocalSearchParams } from "expo-router";
import { AlertTriangle, Clock, Package } from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { useFarm } from "@/context/farm-context";
import { getInventoryItems } from "@/services/inventory-service";

interface AlertItem {
  id: string;
  name: string;
  category: string;
  currentStock: number;
  minQuantity: number;
  expiryDate?: string;
  alertType: "low_stock" | "expiring" | "expired";
  severity: "high" | "medium" | "low";
}

export default function LowStockAlertsScreen() {
  const { farmId } = useLocalSearchParams();
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";
  const { selectedFarm } = useFarm();
  
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [alerts, setAlerts] = useState<AlertItem[]>([]);
  const [filter, setFilter] = useState<"all" | "low_stock" | "expiring" | "expired">("all");
  
  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";
  
  useEffect(() => {
    loadAlerts();
  }, [farmId, selectedFarm]);
  
  const loadAlerts = async () => {
    setLoading(true);
    try {
      const items = await getInventoryItems(farmId as string || selectedFarm?.id || "");
      const alertItems: AlertItem[] = [];
      
      const today = new Date();
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(today.getDate() + 30);
      
      items.forEach(item => {
        // Check for low stock
        if (item.quantity <= (item.minQuantity || 0)) {
          alertItems.push({
            id: item.id,
            name: item.name,
            category: item.category,
            currentStock: item.quantity,
            minQuantity: item.minQuantity || 0,
            alertType: "low_stock",
            severity: item.quantity === 0 ? "high" : item.quantity <= (item.minQuantity || 0) * 0.5 ? "medium" : "low"
          });
        }
        
        // Check for expiring items
        if (item.expiryDate) {
          const expiryDate = new Date(item.expiryDate);
          if (expiryDate <= today) {
            alertItems.push({
              id: item.id + "_expired",
              name: item.name,
              category: item.category,
              currentStock: item.quantity,
              minQuantity: item.minQuantity || 0,
              expiryDate: item.expiryDate,
              alertType: "expired",
              severity: "high"
            });
          } else if (expiryDate <= thirtyDaysFromNow) {
            const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
            alertItems.push({
              id: item.id + "_expiring",
              name: item.name,
              category: item.category,
              currentStock: item.quantity,
              minQuantity: item.minQuantity || 0,
              expiryDate: item.expiryDate,
              alertType: "expiring",
              severity: daysUntilExpiry <= 7 ? "high" : daysUntilExpiry <= 14 ? "medium" : "low"
            });
          }
        }
      });
      
      // Sort by severity (high first)
      alertItems.sort((a, b) => {
        const severityOrder = { high: 3, medium: 2, low: 1 };
        return severityOrder[b.severity] - severityOrder[a.severity];
      });
      
      setAlerts(alertItems);
    } catch (error) {
      console.error("Error loading alerts:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  const onRefresh = () => {
    setRefreshing(true);
    loadAlerts();
  };
  
  const getAlertColor = (alertType: string, severity: string) => {
    if (alertType === "expired") return "#D32F2F";
    if (severity === "high") return "#FF5722";
    if (severity === "medium") return "#FF9800";
    return "#FFC107";
  };
  
  const getAlertIcon = (alertType: string) => {
    switch (alertType) {
      case "expired":
        return <AlertTriangle size={20} color="#D32F2F" />;
      case "expiring":
        return <Clock size={20} color="#FF9800" />;
      case "low_stock":
        return <Package size={20} color="#FF5722" />;
      default:
        return <AlertTriangle size={20} color="#FF9800" />;
    }
  };
  
  const getAlertTitle = (alertType: string) => {
    switch (alertType) {
      case "expired":
        return "Expired";
      case "expiring":
        return "Expiring Soon";
      case "low_stock":
        return "Low Stock";
      default:
        return "Alert";
    }
  };
  
  const getDaysUntilExpiry = (expiryDate: string) => {
    const today = new Date();
    const expiry = new Date(expiryDate);
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };
  
  const filteredAlerts = filter === "all" ? alerts : alerts.filter(alert => alert.alertType === filter);
  
  const alertCounts = {
    all: alerts.length,
    low_stock: alerts.filter(a => a.alertType === "low_stock").length,
    expiring: alerts.filter(a => a.alertType === "expiring").length,
    expired: alerts.filter(a => a.alertType === "expired").length,
  };
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen 
        options={{
          title: "Low Stock & Expiry Alerts",
          headerStyle: {
            backgroundColor: isDarkMode ? "#1e1e1e" : "#D32F2F",
          },
          headerTintColor: "#fff",
          headerBackVisible: true,
        }} 
      />
      
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.scrollContent}
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={isDarkMode ? "#FF5722" : "#D32F2F"} />
            <Text style={[styles.loadingText, { color: secondaryTextColor }]}>Loading alerts...</Text>
          </View>
        ) : (
          <>
            <View style={styles.headerContainer}>
              <Text style={[styles.headerTitle, { color: textColor }]}>Stock & Expiry Alerts</Text>
              <Text style={[styles.headerSubtitle, { color: secondaryTextColor }]}>
                {selectedFarm ? `For ${selectedFarm.name}` : "All Farms"}
              </Text>
            </View>
            
            <View style={[styles.summaryCard, { backgroundColor: cardColor, borderColor }]}>
              <Text style={[styles.summaryTitle, { color: textColor }]}>Alert Summary</Text>
              <View style={styles.summaryGrid}>
                <View style={styles.summaryItem}>
                  <Text style={[styles.summaryValue, { color: "#D32F2F" }]}>{alertCounts.expired}</Text>
                  <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Expired</Text>
                </View>
                <View style={styles.summaryItem}>
                  <Text style={[styles.summaryValue, { color: "#FF9800" }]}>{alertCounts.expiring}</Text>
                  <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Expiring Soon</Text>
                </View>
                <View style={styles.summaryItem}>
                  <Text style={[styles.summaryValue, { color: "#FF5722" }]}>{alertCounts.low_stock}</Text>
                  <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Low Stock</Text>
                </View>
                <View style={styles.summaryItem}>
                  <Text style={[styles.summaryValue, { color: "#666" }]}>{alertCounts.all}</Text>
                  <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Total Alerts</Text>
                </View>
              </View>
            </View>
            
            <View style={styles.filterContainer}>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {[
                  { key: "all", label: "All", count: alertCounts.all },
                  { key: "expired", label: "Expired", count: alertCounts.expired },
                  { key: "expiring", label: "Expiring", count: alertCounts.expiring },
                  { key: "low_stock", label: "Low Stock", count: alertCounts.low_stock },
                ].map((filterOption) => (
                  <TouchableOpacity
                    key={filterOption.key}
                    style={[
                      styles.filterButton,
                      {
                        backgroundColor: filter === filterOption.key 
                          ? (isDarkMode ? "#D32F2F" : "#D32F2F")
                          : (isDarkMode ? "#333" : "#f0f0f0")
                      }
                    ]}
                    onPress={() => setFilter(filterOption.key as any)}
                  >
                    <Text style={[
                      styles.filterButtonText,
                      {
                        color: filter === filterOption.key 
                          ? "#fff" 
                          : (isDarkMode ? "#aaa" : "#666")
                      }
                    ]}>
                      {filterOption.label} ({filterOption.count})
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
            
            <View style={styles.alertsContainer}>
              {filteredAlerts.length === 0 ? (
                <View style={[styles.emptyState, { backgroundColor: cardColor }]}>
                  <Package size={48} color={isDarkMode ? "#555" : "#ccc"} />
                  <Text style={[styles.emptyStateText, { color: textColor }]}>
                    {filter === "all" ? "No alerts found" : `No ${filter.replace("_", " ")} alerts`}
                  </Text>
                  <Text style={[styles.emptyStateSubtext, { color: secondaryTextColor }]}>
                    All items are properly stocked and within expiry dates
                  </Text>
                </View>
              ) : (
                filteredAlerts.map((alert) => (
                  <View key={alert.id} style={[styles.alertCard, { backgroundColor: cardColor, borderColor }]}>
                    <View style={styles.alertHeader}>
                      <View style={styles.alertIconContainer}>
                        {getAlertIcon(alert.alertType)}
                        <View style={[styles.severityIndicator, { backgroundColor: getAlertColor(alert.alertType, alert.severity) }]} />
                      </View>
                      <View style={styles.alertInfo}>
                        <Text style={[styles.alertItemName, { color: textColor }]}>{alert.name}</Text>
                        <Text style={[styles.alertCategory, { color: secondaryTextColor }]}>{alert.category}</Text>
                      </View>
                      <View style={styles.alertBadge}>
                        <Text style={[styles.alertBadgeText, { color: getAlertColor(alert.alertType, alert.severity) }]}>
                          {getAlertTitle(alert.alertType)}
                        </Text>
                      </View>
                    </View>
                    
                    <View style={styles.alertDetails}>
                      {alert.alertType === "low_stock" && (
                        <Text style={[styles.alertDetailText, { color: secondaryTextColor }]}>
                          Current stock: {alert.currentStock} (Min: {alert.minQuantity})
                        </Text>
                      )}
                      {alert.alertType === "expiring" && alert.expiryDate && (
                        <Text style={[styles.alertDetailText, { color: secondaryTextColor }]}>
                          Expires in {getDaysUntilExpiry(alert.expiryDate)} days ({new Date(alert.expiryDate).toLocaleDateString()})
                        </Text>
                      )}
                      {alert.alertType === "expired" && alert.expiryDate && (
                        <Text style={[styles.alertDetailText, { color: "#D32F2F" }]}>
                          Expired on {new Date(alert.expiryDate).toLocaleDateString()}
                        </Text>
                      )}
                    </View>
                  </View>
                ))
              )}
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  headerContainer: {
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
  },
  summaryCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  summaryItem: {
    width: "48%",
    alignItems: "center",
    marginBottom: 12,
    padding: 12,
    borderRadius: 8,
    backgroundColor: "rgba(211, 47, 47, 0.1)",
  },
  summaryValue: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 14,
    textAlign: "center",
  },
  filterContainer: {
    marginBottom: 16,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: "500",
  },
  alertsContainer: {
    gap: 12,
  },
  emptyState: {
    borderRadius: 12,
    padding: 24,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: "600",
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    textAlign: "center",
  },
  alertCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  alertHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  alertIconContainer: {
    position: "relative",
    marginRight: 12,
  },
  severityIndicator: {
    position: "absolute",
    top: -2,
    right: -2,
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  alertInfo: {
    flex: 1,
  },
  alertItemName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  alertCategory: {
    fontSize: 14,
  },
  alertBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: "rgba(211, 47, 47, 0.1)",
  },
  alertBadgeText: {
    fontSize: 12,
    fontWeight: "500",
  },
  alertDetails: {
    marginTop: 8,
  },
  alertDetailText: {
    fontSize: 14,
  },
});
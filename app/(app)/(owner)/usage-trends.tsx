import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, ActivityIndicator, RefreshControl } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useLocalSearchParams } from "expo-router";
import { useTheme } from "@/context/theme-context";
import { useFarm } from "@/context/farm-context";
import { getInventoryReport } from "@/services/reports-service";

export default function UsageTrendsScreen() {
  const { farmId } = useLocalSearchParams();
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";
  const { selectedFarm } = useFarm();
  
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [reportData, setReportData] = useState<any>(null);
  
  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";
  
  useEffect(() => {
    loadReportData();
  }, [farmId, selectedFarm]);
  
  const loadReportData = async () => {
    setLoading(true);
    try {
      const data = await getInventoryReport(farmId as string || selectedFarm?.id || "");
      setReportData(data);
    } catch (error) {
      console.error("Error loading usage trends:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  const onRefresh = () => {
    setRefreshing(true);
    loadReportData();
  };
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen 
        options={{
          title: "Usage Trends",
          headerStyle: {
            backgroundColor: isDarkMode ? "#1e1e1e" : "#1976D2",
          },
          headerTintColor: "#fff",
          headerBackVisible: true,
        }} 
      />
      
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.scrollContent}
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={isDarkMode ? "#2196F3" : "#1976D2"} />
            <Text style={[styles.loadingText, { color: secondaryTextColor }]}>Loading usage trends...</Text>
          </View>
        ) : (
          <>
            <View style={styles.headerContainer}>
              <Text style={[styles.headerTitle, { color: textColor }]}>Usage Trends Analysis</Text>
              <Text style={[styles.headerSubtitle, { color: secondaryTextColor }]}>
                {selectedFarm ? `For ${selectedFarm.name}` : "All Farms"}
              </Text>
            </View>
            
            {reportData && (
              <View style={styles.reportContainer}>
                <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
                  <Text style={[styles.cardTitle, { color: textColor }]}>Monthly Usage Trends</Text>
                  <View style={styles.chartContainer}>
                    {reportData.trendData && reportData.trendData.labels.map((label: string, index: number) => (
                      <View key={index} style={styles.trendItem}>
                        <Text style={[styles.monthLabel, { color: textColor }]}>{label}</Text>
                        <View style={styles.trendBars}>
                          <View style={styles.trendBarContainer}>
                            <View 
                              style={[
                                styles.trendBar, 
                                { 
                                  width: `${(reportData.trendData.datasets[0].data[index] / 100) * 100}%`,
                                  backgroundColor: '#2196F3'
                                }
                              ]} 
                            />
                            <Text style={[styles.trendValue, { color: secondaryTextColor }]}>
                              {reportData.trendData.datasets[0].data[index]} added
                            </Text>
                          </View>
                          <View style={styles.trendBarContainer}>
                            <View 
                              style={[
                                styles.trendBar, 
                                { 
                                  width: `${(reportData.trendData.datasets[1].data[index] / 100) * 100}%`,
                                  backgroundColor: '#FF5722'
                                }
                              ]} 
                            />
                            <Text style={[styles.trendValue, { color: secondaryTextColor }]}>
                              {reportData.trendData.datasets[1].data[index]} used
                            </Text>
                          </View>
                        </View>
                      </View>
                    ))}
                  </View>
                </View>
                
                <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
                  <Text style={[styles.cardTitle, { color: textColor }]}>Top Used Items</Text>
                  <View style={styles.chartContainer}>
                    {reportData.inventoryLevels && reportData.inventoryLevels.labels.map((label: string, index: number) => (
                      <View key={index} style={styles.usageItem}>
                        <View style={styles.usageRank}>
                          <Text style={[styles.rankNumber, { color: "#fff" }]}>{index + 1}</Text>
                        </View>
                        <View style={styles.usageDetails}>
                          <Text style={[styles.itemName, { color: textColor }]}>{label}</Text>
                          <View style={styles.usageBar}>
                            <View 
                              style={[
                                styles.usageProgress, 
                                { 
                                  width: `${(reportData.inventoryLevels.datasets[0].data[index] / Math.max(...reportData.inventoryLevels.datasets[0].data)) * 100}%`,
                                  backgroundColor: index < 2 ? '#FF5722' : index < 4 ? '#FF9800' : '#4CAF50'
                                }
                              ]} 
                            />
                          </View>
                          <Text style={[styles.usageCount, { color: secondaryTextColor }]}>
                            {reportData.inventoryLevels.datasets[0].data[index]} times used
                          </Text>
                        </View>
                      </View>
                    ))}
                  </View>
                </View>
                
                <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
                  <Text style={[styles.cardTitle, { color: textColor }]}>Usage Insights</Text>
                  <View style={styles.insightsContainer}>
                    <View style={styles.insightItem}>
                      <Text style={[styles.insightTitle, { color: textColor }]}>Peak Usage Month</Text>
                      <Text style={[styles.insightValue, { color: "#2196F3" }]}>
                        {reportData.trendData?.labels[reportData.trendData.datasets[1].data.indexOf(Math.max(...reportData.trendData.datasets[1].data))]}
                      </Text>
                    </View>
                    <View style={styles.insightItem}>
                      <Text style={[styles.insightTitle, { color: textColor }]}>Average Monthly Usage</Text>
                      <Text style={[styles.insightValue, { color: "#FF5722" }]}>
                        {Math.round(reportData.trendData?.datasets[1].data.reduce((a: number, b: number) => a + b, 0) / reportData.trendData?.datasets[1].data.length)} items
                      </Text>
                    </View>
                    <View style={styles.insightItem}>
                      <Text style={[styles.insightTitle, { color: textColor }]}>Usage Efficiency</Text>
                      <Text style={[styles.insightValue, { color: "#4CAF50" }]}>
                        {Math.round((reportData.trendData?.datasets[1].data.reduce((a: number, b: number) => a + b, 0) / reportData.trendData?.datasets[0].data.reduce((a: number, b: number) => a + b, 0)) * 100)}%
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
            )}
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  headerContainer: {
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
  },
  reportContainer: {
    gap: 16,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  chartContainer: {
    padding: 8,
  },
  trendItem: {
    marginBottom: 20,
  },
  monthLabel: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 8,
  },
  trendBars: {
    gap: 6,
  },
  trendBarContainer: {
    flexDirection: "row",
    alignItems: "center",
    height: 24,
  },
  trendBar: {
    height: 20,
    borderRadius: 4,
    marginRight: 12,
    minWidth: 4,
  },
  trendValue: {
    fontSize: 12,
  },
  usageItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
    backgroundColor: "rgba(33, 150, 243, 0.1)",
  },
  usageRank: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#2196F3",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  rankNumber: {
    fontSize: 14,
    fontWeight: "bold",
  },
  usageDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 4,
  },
  usageBar: {
    height: 6,
    backgroundColor: "rgba(0,0,0,0.1)",
    borderRadius: 3,
    marginBottom: 4,
  },
  usageProgress: {
    height: 6,
    borderRadius: 3,
  },
  usageCount: {
    fontSize: 12,
  },
  insightsContainer: {
    gap: 16,
  },
  insightItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 12,
    borderRadius: 8,
    backgroundColor: "rgba(33, 150, 243, 0.05)",
  },
  insightTitle: {
    fontSize: 14,
    flex: 1,
  },
  insightValue: {
    fontSize: 16,
    fontWeight: "600",
  },
});
import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, ActivityIndicator, RefreshControl, Dimensions } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useLocalSearchParams } from "expo-router";
import { useTheme } from "@/context/theme-context";
import { useFarm } from "@/context/farm-context";
import { getInventoryReport } from "@/services/reports-service";

export default function InventorySummaryScreen() {
  const { farmId } = useLocalSearchParams();
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";
  const { selectedFarm } = useFarm();
  
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [reportData, setReportData] = useState<any>(null);
  
  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";
  
  useEffect(() => {
    loadReportData();
  }, [farmId, selectedFarm]);
  
  const loadReportData = async () => {
    setLoading(true);
    try {
      const data = await getInventoryReport(farmId as string || selectedFarm?.id || "");
      setReportData(data);
    } catch (error) {
      console.error("Error loading inventory report:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  const onRefresh = () => {
    setRefreshing(true);
    loadReportData();
  };
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen 
        options={{
          title: "Inventory Summary",
          headerStyle: {
            backgroundColor: isDarkMode ? "#1e1e1e" : "#2E7D32",
          },
          headerTintColor: "#fff",
          headerBackVisible: true,
        }} 
      />
      
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.scrollContent}
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={isDarkMode ? "#4CAF50" : "#2E7D32"} />
            <Text style={[styles.loadingText, { color: secondaryTextColor }]}>Loading inventory summary...</Text>
          </View>
        ) : (
          <>
            <View style={styles.headerContainer}>
              <Text style={[styles.headerTitle, { color: textColor }]}>Inventory Summary</Text>
              <Text style={[styles.headerSubtitle, { color: secondaryTextColor }]}>
                {selectedFarm ? `For ${selectedFarm.name}` : "All Farms"}
              </Text>
            </View>
            
            {reportData && (
              <View style={styles.reportContainer}>
                <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
                  <Text style={[styles.cardTitle, { color: textColor }]}>Summary Overview</Text>
                  <View style={styles.summaryGrid}>
                    <View style={styles.summaryItem}>
                      <Text style={[styles.summaryValue, { color: "#2E7D32" }]}>{reportData.summary.totalItems}</Text>
                      <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Total Items</Text>
                    </View>
                    <View style={styles.summaryItem}>
                      <Text style={[styles.summaryValue, { color: "#D32F2F" }]}>{reportData.summary.lowStockItems}</Text>
                      <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Low Stock</Text>
                    </View>
                    <View style={styles.summaryItem}>
                      <Text style={[styles.summaryValue, { color: "#FF9800" }]}>{reportData.summary.expiringItems}</Text>
                      <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Expiring Soon</Text>
                    </View>
                    <View style={styles.summaryItem}>
                      <Text style={[styles.summaryValue, { color: "#1976D2" }]}>${reportData.summary.totalValue.toFixed(2)}</Text>
                      <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Total Value</Text>
                    </View>
                  </View>
                </View>
                
                <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
                  <Text style={[styles.cardTitle, { color: textColor }]}>Inventory by Category</Text>
                  <View style={styles.chartContainer}>
                    {reportData.categoryData && reportData.categoryData.map((item: any, index: number) => (
                      <View key={index} style={styles.categoryItem}>
                        <View style={[styles.categoryColor, { backgroundColor: item.color }]} />
                        <Text style={[styles.categoryName, { color: textColor }]}>{item.name}</Text>
                        <Text style={[styles.categoryCount, { color: secondaryTextColor }]}>{item.count} items</Text>
                      </View>
                    ))}
                  </View>
                </View>
                
                <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
                  <Text style={[styles.cardTitle, { color: textColor }]}>Current Stock Levels</Text>
                  <View style={styles.chartContainer}>
                    {reportData.inventoryLevels && reportData.inventoryLevels.labels.map((label: string, index: number) => (
                      <View key={index} style={styles.barItem}>
                        <Text style={[styles.barLabel, { color: textColor }]}>{label}</Text>
                        <View style={styles.barContainer}>
                          <View 
                            style={[
                              styles.bar, 
                              { 
                                width: `${(reportData.inventoryLevels.datasets[0].data[index] / Math.max(...reportData.inventoryLevels.datasets[0].data)) * 100}%`,
                                backgroundColor: '#4CAF50'
                              }
                            ]} 
                          />
                          <Text style={[styles.barValue, { color: textColor }]}>
                            {reportData.inventoryLevels.datasets[0].data[index]}
                          </Text>
                        </View>
                      </View>
                    ))}
                  </View>
                </View>
              </View>
            )}
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  headerContainer: {
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
  },
  reportContainer: {
    gap: 16,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  summaryItem: {
    width: "48%",
    alignItems: "center",
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
    backgroundColor: "rgba(46, 125, 50, 0.1)",
  },
  summaryValue: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 14,
    textAlign: "center",
  },
  chartContainer: {
    padding: 8,
  },
  categoryItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
    paddingVertical: 8,
  },
  categoryColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  categoryName: {
    fontSize: 16,
    flex: 1,
  },
  categoryCount: {
    fontSize: 14,
  },
  barItem: {
    marginBottom: 16,
  },
  barLabel: {
    fontSize: 14,
    marginBottom: 6,
    fontWeight: "500",
  },
  barContainer: {
    flexDirection: "row",
    alignItems: "center",
    height: 28,
  },
  bar: {
    height: 24,
    borderRadius: 4,
    marginRight: 12,
    minWidth: 4,
  },
  barValue: {
    fontSize: 14,
    fontWeight: "500",
    minWidth: 30,
  },
});
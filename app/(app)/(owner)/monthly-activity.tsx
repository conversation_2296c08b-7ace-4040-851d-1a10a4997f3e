import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, ActivityIndicator, RefreshControl } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useLocalSearchParams } from "expo-router";
import { Calendar, TrendingUp, Users, Package, FileText } from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { useFarm } from "@/context/farm-context";
import { getActivitiesReport } from "@/services/reports-service";

export default function MonthlyActivityScreen() {
  const { farmId } = useLocalSearchParams();
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";
  const { selectedFarm } = useFarm();
  
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [reportData, setReportData] = useState<any>(null);
  
  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";
  
  useEffect(() => {
    loadReportData();
  }, [farmId, selectedFarm]);
  
  const loadReportData = async () => {
    setLoading(true);
    try {
      const data = await getActivitiesReport(farmId as string || selectedFarm?.id);
      setReportData(data);
    } catch (error) {
      console.error("Error loading monthly activity:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  const onRefresh = () => {
    setRefreshing(true);
    loadReportData();
  };
  
  const getActivityIcon = (activityType: string) => {
    if (activityType.includes("inventory")) return <Package size={16} color="#4CAF50" />;
    if (activityType.includes("request")) return <FileText size={16} color="#2196F3" />;
    if (activityType.includes("user")) return <Users size={16} color="#FF9800" />;
    if (activityType.includes("note")) return <FileText size={16} color="#9C27B0" />;
    return <TrendingUp size={16} color="#607D8B" />;
  };
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen 
        options={{
          title: "Monthly Activity",
          headerStyle: {
            backgroundColor: isDarkMode ? "#1e1e1e" : "#7B1FA2",
          },
          headerTintColor: "#fff",
          headerBackVisible: true,
        }} 
      />
      
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.scrollContent}
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={isDarkMode ? "#9C27B0" : "#7B1FA2"} />
            <Text style={[styles.loadingText, { color: secondaryTextColor }]}>Loading activity data...</Text>
          </View>
        ) : (
          <>
            <View style={styles.headerContainer}>
              <Text style={[styles.headerTitle, { color: textColor }]}>Monthly Activity Report</Text>
              <Text style={[styles.headerSubtitle, { color: secondaryTextColor }]}>
                {selectedFarm ? `For ${selectedFarm.name}` : "All Farms"}
              </Text>
            </View>
            
            {reportData && (
              <View style={styles.reportContainer}>
                <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
                  <Text style={[styles.cardTitle, { color: textColor }]}>Activity Summary</Text>
                  <View style={styles.summaryGrid}>
                    <View style={styles.summaryItem}>
                      <Calendar size={24} color="#7B1FA2" />
                      <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary.monthActivities}</Text>
                      <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>This Month</Text>
                    </View>
                    <View style={styles.summaryItem}>
                      <TrendingUp size={24} color="#4CAF50" />
                      <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary.weekActivities}</Text>
                      <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>This Week</Text>
                    </View>
                    <View style={styles.summaryItem}>
                      <Calendar size={24} color="#FF9800" />
                      <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary.todayActivities}</Text>
                      <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Today</Text>
                    </View>
                    <View style={styles.summaryItem}>
                      <TrendingUp size={24} color="#2196F3" />
                      <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary.totalActivities}</Text>
                      <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Total</Text>
                    </View>
                  </View>
                </View>
                
                <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
                  <Text style={[styles.cardTitle, { color: textColor }]}>Activity by Type</Text>
                  <View style={styles.chartContainer}>
                    {reportData.activityTypeData && reportData.activityTypeData.map((item: any, index: number) => (
                      <View key={index} style={styles.activityTypeItem}>
                        <View style={styles.activityTypeHeader}>
                          {getActivityIcon(item.name)}
                          <Text style={[styles.activityTypeName, { color: textColor }]}>{item.name}</Text>
                          <Text style={[styles.activityTypeCount, { color: secondaryTextColor }]}>{item.count}</Text>
                        </View>
                        <View style={styles.activityTypeBar}>
                          <View 
                            style={[
                              styles.activityTypeProgress, 
                              { 
                                width: `${(item.count / Math.max(...reportData.activityTypeData.map((d: any) => d.count))) * 100}%`,
                                backgroundColor: item.color
                              }
                            ]} 
                          />
                        </View>
                      </View>
                    ))}
                  </View>
                </View>
                
                <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
                  <Text style={[styles.cardTitle, { color: textColor }]}>Daily Activity Trend</Text>
                  <View style={styles.chartContainer}>
                    {reportData.trendData && reportData.trendData.labels.map((label: string, index: number) => (
                      <View key={index} style={styles.trendItem}>
                        <Text style={[styles.dayLabel, { color: textColor }]}>{label}</Text>
                        <View style={styles.trendBarContainer}>
                          <View 
                            style={[
                              styles.trendBar, 
                              { 
                                width: `${(reportData.trendData.datasets[0].data[index] / Math.max(...reportData.trendData.datasets[0].data)) * 100}%`,
                                backgroundColor: '#7B1FA2'
                              }
                            ]} 
                          />
                          <Text style={[styles.trendValue, { color: secondaryTextColor }]}>
                            {reportData.trendData.datasets[0].data[index]} activities
                          </Text>
                        </View>
                      </View>
                    ))}
                  </View>
                </View>
                
                <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
                  <Text style={[styles.cardTitle, { color: textColor }]}>Recent Activity Highlights</Text>
                  <View style={styles.highlightsContainer}>
                    <View style={styles.highlightItem}>
                      <Package size={20} color="#4CAF50" />
                      <View style={styles.highlightContent}>
                        <Text style={[styles.highlightTitle, { color: textColor }]}>Inventory Updates</Text>
                        <Text style={[styles.highlightDescription, { color: secondaryTextColor }]}>
                          {reportData.activityTypeData?.find((item: any) => item.name.includes("Inventory"))?.count || 0} items updated this month
                        </Text>
                      </View>
                    </View>
                    
                    <View style={styles.highlightItem}>
                      <FileText size={20} color="#2196F3" />
                      <View style={styles.highlightContent}>
                        <Text style={[styles.highlightTitle, { color: textColor }]}>Request Activity</Text>
                        <Text style={[styles.highlightDescription, { color: secondaryTextColor }]}>
                          {(reportData.activityTypeData?.find((item: any) => item.name.includes("Request Created"))?.count || 0) + 
                           (reportData.activityTypeData?.find((item: any) => item.name.includes("Request Approved"))?.count || 0)} requests processed
                        </Text>
                      </View>
                    </View>
                    
                    <View style={styles.highlightItem}>
                      <Users size={20} color="#FF9800" />
                      <View style={styles.highlightContent}>
                        <Text style={[styles.highlightTitle, { color: textColor }]}>User Engagement</Text>
                        <Text style={[styles.highlightDescription, { color: secondaryTextColor }]}>
                          {reportData.activityTypeData?.find((item: any) => item.name.includes("User"))?.count || 0} user activities recorded
                        </Text>
                      </View>
                    </View>
                    
                    <View style={styles.highlightItem}>
                      <FileText size={20} color="#9C27B0" />
                      <View style={styles.highlightContent}>
                        <Text style={[styles.highlightTitle, { color: textColor }]}>Notes & Documentation</Text>
                        <Text style={[styles.highlightDescription, { color: secondaryTextColor }]}>
                          {reportData.activityTypeData?.find((item: any) => item.name.includes("Note"))?.count || 0} notes created this month
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            )}
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  headerContainer: {
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
  },
  reportContainer: {
    gap: 16,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  summaryItem: {
    width: "48%",
    alignItems: "center",
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
    backgroundColor: "rgba(123, 31, 162, 0.1)",
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: "bold",
    marginTop: 8,
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    textAlign: "center",
  },
  chartContainer: {
    padding: 8,
  },
  activityTypeItem: {
    marginBottom: 16,
  },
  activityTypeHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 6,
  },
  activityTypeName: {
    fontSize: 14,
    flex: 1,
    marginLeft: 8,
  },
  activityTypeCount: {
    fontSize: 14,
    fontWeight: "500",
  },
  activityTypeBar: {
    height: 6,
    backgroundColor: "rgba(0,0,0,0.1)",
    borderRadius: 3,
  },
  activityTypeProgress: {
    height: 6,
    borderRadius: 3,
  },
  trendItem: {
    marginBottom: 16,
  },
  dayLabel: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 6,
  },
  trendBarContainer: {
    flexDirection: "row",
    alignItems: "center",
    height: 24,
  },
  trendBar: {
    height: 20,
    borderRadius: 4,
    marginRight: 12,
    minWidth: 4,
  },
  trendValue: {
    fontSize: 12,
  },
  highlightsContainer: {
    gap: 16,
  },
  highlightItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    padding: 12,
    borderRadius: 8,
    backgroundColor: "rgba(123, 31, 162, 0.05)",
  },
  highlightContent: {
    flex: 1,
    marginLeft: 12,
  },
  highlightTitle: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 4,
  },
  highlightDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
});
import React, { useState, useEffect, useCallback, useRef } from "react";
import { 
  StyleSheet, 
  Text, 
  View, 
  FlatList, 
  TouchableOpacity, 
  TextInput, 
  RefreshControl, 
  Alert, 
  ActivityIndicator, 
  Animated,
  Dimensions,
  ScrollView
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter } from "expo-router";
import { 
  getInventoryItems, 
  InventoryItem, 
  deleteInventoryItem, 
  subscribeToInventory 
} from "@/services/inventory-service";
import { 
  Search, 
  Plus, 
  AlertTriangle, 
  Package,
  Grid,
  List,
  Filter
} from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { FilterButton } from "@/components/FilterButton";
import { InventoryItemComponent } from "@/components/InventoryItem";
import { useFarm } from "@/context/farm-context";
import { useAuth } from "@/context/auth-context";
import { useFocusEffect } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";

export default function OwnerInventoryScreen() {
  const { theme, colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const isDarkMode = theme === "dark";
  const { selectedFarm, farms } = useFarm();
  const { user } = useAuth();
  const router = useRouter();
  
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<InventoryItem[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeFilter, setActiveFilter] = useState("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("list");
  
  // Animation values
  const scrollY = useRef(new Animated.Value(0)).current;
  
  // Theme colors
  const backgroundColor = isDarkMode ? "#121212" : "#f8f9fa";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";
  const primaryColor = colors.primary;
  
  // Screen dimensions
  const screenWidth = Dimensions.get("window").width;
  
  // Refs
  const scrollRef = useRef(null);

  useEffect(() => {
    // If no farm is selected, don't load inventory
    if (!selectedFarm) {
      setInventoryItems([]);
      setFilteredItems([]);
      setLoading(false);
      return;
    }
    
    // Set up real-time listener for inventory changes
    const unsubscribe = subscribeToInventory(selectedFarm.id, (items) => {
      setInventoryItems(items);
      applyFilters(items, activeFilter, searchQuery);
      setLoading(false);
      setRefreshing(false);
    });
    
    // Clean up listener on unmount
    return () => {
      unsubscribe();
    };
  }, [selectedFarm]);

  // Refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      if (selectedFarm) {
        // The real-time listener will update the data
        // This is just to show the refresh indicator
        setRefreshing(true);
        setTimeout(() => {
          setRefreshing(false);
        }, 500);
      }
      return () => {};
    }, [selectedFarm])
  );

  const onRefresh = () => {
    setRefreshing(true);
    // The real-time listener will update the data
    // This is just to show the refresh indicator
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    applyFilters(inventoryItems, activeFilter, text);
  };

  const applyFilters = (items: InventoryItem[], filter: string, search: string = "") => {
    let filtered = [...items];
    
    // Apply search filter first
    if (search) {
      filtered = filtered.filter(
        (item) =>
          item.name.toLowerCase().includes(search.toLowerCase()) ||
          item.category.toLowerCase().includes(search.toLowerCase()) ||
          (item.supplier && item.supplier.toLowerCase().includes(search.toLowerCase()))
      );
    }
    
    // Then apply status filter
    switch (filter) {
      case "low":
        filtered = filtered.filter(item => item.quantity <= item.minQuantity);
        break;
      case "expiring":
        const today = new Date();
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(today.getDate() + 30);
        
        filtered = filtered.filter(item => {
          if (!item.expiryDate) return false;
          const expiryDate = new Date(item.expiryDate);
          return expiryDate <= thirtyDaysFromNow && expiryDate >= today;
        });
        break;
      case "expired":
        const now = new Date();
        filtered = filtered.filter(item => {
          if (!item.expiryDate) return false;
          const expiryDate = new Date(item.expiryDate);
          return expiryDate < now;
        });
        break;
      case "all":
      default:
        // No filtering needed
        break;
    }
    
    setFilteredItems(filtered);
    setActiveFilter(filter);
  };

  const handleAddItem = () => {
    if (!selectedFarm) {
      Alert.alert("Error", "Please select a farm first");
      return;
    }
    
    router.push("/inventory/edit");
  };

  const handleEditItem = (item: InventoryItem) => {
    router.push({
      pathname: "/inventory/edit",
      params: {
        id: item.id,
        name: item.name,
        category: item.category,
        quantity: item.quantity.toString(),
        unit: item.unit,
        minQuantity: item.minQuantity.toString(),
        location: item.location,
        expiryDate: item.expiryDate || "",
        purchaseDate: item.purchaseDate || "",
        description: item.description || "",
        supplier: item.supplier || "",
        price: item.price ? item.price.toString() : "",
        imageUrl: item.imageUrl || "",
      }
    });
  };

  const handleDeleteItem = (item: InventoryItem) => {
    Alert.alert(
      "Confirm Delete",
      "Are you sure you want to delete this item?",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Delete",
          onPress: async () => {
            try {
              await deleteInventoryItem(item.id, selectedFarm?.id);
              Alert.alert("Success", "Item deleted successfully");
              // The real-time listener will update the inventory
            } catch (error) {
              console.error("Error deleting item:", error);
              Alert.alert("Error", "Failed to delete item");
            }
          },
          style: "destructive",
        },
      ]
    );
  };
  
  const handleItemPress = (item: InventoryItem) => {
    router.push({
      pathname: "/(app)/inventory/[id]",
      params: { id: item.id },
    });
  };

  const getFarmName = (farmId: string) => {
    const farm = farms.find(f => f.id === farmId);
    return farm ? farm.name : "Unknown Farm";
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t("inventory.title"),
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: "#fff",
          headerRight: () => (
            <View style={styles.headerActions}>
              {/* <TouchableOpacity 
                onPress={() => setViewMode(viewMode === "list" ? "grid" : "list")}
                style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)", marginRight: 8 }]}
              >
                {viewMode === "list" ? <Grid size={20} color="#fff" /> : <List size={20} color="#fff" />}
              </TouchableOpacity> */}
              <TouchableOpacity 
                onPress={handleAddItem} 
                style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)" }]}
                disabled={!selectedFarm}
              >
                <Plus size={20} color="#fff" />
              </TouchableOpacity>
            </View>
          ),
        }}
      />

      {/* Simple Search Section */}
      <View style={[styles.searchContainer, { backgroundColor: cardColor, borderColor }]}>
        <Search size={20} color={secondaryTextColor} />
        <TextInput
          style={[
            styles.searchInput, 
            { 
              color: textColor, 
              textAlign: isRTL ? 'right' : 'left'
            }
          ]}
          placeholder={t('inventory.searchPlaceholder')}
          placeholderTextColor={secondaryTextColor}
          value={searchQuery}
          onChangeText={handleSearch}
        />
      </View>

      {/* Filter Section */}
      <View style={[styles.filterContainer, isRTL && styles.rtlFilterContainer]}>
        <View style={styles.filtersHeader}>
          {/* <Text style={[styles.filtersLabel, { color: secondaryTextColor }]}>
            <Filter size={14} color={secondaryTextColor} /> Filter by Status
          </Text>
          <Text style={[styles.itemCount, { color: primaryColor }]}>
            {filteredItems.length} items
          </Text> */}
        </View>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false} 
          contentContainerStyle={styles.filterScroll}
        >
          <FilterButton
            label={t('inventory.showAll')}
            active={activeFilter === "all"}
            onPress={() => applyFilters(inventoryItems, "all", searchQuery)}
            isDarkMode={isDarkMode}
          />
          <FilterButton
            label={t('inventory.showLowStock')}
            active={activeFilter === "low"}
            onPress={() => applyFilters(inventoryItems, "low", searchQuery)}
            isDarkMode={isDarkMode}
          />
          <FilterButton
            label={t('inventory.showExpiringSoon')}
            active={activeFilter === "expiring"}
            onPress={() => applyFilters(inventoryItems, "expiring", searchQuery)}
            isDarkMode={isDarkMode}
          />
          <FilterButton
            label={t('inventory.showExpired')}
            active={activeFilter === "expired"}
            onPress={() => applyFilters(inventoryItems, "expired", searchQuery)}
            isDarkMode={isDarkMode}
          />
        </ScrollView>
      </View>

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={primaryColor} />
          <Text style={[styles.loadingText, { color: textColor }]}>Loading your inventory...</Text>
        </View>
      ) : (
        <Animated.FlatList
          ref={scrollRef}
          data={filteredItems}
          renderItem={({ item }) => (
            <InventoryItemComponent
              item={item}
              onPress={() => handleItemPress(item)}
              onEditPress={() => handleEditItem(item)}
              onDeletePress={() => handleDeleteItem(item)}
              showEditOptions={true}
              isDarkMode={isDarkMode}
              role="owner"
              farmName={getFarmName(item.location)}
            />
          )}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { y: scrollY } } }],
            { useNativeDriver: false }
          )}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <LinearGradient
                colors={isDarkMode ? ["#2a2a2a", "#1e1e1e"] : ["#f8f9fa", "#ffffff"]}
                style={styles.emptyGradient}
              >
                <Package size={80} color={secondaryTextColor} />
                <Text style={[styles.emptyTitle, { color: textColor }]}>
                  {loading ? "Loading..." : selectedFarm ? 
                    (searchQuery || activeFilter !== "all" ? 
                      "No items match your search" : 
                      "Your inventory is empty") : 
                    "Select a farm to view inventory"}
                </Text>
                <Text style={[styles.emptySubtitle, { color: secondaryTextColor }]}>
                  {!loading && selectedFarm && !searchQuery && activeFilter === "all" ? 
                    "Start by adding your first inventory item" :
                    searchQuery || activeFilter !== "all" ?
                    "Try adjusting your search or filters" :
                    "Choose a farm from the selector above"
                  }
                </Text>
                {!loading && selectedFarm && !searchQuery && activeFilter === "all" && (
                  <TouchableOpacity 
                    style={[styles.emptyButton, { backgroundColor: primaryColor }]}
                    onPress={handleAddItem}
                  >
                    <Plus size={20} color="#fff" />
                    <Text style={styles.emptyButtonText}>Add First Item</Text>
                  </TouchableOpacity>
                )}
              </LinearGradient>
            </View>
          }
        />
      )}

      {/* Floating Action Button */}
      <TouchableOpacity 
        style={[styles.fab, { backgroundColor: primaryColor }]}
        onPress={handleAddItem}
        disabled={!selectedFarm}
      >
        <Plus size={24} color="#fff" />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerActions: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 16,
  },
  headerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    margin: 16,
    marginBottom: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  filterContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 8,
  },
  rtlFilterContainer: {
    flexDirection: 'row-reverse',
  },
  filtersHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  filtersLabel: {
    fontSize: 14,
    fontWeight: "600",
    letterSpacing: 0.5,
  },
  itemCount: {
    fontSize: 14,
    fontWeight: "600",
  },
  filterScroll: {
    paddingVertical: 8,
    gap: 12,
  },
  listContent: {
    padding: 20,
    paddingBottom: 100, // Add padding for FAB
    paddingTop: 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: "500",
  },
  emptyContainer: {
    flex: 1,
    padding: 20,
    alignItems: "center",
    justifyContent: "center",
    minHeight: 400,
  },
  emptyGradient: {
    padding: 32,
    borderRadius: 20,
    alignItems: "center",
    width: "100%",
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "700",
    marginTop: 16,
    marginBottom: 8,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: 16,
    marginBottom: 24,
    textAlign: "center",
    lineHeight: 22,
  },
  emptyButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 8,
  },
  emptyButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  fab: {
    position: "absolute",
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 1000,
  },
});
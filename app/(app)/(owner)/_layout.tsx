import React from "react";
import { Tabs } from "expo-router";
import { useAuth } from "@/context/auth-context";
import { Redirect } from "expo-router";
import { Home, Package, User, Truck, Users, Bot } from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";

export default function OwnerLayout() {
  const { user, isLoading } = useAuth();
  const { theme, colors } = useTheme();
  const { t } = useLanguage();
  const isDarkMode = theme === "dark";

  // Show loading while auth is being determined
  if (isLoading) {
    return null;
  }

  // If no user, let the auth context handle the redirect
  if (!user) {
    return null;
  }

  // If user is not an owner, redirect to the appropriate dashboard
  if (user.role !== "owner") {
    return <Redirect href={`/(app)/(${user.role})`} />;
  }

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: "#757575",
        tabBarStyle: {
          borderTopWidth: 1,
          borderTopColor: isDarkMode ? "#333333" : "#e0e0e0",
          height: 60,
          paddingBottom: 8,
          backgroundColor: isDarkMode ? "#121212" : "#ffffff",
        },
        tabBarLabelStyle: {
          fontSize: 12,
        },
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: "#fff",
        headerTitleStyle: {
          fontWeight: "bold",
        },
        lazy: true, // Only render screens when they are focused
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: t("dashboard.title"),
          tabBarIcon: ({ color }) => <Home size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="inventory"
        options={{
          title: t("inventory.title"),
          tabBarIcon: ({ color }) => <Package size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="machinery"
        options={{
          title: t("machinery.title"),
          tabBarIcon: ({ color }) => <Truck size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="ai-assistant"
        options={{
          title: t("chat.aiAssistant"),
          tabBarIcon: ({ color }) => <Bot size={24} color={color} />,
          href: "/(app)/ai-assistant",
        }}
      />
      <Tabs.Screen
        name="users"
        options={{
          title: t("users.title"),
          tabBarIcon: ({ color }) => <Users size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: t("profile.title"),
          tabBarIcon: ({ color }) => <User size={24} color={color} />,
        }}
      />
      
      {/* Hide all non-tab screens from the tab bar */}
      <Tabs.Screen
        name="requests"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="reports"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="notes"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="farms"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="farms/create"
        options={{
          href: null, // This prevents the screen from being accessible via direct URL
        }}
      />
      <Tabs.Screen
        name="farms/edit"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="farms/[id]"
        options={{
          href: null,
        }}
      />


      <Tabs.Screen
        name="report-details"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="scheduled-reports"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="inventory-summary"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="low-stock-alerts"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="monthly-activity"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="usage-trends"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="admins"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="approvals"
        options={{
          href: null,
        }}
      />
    </Tabs>
  );
}

import { useState, useEffect, useCallback } from "react";
import { StyleSheet, Text, View, FlatList, TouchableOpacity, TextInput, RefreshControl, Alert, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { getUsers, User, deleteUser, getUsersByFarm, refreshUsers } from "@/services/user-service";
import { Search, Plus, UserPlus, Users as UsersIcon, Filter, ArrowLeft } from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { UserItem } from "@/components/UserItem";
import { useAuth } from "@/context/auth-context";
import { useFarm } from "@/context/farm-context";
import { useFocusEffect } from "@react-navigation/native";
import { router, Stack } from "expo-router";

export default function OwnerUsersScreen() {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const { user: currentUser } = useAuth();
  const { selectedFarm } = useFarm();
  const isDarkMode = theme === "dark";
  
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filterRole, setFilterRole] = useState<string>('all');

  const backgroundColor = isDarkMode ? "#0f0f0f" : "#f8fafc";
  const cardColor = isDarkMode ? "#1a1a1a" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#1e293b";
  const secondaryTextColor = isDarkMode ? "#94a3b8" : "#64748b";
  const borderColor = isDarkMode ? "#2d2d2d" : "#e2e8f0";
  const primaryColor = "#2E7D32";

  const loadUsers = useCallback(async () => {
    try {
      setLoading(true);
      let fetchedUsers: User[] = [];
      
      if (selectedFarm) {
        // Get users assigned to the selected farm
        fetchedUsers = await getUsersByFarm(selectedFarm.id);
      } else {
        // Get all users if no farm is selected
        fetchedUsers = await getUsers();
      }
      
      console.log("Loaded users:", fetchedUsers.length);
      setUsers(fetchedUsers);
      
      // Apply filters
      applyFilters(fetchedUsers, searchQuery, filterRole);
    } catch (error) {
      console.error("Error loading users:", error);
      Alert.alert(t('common.error'), t('users.loadError'));
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [selectedFarm, searchQuery, filterRole, t]);

  // Apply search and role filters
  const applyFilters = (usersList: User[], query: string, role: string) => {
    let filtered = usersList;

    // Apply search filter
    if (query) {
      filtered = filtered.filter(
        (user) =>
          (user.name || user.displayName || "").toLowerCase().includes(query.toLowerCase()) ||
          user.email.toLowerCase().includes(query.toLowerCase())
      );
    }

    // Apply role filter
    if (role !== 'all') {
      filtered = filtered.filter(user => user.role === role);
    }

    setFilteredUsers(filtered);
  };

  // Load users when farm changes
  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  // Refresh when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log("Users screen focused, refreshing data...");
      loadUsers();
      return () => {};
    }, [loadUsers])
  );

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    applyFilters(users, text, filterRole);
  };

  const handleRoleFilter = (role: string) => {
    setFilterRole(role);
    applyFilters(users, searchQuery, role);
  };

  const onRefresh = async () => {
    console.log("Manual refresh triggered - forcing fresh data");
    setRefreshing(true);
    try {
      let fetchedUsers: User[] = [];

      if (selectedFarm) {
        // Force refresh users assigned to the selected farm
        fetchedUsers = await refreshUsers(selectedFarm.id);
      } else {
        // Force refresh all users
        fetchedUsers = await refreshUsers();
      }

      console.log("Force refreshed users:", fetchedUsers.length);
      setUsers(fetchedUsers);

      // Apply filters
      applyFilters(fetchedUsers, searchQuery, filterRole);
    } catch (error) {
      console.error("Error force refreshing users:", error);
      Alert.alert(t('common.error'), t('users.loadError'));
    } finally {
      setRefreshing(false);
    }
  };

  const handleAddUser = () => {
    router.push("/users/add");
  };

  const handleEditUser = (user: User) => {
    router.push(`/users/edit?id=${user.uid}`);
  };

  const handleDeleteUser = async (user: User) => {
    Alert.alert(
      t('users.deleteUser'),
      t('users.deleteUserConfirmation', { userName: user.name || user.displayName }),
      [
        { text: t('common.cancel'), style: "cancel" },
        { 
          text: t('common.delete'), 
          onPress: async () => {
            try {
              await deleteUser(user.uid);
              Alert.alert(t('common.success'), t('users.userDeleted'));
              loadUsers(); // Refresh the list
            } catch (error) {
              console.error("Error deleting user:", error);
              Alert.alert(t('common.error'), t('users.deleteError'));
            }
          }, 
          style: "destructive" 
        }
      ]
    );
  };

  const renderUserItem = ({ item }: { item: User }) => {
    return (
      <UserItem
        user={item}
        onEditPress={() => handleEditUser(item)}
        onDeletePress={() => handleDeleteUser(item)}
        showEditOptions={true}
        isDarkMode={isDarkMode}
        role="owner"
        currentUserId={currentUser?.uid}
        backgroundColor={cardColor}
        textColor={textColor}
        secondaryTextColor={secondaryTextColor}
        borderColor={borderColor}
      />
    );
  };

  const roleFilters = [
    { key: 'all', label: t('common.all') },
    { key: 'admin', label: t('users.admin') },
    { key: 'caretaker', label: t('users.caretaker') },
  ];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t('users.title'),
          headerStyle: {
            backgroundColor: isDarkMode ? "#1e1e1e" : "#2E7D32",
          },
          headerTintColor: "#fff",
          headerLeft: () => (
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)", marginLeft: 10 }]}
              onPress={() => router.push("/(app)/(owner)")}
            >
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      {/* Header */}
      <View style={styles.header}>
        {/* <View style={styles.headerTop}>
          <View>
            <Text style={[styles.title, { color: textColor }]}>
              {t('users.title')}
            </Text>
            <Text style={[styles.subtitle, { color: secondaryTextColor }]}>
              {selectedFarm ? t('users.farmUsers', { farmName: selectedFarm.name }) : t('users.allUsers')}
            </Text>
          </View>
          <TouchableOpacity 
            style={[styles.addButton, { backgroundColor: primaryColor }]} 
            onPress={handleAddUser}
          >
            <Plus size={24} color="#fff" />
          </TouchableOpacity>
        </View> */}

        {/* Search Bar */}
        <View style={[styles.searchContainer, { backgroundColor: cardColor, borderColor }]}>
          <Search size={20} color={secondaryTextColor} />
          <TextInput
            style={[styles.searchInput, { color: textColor }]}
            placeholder={t('users.searchUsers')}
            placeholderTextColor={secondaryTextColor}
            value={searchQuery}
            onChangeText={handleSearch}
          />
        </View>

        {/* Role Filters */}
        <View style={styles.filtersContainer}>
          {roleFilters.map((filter) => (
            <TouchableOpacity
              key={filter.key}
              style={[
                styles.filterButton,
                { 
                  backgroundColor: filterRole === filter.key ? primaryColor : cardColor,
                  borderColor: filterRole === filter.key ? primaryColor : borderColor
                }
              ]}
              onPress={() => handleRoleFilter(filter.key)}
            >
              <Text style={[
                styles.filterButtonText,
                { color: filterRole === filter.key ? '#fff' : textColor }
              ]}>
                {filter.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Content */}
      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={primaryColor} />
          <Text style={[styles.loadingText, { color: secondaryTextColor }]}>
            {t('common.loading')}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredUsers}
          renderItem={renderUserItem}
          keyExtractor={(item) => item.uid}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl 
              refreshing={refreshing} 
              onRefresh={onRefresh}
              tintColor={primaryColor}
              colors={[primaryColor]}
            />
          }
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <View style={[styles.emptyIcon, { backgroundColor: `${primaryColor}15` }]}>
                <UsersIcon size={48} color={primaryColor} />
              </View>
              <Text style={[styles.emptyTitle, { color: textColor }]}>
                {searchQuery || filterRole !== 'all' 
                  ? t('users.noUsersFound') 
                  : (selectedFarm ? t('users.noUsersFoundForFarm') : t('users.noUsers'))
                }
              </Text>
              <Text style={[styles.emptySubtitle, { color: secondaryTextColor }]}>
                {searchQuery || filterRole !== 'all'
                  ? t('users.tryDifferentSearch')
                  : t('users.addFirstUser')
                }
              </Text>
              {(!searchQuery && filterRole === 'all') && (
                <TouchableOpacity 
                  style={[styles.emptyButton, { backgroundColor: primaryColor }]}
                  onPress={handleAddUser}
                >
                  <UserPlus size={20} color="#fff" />
                  <Text style={styles.emptyButtonText}>{t('users.addUser')}</Text>
                </TouchableOpacity>
              )}
            </View>
          }
        />
      )}
      <TouchableOpacity 
             style={[styles.fab, { backgroundColor: primaryColor }]}
            onPress={handleAddUser}
          >
            <Plus size={24} color="#fff" />
          </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  headerTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: "800",
    letterSpacing: -0.5,
  },
  fab: {
    position: "absolute",
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 1000,
  },
  subtitle: {
    fontSize: 16,
    marginTop: 4,
    fontWeight: "500",
  },
  addButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    marginBottom: 16,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    marginLeft: 12,
    fontWeight: "500",
  },
  filtersContainer: {
    flexDirection: "row",
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: "600",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: "500",
  },
  listContent: {
    padding: 20,
    paddingTop: 0,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyIcon: {
    width: 96,
    height: 96,
    borderRadius: 48,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "700",
    textAlign: "center",
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 32,
    lineHeight: 24,
  },
  emptyButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 16,
    gap: 8,
  },
  emptyButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
});
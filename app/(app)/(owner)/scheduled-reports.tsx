import { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Switch, TextInput, Alert } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack } from "expo-router";
import { Clock, Trash2, Plus, Calendar, Mail, BarChart3, TrendingUp, AlertTriangle } from "lucide-react-native";
import { getScheduledReports, scheduleReport, deleteScheduledReport } from "@/services/reports-service";
import { useTheme } from "@/context/theme-context";

interface ScheduledReport {
  id: string;
  reportType: string;
  reportName: string;
  frequency: string;
  emails: string[];
  format: "pdf" | "excel";
  active: boolean;
  nextRun: string;
}

export default function ScheduledReportsScreen() {
  const [reports, setReports] = useState<ScheduledReport[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newReport, setNewReport] = useState({
    reportType: "inventory-summary",
    frequency: "weekly",
    email: "",
    format: "pdf" as "pdf" | "excel",
  });
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";

  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";
  const inputBgColor = isDarkMode ? "#2a2a2a" : "#f5f5f5";

  useEffect(() => {
    loadScheduledReports();
  }, []);

  const loadScheduledReports = async () => {
    try {
      const data = await getScheduledReports();
      setReports(data);
    } catch (error) {
      console.error("Error loading scheduled reports:", error);
    }
  };

  const handleToggleActive = async (id: string, currentValue: boolean) => {
    try {
      // In a real app, update the scheduled report status
      const updatedReports = reports.map(report => 
        report.id === id ? { ...report, active: !currentValue } : report
      );
      setReports(updatedReports);
    } catch (error) {
      console.error("Error toggling report status:", error);
    }
  };

  const handleDeleteReport = async (id: string) => {
    try {
      await deleteScheduledReport(id);
      setReports(reports.filter(report => report.id !== id));
    } catch (error) {
      console.error("Error deleting scheduled report:", error);
    }
  };

  const handleAddReport = async () => {
    if (!newReport.email) {
      Alert.alert("Error", "Please enter an email address");
      return;
    }

    try {
      const result = await scheduleReport({
        reportType: newReport.reportType,
        frequency: newReport.frequency,
        emails: [newReport.email],
        format: newReport.format,
      });

      if (result.success) {
        await loadScheduledReports();
        setShowAddForm(false);
        setNewReport({
          reportType: "inventory-summary",
          frequency: "weekly",
          email: "",
          format: "pdf",
        });
      }
    } catch (error) {
      console.error("Error scheduling report:", error);
      Alert.alert("Error", "Failed to schedule report. Please try again.");
    }
  };

  const getReportTypeIcon = (type: string) => {
    switch (type) {
      case "inventory-summary":
        return <BarChart3 size={20} color="#2E7D32" />;
      case "usage-trends":
        return <TrendingUp size={20} color="#1976D2" />;
      case "low-stock-alerts":
        return <AlertTriangle size={20} color="#D32F2F" />;
      case "monthly-activity":
        return <Calendar size={20} color="#7B1FA2" />;
      default:
        return <BarChart3 size={20} color="#2E7D32" />;
    }
  };

  const getReportTypeName = (type: string) => {
    switch (type) {
      case "inventory-summary":
        return "Inventory Summary";
      case "usage-trends":
        return "Usage Trends";
      case "low-stock-alerts":
        return "Low Stock & Expiry Alerts";
      case "monthly-activity":
        return "Monthly Activity";
      default:
        return "Unknown Report";
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen 
        options={{
          title: "Scheduled Reports",
          headerStyle: {
            backgroundColor: isDarkMode ? "#1e1e1e" : "#2E7D32",
          },
          headerTintColor: "#fff",
          headerRight: () => (
            <TouchableOpacity 
              style={styles.headerButton} 
              onPress={() => setShowAddForm(!showAddForm)}
            >
              <Plus size={24} color="#fff" />
            </TouchableOpacity>
          )
        }} 
      />
      
      <ScrollView style={styles.scrollView}>
        {showAddForm && (
          <View style={[styles.addFormContainer, { backgroundColor: cardColor }]}>
            <Text style={[styles.formTitle, { color: textColor }]}>Schedule New Report</Text>
            
            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: secondaryTextColor }]}>Report Type</Text>
              <View style={styles.selectContainer}>
                <TouchableOpacity 
                  style={[
                    styles.selectOption, 
                    { backgroundColor: isDarkMode 
                      ? (newReport.reportType === "inventory-summary" ? "#2E7D32" : "#333333") 
                      : (newReport.reportType === "inventory-summary" ? "#2E7D32" : "#f0f0f0") 
                    }
                  ]}
                  onPress={() => setNewReport({...newReport, reportType: "inventory-summary"})}
                >
                  <Text style={[
                    styles.selectOptionText,
                    { color: newReport.reportType === "inventory-summary" 
                      ? "#ffffff" 
                      : (isDarkMode ? "#aaaaaa" : "#555555") 
                    }
                  ]}>Inventory Summary</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[
                    styles.selectOption, 
                    { backgroundColor: isDarkMode 
                      ? (newReport.reportType === "usage-trends" ? "#2E7D32" : "#333333") 
                      : (newReport.reportType === "usage-trends" ? "#2E7D32" : "#f0f0f0") 
                    }
                  ]}
                  onPress={() => setNewReport({...newReport, reportType: "usage-trends"})}
                >
                  <Text style={[
                    styles.selectOptionText,
                    { color: newReport.reportType === "usage-trends" 
                      ? "#ffffff" 
                      : (isDarkMode ? "#aaaaaa" : "#555555") 
                    }
                  ]}>Usage Trends</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[
                    styles.selectOption, 
                    { backgroundColor: isDarkMode 
                      ? (newReport.reportType === "low-stock-alerts" ? "#2E7D32" : "#333333") 
                      : (newReport.reportType === "low-stock-alerts" ? "#2E7D32" : "#f0f0f0") 
                    }
                  ]}
                  onPress={() => setNewReport({...newReport, reportType: "low-stock-alerts"})}
                >
                  <Text style={[
                    styles.selectOptionText,
                    { color: newReport.reportType === "low-stock-alerts" 
                      ? "#ffffff" 
                      : (isDarkMode ? "#aaaaaa" : "#555555") 
                    }
                  ]}>Low Stock & Expiry</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[
                    styles.selectOption, 
                    { backgroundColor: isDarkMode 
                      ? (newReport.reportType === "monthly-activity" ? "#2E7D32" : "#333333") 
                      : (newReport.reportType === "monthly-activity" ? "#2E7D32" : "#f0f0f0") 
                    }
                  ]}
                  onPress={() => setNewReport({...newReport, reportType: "monthly-activity"})}
                >
                  <Text style={[
                    styles.selectOptionText,
                    { color: newReport.reportType === "monthly-activity" 
                      ? "#ffffff" 
                      : (isDarkMode ? "#aaaaaa" : "#555555") 
                    }
                  ]}>Monthly Activity</Text>
                </TouchableOpacity>
              </View>
            </View>
            
            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: secondaryTextColor }]}>Frequency</Text>
              <View style={styles.frequencyContainer}>
                <TouchableOpacity 
                  style={[
                    styles.frequencyOption, 
                    { backgroundColor: isDarkMode 
                      ? (newReport.frequency === "daily" ? "#2E7D32" : "#333333") 
                      : (newReport.frequency === "daily" ? "#2E7D32" : "#f0f0f0") 
                    }
                  ]}
                  onPress={() => setNewReport({...newReport, frequency: "daily"})}
                >
                  <Text style={[
                    styles.frequencyOptionText,
                    { color: newReport.frequency === "daily" 
                      ? "#ffffff" 
                      : (isDarkMode ? "#aaaaaa" : "#555555") 
                    }
                  ]}>Daily</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[
                    styles.frequencyOption, 
                    { backgroundColor: isDarkMode 
                      ? (newReport.frequency === "weekly" ? "#2E7D32" : "#333333") 
                      : (newReport.frequency === "weekly" ? "#2E7D32" : "#f0f0f0") 
                    }
                  ]}
                  onPress={() => setNewReport({...newReport, frequency: "weekly"})}
                >
                  <Text style={[
                    styles.frequencyOptionText,
                    { color: newReport.frequency === "weekly" 
                      ? "#ffffff" 
                      : (isDarkMode ? "#aaaaaa" : "#555555") 
                    }
                  ]}>Weekly</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[
                    styles.frequencyOption, 
                    { backgroundColor: isDarkMode 
                      ? (newReport.frequency === "monthly" ? "#2E7D32" : "#333333") 
                      : (newReport.frequency === "monthly" ? "#2E7D32" : "#f0f0f0") 
                    }
                  ]}
                  onPress={() => setNewReport({...newReport, frequency: "monthly"})}
                >
                  <Text style={[
                    styles.frequencyOptionText,
                    { color: newReport.frequency === "monthly" 
                      ? "#ffffff" 
                      : (isDarkMode ? "#aaaaaa" : "#555555") 
                    }
                  ]}>Monthly</Text>
                </TouchableOpacity>
              </View>
            </View>
            
            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: secondaryTextColor }]}>Format</Text>
              <View style={styles.formatContainer}>
                <TouchableOpacity 
                  style={[
                    styles.formatOption, 
                    { backgroundColor: isDarkMode 
                      ? (newReport.format === "pdf" ? "#2E7D32" : "#333333") 
                      : (newReport.format === "pdf" ? "#2E7D32" : "#f0f0f0") 
                    }
                  ]}
                  onPress={() => setNewReport({...newReport, format: "pdf"})}
                >
                  <Text style={[
                    styles.formatOptionText,
                    { color: newReport.format === "pdf" 
                      ? "#ffffff" 
                      : (isDarkMode ? "#aaaaaa" : "#555555") 
                    }
                  ]}>PDF</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[
                    styles.formatOption, 
                    { backgroundColor: isDarkMode 
                      ? (newReport.format === "excel" ? "#2E7D32" : "#333333") 
                      : (newReport.format === "excel" ? "#2E7D32" : "#f0f0f0") 
                    }
                  ]}
                  onPress={() => setNewReport({...newReport, format: "excel"})}
                >
                  <Text style={[
                    styles.formatOptionText,
                    { color: newReport.format === "excel" 
                      ? "#ffffff" 
                      : (isDarkMode ? "#aaaaaa" : "#555555") 
                    }
                  ]}>Excel</Text>
                </TouchableOpacity>
              </View>
            </View>
            
            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: secondaryTextColor }]}>Email</Text>
              <TextInput
                style={[styles.input, { backgroundColor: inputBgColor, color: textColor }]}
                value={newReport.email}
                onChangeText={(text) => setNewReport({...newReport, email: text})}
                placeholder="Enter email address"
                placeholderTextColor={secondaryTextColor}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>
            
            <View style={styles.formActions}>
              <TouchableOpacity 
                style={styles.cancelButton}
                onPress={() => setShowAddForm(false)}
              >
                <Text style={[styles.cancelButtonText, { color: secondaryTextColor }]}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.scheduleButton}
                onPress={handleAddReport}
              >
                <Text style={styles.scheduleButtonText}>Schedule Report</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
        
        <View style={styles.reportsContainer}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>Your Scheduled Reports</Text>
          
          {reports.length === 0 ? (
            <View style={[styles.emptyState, { backgroundColor: cardColor }]}>
              <Clock size={48} color={isDarkMode ? "#555555" : "#cccccc"} />
              <Text style={[styles.emptyStateText, { color: textColor }]}>No scheduled reports</Text>
              <Text style={[styles.emptyStateSubtext, { color: secondaryTextColor }]}>
                Set up automatic reports to be delivered to your email
              </Text>
              <TouchableOpacity 
                style={styles.emptyStateButton}
                onPress={() => setShowAddForm(true)}
              >
                <Text style={styles.emptyStateButtonText}>Schedule Your First Report</Text>
              </TouchableOpacity>
            </View>
          ) : (
            reports.map((report) => (
              <View key={report.id} style={[styles.reportCard, { backgroundColor: cardColor }]}>
                <View style={styles.reportHeader}>
                  <View style={styles.reportTypeContainer}>
                    {getReportTypeIcon(report.reportType)}
                    <Text style={[styles.reportName, { color: textColor }]}>{getReportTypeName(report.reportType)}</Text>
                  </View>
                  <Switch
                    value={report.active}
                    onValueChange={() => handleToggleActive(report.id, report.active)}
                    trackColor={{ false: isDarkMode ? "#555555" : "#cccccc", true: "#a5d6a7" }}
                    thumbColor={report.active ? "#2E7D32" : isDarkMode ? "#aaaaaa" : "#f4f3f4"}
                  />
                </View>
                
                <View style={styles.reportDetails}>
                  <View style={styles.reportDetailItem}>
                    <Calendar size={16} color={secondaryTextColor} />
                    <Text style={[styles.reportDetailText, { color: secondaryTextColor }]}>
                      {report.frequency.charAt(0).toUpperCase() + report.frequency.slice(1)}
                    </Text>
                  </View>
                  
                  <View style={styles.reportDetailItem}>
                    <Mail size={16} color={secondaryTextColor} />
                    <Text style={[styles.reportDetailText, { color: secondaryTextColor }]}>
                      {report.emails.join(", ")}
                    </Text>
                  </View>
                  
                  <View style={styles.reportDetailItem}>
                    <Clock size={16} color={secondaryTextColor} />
                    <Text style={[styles.reportDetailText, { color: secondaryTextColor }]}>
                      Next: {report.nextRun}
                    </Text>
                  </View>
                </View>
                
                <View style={[styles.reportActions, { borderTopColor: borderColor }]}>
                  <TouchableOpacity 
                    style={styles.deleteButton}
                    onPress={() => handleDeleteReport(report.id)}
                  >
                    <Trash2 size={16} color="#D32F2F" />
                    <Text style={styles.deleteButtonText}>Delete</Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  headerButton: {
    marginRight: 16,
  },
  addFormContainer: {
    margin: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 8,
  },
  selectContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  selectOption: {
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  selectOptionText: {
    fontSize: 14,
  },
  frequencyContainer: {
    flexDirection: "row",
  },
  frequencyOption: {
    flex: 1,
    borderRadius: 8,
    paddingVertical: 10,
    alignItems: "center",
    marginRight: 8,
  },
  frequencyOptionText: {
    fontSize: 14,
  },
  formatContainer: {
    flexDirection: "row",
  },
  formatOption: {
    flex: 1,
    borderRadius: 8,
    paddingVertical: 10,
    alignItems: "center",
    marginRight: 8,
  },
  formatOptionText: {
    fontSize: 14,
  },
  input: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
  },
  formActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: 16,
  },
  cancelButton: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 14,
  },
  scheduleButton: {
    backgroundColor: "#2E7D32",
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  scheduleButtonText: {
    fontSize: 14,
    color: "#fff",
    fontWeight: "500",
  },
  reportsContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  emptyState: {
    borderRadius: 12,
    padding: 24,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: "600",
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    textAlign: "center",
    marginBottom: 24,
  },
  emptyStateButton: {
    backgroundColor: "#2E7D32",
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  emptyStateButtonText: {
    fontSize: 14,
    color: "#fff",
    fontWeight: "500",
  },
  reportCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  reportHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  reportTypeContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  reportName: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  reportDetails: {
    borderTopWidth: 1,
    borderTopColor: "#f0f0f0",
    paddingTop: 16,
  },
  reportDetailItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  reportDetailText: {
    fontSize: 14,
    marginLeft: 8,
  },
  reportActions: {
    borderTopWidth: 1,
    paddingTop: 16,
    marginTop: 8,
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  deleteButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  deleteButtonText: {
    fontSize: 14,
    color: "#D32F2F",
    marginLeft: 4,
  },
});
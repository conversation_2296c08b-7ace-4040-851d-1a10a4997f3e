import { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, RefreshControl, Dimensions } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { BarChart3, TrendingUp, AlertTriangle, Users, FileText, Truck, Download, Eye, Activity, ArrowLeft } from "lucide-react-native";
import { router, Stack } from "expo-router";
import { useFarm } from "@/context/farm-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { getDashboardAnalytics, getInventoryReport, getUsersReport, getRequestsReport, getActivitiesReport } from "@/services/reports-service";
import { getMachineryReport } from "@/services/machinery-service";
import { LinearGradient } from "expo-linear-gradient";

const { width } = Dimensions.get('window');

export default function OwnerReportsScreen() {
  const [refreshing, setRefreshing] = useState(false);
  const [analytics, setAnalytics] = useState<any>({});
  const [reportStats, setReportStats] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const { selectedFarm } = useFarm();
  const { colors } = useTheme();
  const { t } = useLanguage();

  useEffect(() => {
    loadReportData();
  }, [selectedFarm]);

  const loadReportData = async () => {
    if (!selectedFarm) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const [analyticsData, inventoryData, usersData, requestsData, activityData, machineryData] = await Promise.all([
        getDashboardAnalytics(selectedFarm.id),
        getInventoryReport(selectedFarm.id),
        getUsersReport(selectedFarm.id),
        getRequestsReport(selectedFarm.id),
        getActivitiesReport(selectedFarm.id),
        getMachineryReport(selectedFarm.id)
      ]);

      setAnalytics(analyticsData);
      setReportStats({
        inventory: inventoryData,
        users: usersData,
        requests: requestsData,
        activity: activityData,
        machinery: machineryData
      });
    } catch (error) {
      console.error("Error loading report data:", error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadReportData();
    setRefreshing(false);
  };

  const handleViewReport = (reportType: string) => {
    if (!selectedFarm) {
      alert("Please select a farm first");
      return;
    }
    router.push(`/(app)/report-details?type=${reportType}&farmId=${selectedFarm.id}`);
  };

  const overviewCards = [
    {
      title: t("dashboard.totalInventory"),
      value: analytics.overview?.totalItems || 0,
      icon: BarChart3,
      color: colors.success,
    },
    {
      title: t("dashboard.activeMachinery"),
      value: analytics.overview?.totalMachinery || 0,
      icon: Truck,
      color: colors.info,
    },
    {
      title: t("dashboard.totalUsers"),
      value: analytics.overview?.totalUsers || 0,
      icon: Users,
      color: colors.primary,
    },
    {
      title: t("dashboard.pendingRequests"),
      value: analytics.overview?.totalRequests || 0,
      icon: FileText,
      color: colors.warning,
    }
  ];

  const reports = [
    {
      id: "inventory-summary",
      title: "Inventory Analytics",
      description: "Comprehensive inventory analysis and stock management",
      icon: BarChart3,
      color: colors.success,
      gradient: [`${colors.success}20`, `${colors.success}10`],
      stats: reportStats.inventory ? [
        `${reportStats.inventory.summary.totalItems} Total Items`,
        `${reportStats.inventory.summary.lowStockItems} Low Stock`,
        `$${reportStats.inventory.summary.totalValue.toFixed(2)} Total Value`
      ] : [],
      onPress: () => handleViewReport("inventory"),
    },
    {
      id: "machinery-overview",
      title: "Machinery Management",
      description: "Machinery performance and utilization overview",
      icon: Truck,
      color: colors.info,
      gradient: [`${colors.info}20`, `${colors.info}10`],
      stats: reportStats.machinery ? [
        `${reportStats.machinery.summary.totalMachinery} Total Units`,
        `${reportStats.machinery.summary.activeMachinery} Active`,
        `${reportStats.machinery.summary.utilizationRate}% Utilization`
      ] : [],
      onPress: () => handleViewReport("machinery"),
    },
    {
      id: "team-analytics",
      title: "Team Management",
      description: "User activity and team performance metrics",
      icon: Users,
      color: colors.primary,
      gradient: [`${colors.primary}20`, `${colors.primary}10`],
      stats: reportStats.users ? [
        `${reportStats.users.summary.totalUsers} Team Members`,
        `${reportStats.users.summary.activeUsers} Active`,
        `${reportStats.users.summary.admins} Admins`
      ] : [],
      onPress: () => handleViewReport("users"),
    },
    {
      id: "requests-flow",
      title: "Request Management",
      description: "Request workflows and approval tracking",
      icon: Activity,
      color: colors.warning,
      gradient: [`${colors.warning}20`, `${colors.warning}10`],
      stats: reportStats.requests ? [
        `${reportStats.requests.summary.totalRequests} Total Requests`,
        `${reportStats.requests.summary.pendingRequests} Pending`,
        `${reportStats.requests.summary.approvedRequests} Approved`
      ] : [],
      onPress: () => handleViewReport("requests"),
    },
    {
      id: "activity-summary",
      title: "Activity Overview",
      description: "Farm activities and operational insights",
      icon: TrendingUp,
      color: colors.textSecondary,
      gradient: [`${colors.textSecondary}20`, `${colors.textSecondary}10`],
      stats: reportStats.activity ? [
        `${reportStats.activity.summary.totalActivities} Total Activities`,
        `${reportStats.activity.summary.todayActivities} Today`,
        `${reportStats.activity.summary.weekActivities} This Week`
      ] : [],
      onPress: () => handleViewReport("activity"),
    },
  ];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t("reports.title"),
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: "#fff",
          headerLeft: () => (
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)", marginLeft: 10 }]}
              onPress={() => router.push("/(app)/(owner)")}
            >
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={[styles.title, { color: colors.text }]}>Reports & Analytics</Text>
            <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
              {selectedFarm ? `${selectedFarm.name} • Comprehensive Reports` : "Select a farm to view reports"}
            </Text>
          </View>
          <TouchableOpacity style={[styles.exportButton, { backgroundColor: colors.primary }]}>
            <Download size={20} color="#fff" />
          </TouchableOpacity>
        </View>

        {!selectedFarm && (
          <View style={[styles.noFarmContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <Text style={[styles.noFarmText, { color: colors.text }]}>
              Please select a farm to generate reports
            </Text>
          </View>
        )}

        {selectedFarm && (
          <>
            {/* Overview Cards */}
            <View style={styles.overviewContainer}>
              {overviewCards.map((card, index) => (
                <View key={index} style={[styles.overviewCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                  <View style={styles.overviewCardContent}>
                    <View style={[styles.overviewIconContainer, { backgroundColor: card.color + '20' }]}>
                      <card.icon size={24} color={card.color} />
                    </View>
                    <View style={styles.overviewTextContainer}>
                      <Text style={[styles.overviewValue, { color: colors.text }]}>{card.value}</Text>
                      <Text style={[styles.overviewTitle, { color: colors.textSecondary }]}>{card.title}</Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>

            {/* Key Insights */}
            {analytics.alerts && analytics.alerts.length > 0 && (
              <View style={styles.alertsSection}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>Key Alerts</Text>
                {analytics.alerts.slice(0, 2).map((alert: any, index: number) => (
                  <View key={index} style={[styles.alertCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                    <View style={styles.alertHeader}>
                      <View style={[
                        styles.alertTypeIndicator,
                        { backgroundColor: alert.type === 'critical' ? colors.error + '20' : colors.warning + '20' }
                      ]}>
                        <AlertTriangle 
                          size={16} 
                          color={alert.type === 'critical' ? colors.error : colors.warning} 
                        />
                      </View>
                      <Text style={[styles.alertTitle, { color: colors.text }]}>{alert.title}</Text>
                    </View>
                    <Text style={[styles.alertMessage, { color: colors.textSecondary }]}>
                      {alert.message}
                    </Text>
                  </View>
                ))}
              </View>
            )}

            {/* Reports Grid */}
            <View style={styles.reportsSection}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>Detailed Reports</Text>
              </View>

              {loading ? (
                <View style={[styles.loadingContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                  <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                    Loading reports...
                  </Text>
                </View>
              ) : (
                <View style={styles.reportsGrid}>
                  {reports.map((report) => (
                    <TouchableOpacity
                      key={report.id}
                      style={[styles.reportCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
                      onPress={report.onPress}
                      activeOpacity={0.7}
                    >
                      <LinearGradient
                        colors={report.gradient}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                        style={styles.reportCardGradient}
                      >
                        <View style={styles.reportCardHeader}>
                          <View style={[styles.reportIconContainer, { backgroundColor: report.color + '20' }]}>
                            <report.icon size={24} color={report.color} />
                          </View>
                          <TouchableOpacity style={styles.reportActionButton}>
                            <Eye size={16} color={colors.textSecondary} />
                          </TouchableOpacity>
                        </View>
                        
                        <Text style={[styles.reportTitle, { color: colors.text }]}>{report.title}</Text>
                        <Text style={[styles.reportDescription, { color: colors.textSecondary }]}>
                          {report.description}
                        </Text>
                        
                        {report.stats.length > 0 && (
                          <View style={styles.reportStats}>
                            {report.stats.map((stat, index) => (
                              <View key={index} style={styles.statItem}>
                                <View style={[styles.statDot, { backgroundColor: report.color }]} />
                                <Text style={[styles.statText, { color: colors.textSecondary }]}>{stat}</Text>
                              </View>
                            ))}
                          </View>
                        )}
                      </LinearGradient>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: "800",
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 16,
    marginTop: 4,
    fontWeight: "500",
  },
  exportButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  noFarmContainer: {
    margin: 16,
    padding: 24,
    borderRadius: 16,
    alignItems: "center",
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  noFarmText: {
    fontSize: 16,
    textAlign: "center",
    fontWeight: "500",
  },
  overviewContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    marginBottom: 24,
    gap: 12,
  },
  overviewCard: {
    width: (width - 52) / 2,
    padding: 16,
    borderRadius: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  overviewCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  overviewIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  overviewTextContainer: {
    flex: 1,
  },
  overviewValue: {
    fontSize: 20,
    fontWeight: "800",
    marginBottom: 2,
  },
  overviewTitle: {
    fontSize: 12,
    fontWeight: "500",
  },
  alertsSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "700",
    marginBottom: 16,
    letterSpacing: -0.3,
  },
  alertCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  alertHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  alertTypeIndicator: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  alertTitle: {
    fontSize: 16,
    fontWeight: "600",
    flex: 1,
  },
  alertMessage: {
    fontSize: 14,
    lineHeight: 20,
  },
  reportsSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  loadingContainer: {
    padding: 24,
    borderRadius: 16,
    alignItems: "center",
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  loadingText: {
    fontSize: 16,
    textAlign: "center",
    fontWeight: "500",
  },
  reportsGrid: {
    gap: 16,
  },
  reportCard: {
    borderRadius: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  reportCardGradient: {
    padding: 20,
  },
  reportCardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  reportIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
  },
  reportActionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  reportTitle: {
    fontSize: 18,
    fontWeight: "700",
    marginBottom: 8,
    letterSpacing: -0.3,
  },
  reportDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  reportStats: {
    gap: 8,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  statText: {
    fontSize: 13,
    fontWeight: "500",
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
});
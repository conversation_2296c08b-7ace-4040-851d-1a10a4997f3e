import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  Platform,
  KeyboardAvoidingView,
} from "react-native";
import { useLocalSearchParams, useRouter, Stack } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { useAuth } from "@/context/auth-context";
import { useFarm } from "@/context/farm-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { getNoteById, updateNote, Note } from "@/services/notes-service";
import { Image } from "expo-image";
import * as ImagePicker from "expo-image-picker";
import { uploadNoteImages } from "@/services/storage-service";
import { Audio } from "expo-av";
import { Camera, Image as ImageIcon, Mic, X, Play, Pause, Save, ArrowLeft, FileText, Edit3, Upload } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";

export default function EditNoteScreen() {
  const { id, farmId } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useAuth();
  const { selectedFarm } = useFarm();
  const { theme, colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const isDarkMode = theme === "dark";

  const [note, setNote] = useState<Note | null>(null);
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [images, setImages] = useState<string[]>([]);
  const [audioUrl, setAudioUrl] = useState<string | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    const loadNote = async () => {
      try {
        if (!id || !farmId) {
          Alert.alert(t("common.error"), t("notes.idRequired"));
          router.back();
          return;
        }

        const noteId = Array.isArray(id) ? id[0] : id as string;
        const farmIdParam = Array.isArray(farmId) ? farmId[0] : farmId as string;

        const noteData = await getNoteById(noteId, farmIdParam);
        if (!noteData) {
          Alert.alert(t("common.error"), t("notes.notFound"));
          router.back();
          return;
        }

        // Check if user can edit this note
        if (noteData.userId !== user?.uid && noteData.createdBy !== user?.uid) {
          Alert.alert(t("common.error"), t("notes.cannotEditOthers"));
          router.back();
          return;
        }

        setNote(noteData);
        setTitle(noteData.title);
        setContent(noteData.content);
        setImages(noteData.images || []);
        setAudioUrl(noteData.audioUrl);
      } catch (error) {
        console.error("Error loading note:", error);
        Alert.alert(t("common.error"), t("notes.loadError"));
      } finally {
        setLoading(false);
      }
    };

    loadNote();

    // Clean up audio resources
    return () => {
      if (recording) {
        recording.stopAndUnloadAsync();
      }
      if (sound) {
        sound.unloadAsync();
      }
    };
  }, [id, farmId, user]);

  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });

      if (!result.canceled) {
        setImages([...images, result.assets[0].uri]);
      }
    } catch (error) {
      console.error("Error picking image:", error);
      Alert.alert(t("common.error"), t("notes.imagePickError"));
    }
  };

  const takePhoto = async () => {
    if (Platform.OS === "web") {
      Alert.alert(t("common.notAvailable"), t("notes.cameraNotAvailableWeb"));
      return;
    }

    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== "granted") {
        Alert.alert(t("common.permissionRequired"), t("notes.cameraPermissionRequired"));
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });

      if (!result.canceled) {
        setImages([...images, result.assets[0].uri]);
      }
    } catch (error) {
      console.error("Error using camera:", error);
      Alert.alert(t("common.error"), t("notes.cameraError"));
    }
  };

  const removeImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
  };

  const startRecording = async () => {
    try {
      if (Platform.OS === "web") {
        Alert.alert(t("common.notAvailable"), t("notes.audioRecordingNotAvailableWeb"));
        return;
      }

      const { status } = await Audio.requestPermissionsAsync();
      if (status !== "granted") {
        Alert.alert(t("common.permissionRequired"), t("notes.microphonePermissionRequired"));
        return;
      }

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      const newRecording = new Audio.Recording();
      await newRecording.prepareToRecordAsync(Audio.RecordingOptionsPresets.HIGH_QUALITY);
      await newRecording.startAsync();
      setRecording(newRecording);
      setIsRecording(true);
    } catch (error) {
      console.error("Error starting recording:", error);
      Alert.alert(t("common.error"), t("notes.recordingStartError"));
    }
  };

  const stopRecording = async () => {
    try {
      if (!recording) return;

      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();
      setRecording(null);
      setIsRecording(false);

      if (uri) {
        setAudioUrl(uri);
      }
    } catch (error) {
      console.error("Error stopping recording:", error);
      Alert.alert(t("common.error"), t("notes.recordingStopError"));
    }
  };

  const playPauseAudio = async () => {
    if (!audioUrl) return;

    try {
      if (sound) {
        if (isPlaying) {
          await sound.pauseAsync();
          setIsPlaying(false);
        } else {
          await sound.playAsync();
          setIsPlaying(true);
        }
      } else {
        const { sound: newSound } = await Audio.Sound.createAsync(
          { uri: audioUrl },
          { shouldPlay: true }
        );
        
        setSound(newSound);
        setIsPlaying(true);
        
        newSound.setOnPlaybackStatusUpdate((status) => {
          if (status.isLoaded && status.didJustFinish) {
            setIsPlaying(false);
          }
        });
      }
    } catch (error) {
      console.error("Error playing audio:", error);
      Alert.alert(t("common.error"), t("notes.audioPlayError"));
    }
  };

  const removeAudio = () => {
    if (sound) {
      sound.unloadAsync();
      setSound(null);
    }
    setAudioUrl(undefined);
    setIsPlaying(false);
  };

  const handleSubmit = async () => {
    if (!title.trim()) {
      setError(t("notes.titleRequired"));
      return;
    }

    if (!content.trim()) {
      setError(t("notes.contentRequired"));
      return;
    }

    if (!note || !farmId) {
      setError(t("notes.missingData"));
      return;
    }

    const farmIdParam = Array.isArray(farmId) ? farmId[0] : farmId as string;

    setSubmitting(true);
    try {
      let uploadedImages: string[] = [];

      // Upload images to Firebase Storage if any local images are selected
      if (images.length > 0) {
        const localImages = images.filter(img => img.startsWith('file://'));
        if (localImages.length > 0) {
          console.log("Uploading note images to Storage...");
          const uploadedUrls = await uploadNoteImages(localImages, note.id);
          console.log("Note images uploaded:", uploadedUrls);

          // Combine uploaded URLs with any existing Firebase URLs
          const existingFirebaseImages = images.filter(img => !img.startsWith('file://'));
          uploadedImages = [...existingFirebaseImages, ...uploadedUrls];
        } else {
          uploadedImages = images; // All images are already Firebase URLs
        }
      }

      await updateNote(note.id, {
        title,
        content,
        images: uploadedImages,
        audioUrl,
        hasAudio: !!audioUrl,
        updatedAt: new Date().toISOString(),
      }, farmIdParam);

      Alert.alert(
        t("common.success"),
        t("common.noteUpdatedSuccessfully"),
        [{ text: t("common.ok"), onPress: () => router.back() }]
      );
    } catch (error) {
      console.error("Error updating note:", error);
      setError(t("common.failedToUpdateNote"));
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <Stack.Screen options={{ 
          title: t("notes.editNote"),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: "#fff",
        }} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            {t("common.loadingNote")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen options={{ 
        title: t("notes.editNote"),
        headerStyle: { backgroundColor: colors.primary },
        headerTintColor: "#fff",
        headerTitleStyle: {
          fontWeight: "600",
        },
      }} />

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <ScrollView 
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.formContainer}>
            {/* Header Banner */}
            <LinearGradient
              colors={isDarkMode 
                ? [colors.primary, `${colors.primary}80`] 
                : [`${colors.primary}15`, `${colors.primary}08`]
              }
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.headerBanner}
            >
              <View style={styles.headerContent}>
                <View style={[styles.headerIconContainer, { backgroundColor: `${colors.primary}20` }]}>
                  <Edit3 size={28} color={colors.primary} />
                </View>
                <View style={styles.headerTextContainer}>
                  <Text style={[styles.headerTitle, { color: isDarkMode ? "#fff" : colors.primary }]}>
                    {t("notes.editNote")}
                  </Text>
                  <Text style={[styles.headerSubtitle, { color: isDarkMode ? colors.textSecondary : `${colors.primary}CC` }]}>
                    {t("common.updateNoteWithNewInfo")}
                  </Text>
                </View>
              </View>
            </LinearGradient>

            <View style={[styles.formCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
              {/* Title Input */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {t("notes.noteTitle")} *
                </Text>
                <TextInput
                  style={[
                    styles.input,
                    { 
                      backgroundColor: isDarkMode ? "#252525" : "#f8f9fa", 
                      borderColor: colors.border, 
                      color: colors.text,
                      textAlign: isRTL ? 'right' : 'left'
                    }
                  ]}
                  value={title}
                  onChangeText={setTitle}
                  placeholder={t("common.enterDescriptiveTitle")}
                  placeholderTextColor={colors.textSecondary}
                />
              </View>

              {/* Content Input */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {t("notes.content")} *
                </Text>
                <TextInput
                  style={[
                    styles.textArea,
                    { 
                      backgroundColor: isDarkMode ? "#252525" : "#f8f9fa", 
                      borderColor: colors.border, 
                      color: colors.text,
                      textAlign: isRTL ? 'right' : 'left'
                    }
                  ]}
                  value={content}
                  onChangeText={setContent}
                  placeholder={t("common.writeDetailedNote")}
                  placeholderTextColor={colors.textSecondary}
                  multiline
                  numberOfLines={8}
                  textAlignVertical="top"
                />
              </View>

              {/* Attachments Section */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  📎 {t("common.attachments")}
                </Text>
                
                <View style={styles.attachmentButtons}>
                  <TouchableOpacity
                    style={[styles.attachmentButton, { backgroundColor: `${colors.info}15` }]}
                    onPress={pickImage}
                  >
                    <Upload size={20} color={colors.info} />
                    <Text style={[styles.attachmentButtonText, { color: colors.info }]}>{t("common.gallery")}</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[styles.attachmentButton, { backgroundColor: `${colors.warning}15` }]}
                    onPress={takePhoto}
                  >
                    <Camera size={20} color={colors.warning} />
                    <Text style={[styles.attachmentButtonText, { color: colors.warning }]}>{t("common.camera")}</Text>
                  </TouchableOpacity>
                </View>

                {/* Images Preview */}
                {images.length > 0 && (
                  <View style={styles.imagesContainer}>
                    <Text style={[styles.sectionSubtitle, { color: colors.textSecondary }]}>
                      📷 {t("common.imagesCount", { count: images.length })}
                    </Text>
                    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.imagesScrollView}>
                      {images.map((uri, index) => (
                        <View key={index} style={styles.imageWrapper}>
                          <Image source={{ uri }} style={styles.image} />
                          <TouchableOpacity
                            style={styles.removeImageButton}
                            onPress={() => removeImage(index)}
                          >
                            <X size={18} color="#fff" />
                          </TouchableOpacity>
                        </View>
                      ))}
                    </ScrollView>
                  </View>
                )}
              </View>

              {/* Audio Section */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  🎵 {t("common.voiceNote")}
                </Text>
                
                {!audioUrl ? (
                  <TouchableOpacity
                    style={[
                      styles.recordButton,
                      isRecording 
                        ? { backgroundColor: colors.error } 
                        : { backgroundColor: colors.primary }
                    ]}
                    onPress={isRecording ? stopRecording : startRecording}
                  >
                    <Mic size={20} color="#fff" />
                    <Text style={styles.recordButtonText}>
                      {isRecording ? t("common.stopRecording") : t("common.recordVoiceNote")}
                    </Text>
                  </TouchableOpacity>
                ) : (
                  <View style={[styles.audioContainer, { backgroundColor: `${colors.primary}10`, borderColor: `${colors.primary}30` }]}>
                    <TouchableOpacity
                      style={[styles.audioControlButton, { backgroundColor: colors.primary }]}
                      onPress={playPauseAudio}
                    >
                      {isPlaying ? (
                        <Pause size={20} color="#fff" />
                      ) : (
                        <Play size={20} color="#fff" />
                      )}
                    </TouchableOpacity>
                    
                    <View style={styles.audioInfo}>
                      <Text style={[styles.audioText, { color: colors.text }]}>
                        {isPlaying ? t("common.playingAudio") : t("common.audioRecordingReady")}
                      </Text>
                      <Text style={[styles.audioSubtext, { color: colors.textSecondary }]}>
                        {isPlaying ? t("common.tapToPause") : t("common.tapToPlay")}
                      </Text>
                    </View>
                    
                    <TouchableOpacity
                      style={[styles.audioControlButton, { backgroundColor: colors.error }]}
                      onPress={removeAudio}
                    >
                      <X size={20} color="#fff" />
                    </TouchableOpacity>
                  </View>
                )}
              </View>

              {/* Error Display */}
              {error ? (
                <View style={[styles.errorContainer, { backgroundColor: `${colors.error}15`, borderColor: `${colors.error}30` }]}>
                  <Text style={[styles.errorText, { color: colors.error }]}>⚠️ {error}</Text>
                </View>
              ) : null}
            </View>

            {/* Action Buttons */}
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[
                  styles.cancelButton, 
                  { 
                    backgroundColor: isDarkMode ? "#252525" : "#f8f9fa",
                    borderColor: colors.border 
                  }
                ]}
                onPress={() => router.back()}
                disabled={submitting}
              >
                <ArrowLeft size={20} color={colors.textSecondary} />
                <Text style={[styles.cancelButtonText, { color: colors.textSecondary }]}>
                  {t("common.cancel")}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.submitButton, { backgroundColor: colors.primary }]}
                onPress={handleSubmit}
                disabled={submitting}
              >
                {submitting ? (
                  <ActivityIndicator color="#fff" size="small" />
                ) : (
                  <>
                    <Save size={20} color="#fff" />
                    <Text style={styles.submitButtonText}>{t("common.saveChanges")}</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
  },
  headerBanner: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "700",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 15,
    lineHeight: 20,
  },
  formCard: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  formGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1.5,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    fontWeight: "500",
  },
  textArea: {
    borderWidth: 1.5,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    minHeight: 180,
    textAlignVertical: "top",
    lineHeight: 24,
  },
  attachmentButtons: {
    flexDirection: "row",
    marginBottom: 16,
    gap: 12,
  },
  attachmentButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    gap: 8,
    flex: 1,
    justifyContent: "center",
  },
  attachmentButtonText: {
    fontSize: 15,
    fontWeight: "600",
  },
  sectionSubtitle: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 8,
  },
  imagesContainer: {
    marginTop: 16,
  },
  imagesScrollView: {
    marginHorizontal: -4,
  },
  imageWrapper: {
    position: "relative",
    marginHorizontal: 4,
    width: 120,
    height: 120,
  },
  image: {
    width: "100%",
    height: "100%",
    borderRadius: 12,
  },
  removeImageButton: {
    position: "absolute",
    top: 6,
    right: 6,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: "center",
    alignItems: "center",
  },
  recordButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
  },
  recordButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  audioContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  audioControlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
  },
  audioInfo: {
    flex: 1,
    marginHorizontal: 16,
  },
  audioText: {
    fontSize: 15,
    fontWeight: "600",
    marginBottom: 2,
  },
  audioSubtext: {
    fontSize: 13,
  },
  errorContainer: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 15,
    fontWeight: "500",
  },
  buttonContainer: {
    flexDirection: "row",
    gap: 12,
    marginBottom: 24,
  },
  cancelButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 14,
    borderRadius: 12,
    borderWidth: 1.5,
    gap: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
  submitButton: {
    flex: 2,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 14,
    borderRadius: 12,
    gap: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  submitButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});
import React, { useState, useEffect, useRef } from "react";
import { StyleSheet, Text, View, TextInput, TouchableOpacity, ScrollView, Alert, ActivityIndicator, Platform, Modal } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter, useLocalSearchParams } from "expo-router";
import { Audio } from "expo-av";
import * as ImagePicker from "expo-image-picker";
import { Image, Mic, X, Camera, Upload, Pause, Play, Trash2, Save, MicOff, FileText, Plus } from "lucide-react-native";
import { createNote } from "@/services/notes-service";
import { uploadNoteImages } from "@/services/storage-service";
import { useAuth } from "@/context/auth-context";
import { useFarm } from "@/context/farm-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { LinearGradient } from "expo-linear-gradient";

export default function CreateNoteScreen() {
  const router = useRouter();
  const { farmId } = useLocalSearchParams();
  const { user } = useAuth();
  const { selectedFarm, farms } = useFarm();
  const { colors } = useTheme();
  const { t, isRTL } = useLanguage();
  
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [images, setImages] = useState<string[]>([]);
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [audioUri, setAudioUri] = useState<string | null>(null);
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [loading, setLoading] = useState(false);
  const [farm, setFarm] = useState(selectedFarm);
  
  // Voice recognition states
  const [isRecognizing, setIsRecognizing] = useState(false);
  const [recognizedText, setRecognizedText] = useState("");
  const recognitionTimer = useRef<NodeJS.Timeout | null>(null);
  
  useEffect(() => {
    // Find farm by ID if provided in params
    if (farmId && farms.length > 0) {
      const foundFarm = farms.find(f => f.id === farmId);
      if (foundFarm) {
        setFarm(foundFarm);
      }
    } else {
      setFarm(selectedFarm);
    }
    
    // Request permissions
    const getPermissions = async () => {
      const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
      const { status: microphoneStatus } = await Audio.requestPermissionsAsync();
      
      if (cameraStatus !== "granted" || microphoneStatus !== "granted") {
        Alert.alert(
          t("common.permissionsRequired"),
          t("common.grantCameraAndMicPermissions")
        );
      }
    };
    
    getPermissions();
    
    // Clean up audio resources
    return () => {
      if (sound) {
        sound.unloadAsync();
      }
      if (recording) {
        recording.stopAndUnloadAsync();
      }
      if (recognitionTimer.current) {
        clearTimeout(recognitionTimer.current);
      }
    };
  }, [farmId, farms, selectedFarm]);
  
  const startRecording = async () => {
    try {
      // Prepare the recording
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });
      
      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );
      
      setRecording(recording);
      setIsRecording(true);
    } catch (error) {
      console.error("Failed to start recording", error);
      Alert.alert(t("common.error"), t("notes.recordingStartError"));
    }
  };
  
  const stopRecording = async () => {
    if (!recording) return;
    
    try {
      setIsRecording(false);
      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();
      setAudioUri(uri);
      setRecording(null);
      
      // Reset audio mode
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        playsInSilentModeIOS: true,
      });
    } catch (error) {
      console.error("Failed to stop recording", error);
      Alert.alert(t("common.error"), t("notes.recordingStopError"));
    }
  };
  
  const playSound = async () => {
    if (!audioUri) return;
    
    try {
      if (sound) {
        await sound.unloadAsync();
      }
      
      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: audioUri },
        { shouldPlay: true }
      );
      
      setSound(newSound);
      setIsPlaying(true);
      
      newSound.setOnPlaybackStatusUpdate((status) => {
        if (status.isLoaded && !status.isPlaying && status.didJustFinish) {
          setIsPlaying(false);
        }
      });
    } catch (error) {
      console.error("Failed to play sound", error);
      Alert.alert(t("common.error"), t("notes.audioPlayError"));
    }
  };
  
  const pauseSound = async () => {
    if (!sound) return;
    
    try {
      await sound.pauseAsync();
      setIsPlaying(false);
    } catch (error) {
      console.error("Failed to pause sound", error);
    }
  };
  
  const deleteRecording = () => {
    setAudioUri(null);
    if (sound) {
      sound.unloadAsync();
      setSound(null);
    }
    setIsPlaying(false);
  };
  
  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Remove cropping
        quality: 0.8,
        allowsMultipleSelection: true,
      });

      if (!result.canceled && result.assets) {
        const newImages = result.assets.map(asset => asset.uri);
        setImages([...images, ...newImages]);
      }
    } catch (error) {
      console.error("Error picking image:", error);
      Alert.alert(t("common.error"), t("notes.imagePickError"));
    }
  };
  
  const takePhoto = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setImages([...images, result.assets[0].uri]);
      }
    } catch (error) {
      console.error("Error taking photo:", error);
      Alert.alert(t("common.error"), t("notes.cameraError"));
    }
  };
  
  const removeImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
  };
  
  // Simulate voice recognition
  const startVoiceRecognition = () => {
    setIsRecognizing(true);
    
    // Simulate processing time
    recognitionTimer.current = setTimeout(() => {
      // Generate some placeholder text based on what's already in the content
      const placeholderTexts = [
        "I noticed some issues with the irrigation system in the north field.",
        "The tomato plants in section B need additional support stakes.",
        "We should schedule a maintenance check for the greenhouse heating system.",
        "The new fertilizer seems to be working well on the test plots.",
        "Several fruit trees in the orchard show signs of pest damage.",
      ];
      
      // Pick a random placeholder text
      const randomText = placeholderTexts[Math.floor(Math.random() * placeholderTexts.length)];
      setRecognizedText(randomText);
      
      // Add the recognized text to the content
      setContent(prevContent => {
        if (prevContent.trim()) {
          return `${prevContent}

${randomText}`;
        }
        return randomText;
      });
      
      setIsRecognizing(false);
    }, 2000); // Simulate 2 seconds of processing
  };
  
  const stopVoiceRecognition = () => {
    if (recognitionTimer.current) {
      clearTimeout(recognitionTimer.current);
    }
    setIsRecognizing(false);
  };
  
  const handleSave = async () => {
    if (!title.trim()) {
      Alert.alert(t("common.error"), t("common.enterTitleForNote"));
      return;
    }
    
    if (!content.trim()) {
      Alert.alert(t("common.error"), t("common.enterContentForNote"));
      return;
    }
    
    if (!farm) {
      Alert.alert(t("common.error"), t("common.selectFarmForNote"));
      return;
    }
    
    setLoading(true);

    try {
      let uploadedImages: string[] = [];

      // Upload images to Firebase Storage if any local images are selected
      if (images.length > 0) {
        const localImages = images.filter(img => img.startsWith('file://'));
        if (localImages.length > 0) {
          console.log("Uploading note images to Storage...");
          const tempNoteId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
          const uploadedUrls = await uploadNoteImages(localImages, tempNoteId);
          console.log("Note images uploaded:", uploadedUrls);

          // Combine uploaded URLs with any existing Firebase URLs
          const existingFirebaseImages = images.filter(img => !img.startsWith('file://'));
          uploadedImages = [...existingFirebaseImages, ...uploadedUrls];
        } else {
          uploadedImages = images; // All images are already Firebase URLs
        }
      }

      await createNote({
        title,
        content,
        farmId: farm.id,
        userId: user?.uid || "",
        userName: user?.name || user?.displayName || "",
        createdBy: user?.uid || "",
        createdByName: user?.name || user?.displayName || "",
        images: uploadedImages.length > 0 ? uploadedImages : undefined,
        audioUrl: audioUri || undefined,
        hasAudio: !!audioUri,
        createdAt: new Date().toISOString(),
      });
      
      Alert.alert(t("common.success"), t("common.noteCreatedSuccessfully"), [
        {
          text: t("common.ok"),
          onPress: () => router.back(),
        },
      ]);
    } catch (error) {
      console.error("Error creating note:", error);
      Alert.alert(t("common.error"), t("common.failedToCreateNote"));
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t("notes.addNote"),
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: "#fff",
          headerTitleStyle: {
            fontWeight: "600",
          },
        }}
      />
      
      <ScrollView 
        style={styles.scrollView} 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header Banner */}
        <LinearGradient
          colors={[`${colors.primary}15`, `${colors.primary}08`]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerBanner}
        >
          <View style={styles.headerContent}>
            <View style={[styles.headerIconContainer, { backgroundColor: `${colors.primary}20` }]}>
              <FileText size={28} color={colors.primary} />
            </View>
            <View style={styles.headerTextContainer}>
              <Text style={[styles.headerTitle, { color: colors.primary }]}>
                {t("common.createNewNote")}
              </Text>
              <Text style={[styles.headerSubtitle, { color: `${colors.primary}CC` }]}>
                {t("common.documentImportantObservations")}
              </Text>
            </View>
          </View>
        </LinearGradient>

        <View style={[styles.formCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          {/* Farm Badge */}
          {farm && (
            <View style={[styles.farmBadge, { backgroundColor: `${colors.primary}15` }]}>
              <Text style={[styles.farmBadgeText, { color: colors.primary }]}>
                📍 {farm.name}
              </Text>
            </View>
          )}

          {/* Title Input */}
          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: colors.text }]}>{t("notes.noteTitle")} *</Text>
            <TextInput
              style={[styles.titleInput, { 
                color: colors.text, 
                borderColor: colors.border,
                backgroundColor: colors.background === '#121212' ? "#252525" : "#f8f9fa",
                textAlign: isRTL ? 'right' : 'left'
              }]}
              placeholder={t("common.enterDescriptiveTitle")}
              placeholderTextColor={colors.textSecondary}
              value={title}
              onChangeText={setTitle}
            />
          </View>
          
          {/* Content Input */}
          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: colors.text }]}>{t("notes.content")} *</Text>
            <TextInput
              style={[styles.contentInput, { 
                color: colors.text, 
                borderColor: colors.border,
                backgroundColor: colors.background === '#121212' ? "#252525" : "#f8f9fa",
                textAlign: isRTL ? 'right' : 'left'
              }]}
              placeholder={t("common.writeDetailedNote")}
              placeholderTextColor={colors.textSecondary}
              multiline
              numberOfLines={8}
              textAlignVertical="top"
              value={content}
              onChangeText={setContent}
            />
          </View>
          
          {/* Voice Recognition Status */}
          {isRecognizing && (
            <View style={styles.recognitionContainer}>
              <LinearGradient
                colors={[`${colors.primary}20`, `${colors.primary}10`]}
                style={styles.recognitionBanner}
              >
                <View style={styles.recognitionContent}>
                  <ActivityIndicator size="small" color={colors.primary} />
                  <Text style={[styles.recognitionText, { color: colors.primary }]}>
                    {t("common.listeningAndConverting")}
                  </Text>
                </View>
              </LinearGradient>
            </View>
          )}
          
          {recognizedText && !isRecognizing && (
            <View style={[styles.recognizedTextContainer, { backgroundColor: `${colors.success}10`, borderColor: `${colors.success}30` }]}>
              <Text style={[styles.recognizedTextLabel, { color: colors.success }]}>✓ {t("common.recognizedText")}:</Text>
              <Text style={[styles.recognizedTextContent, { color: colors.textSecondary }]}>
                "{recognizedText}"
              </Text>
            </View>
          )}
          
          {/* Images Preview */}
          {images.length > 0 && (
            <View style={styles.attachmentsSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>📷 {t("common.imagesCount", { count: images.length })}</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.imagesScrollView}>
                {images.map((uri, index) => (
                  <View key={index} style={styles.imagePreviewContainer}>
                    <Image
                      source={{ uri }}
                      style={styles.imagePreview}
                      width={120}
                      height={120}
                    />
                    <TouchableOpacity
                      style={styles.removeImageButton}
                      onPress={() => removeImage(index)}
                    >
                      <X size={16} color="#fff" />
                    </TouchableOpacity>
                  </View>
                ))}
              </ScrollView>
            </View>
          )}
          
          {/* Audio Preview */}
          {audioUri && (
            <View style={styles.attachmentsSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>🎵 {t("common.audioRecording")}</Text>
              <View style={[styles.audioPlayer, { backgroundColor: `${colors.primary}10`, borderColor: `${colors.primary}30` }]}>
                <TouchableOpacity
                  style={[styles.audioControlButton, { backgroundColor: colors.primary }]}
                  onPress={isPlaying ? pauseSound : playSound}
                >
                  {isPlaying ? (
                    <Pause size={20} color="#fff" />
                  ) : (
                    <Play size={20} color="#fff" />
                  )}
                </TouchableOpacity>
                
                <View style={styles.audioInfo}>
                  <Text style={[styles.audioText, { color: colors.text }]}>
                    {isPlaying ? t("common.playingAudio") : t("common.audioRecordingReady")}
                  </Text>
                  <Text style={[styles.audioSubtext, { color: colors.textSecondary }]}>
                    {isPlaying ? t("common.tapToPause") : t("common.tapToPlay")}
                  </Text>
                </View>
                
                <TouchableOpacity
                  style={[styles.audioControlButton, { backgroundColor: colors.error }]}
                  onPress={deleteRecording}
                >
                  <Trash2 size={20} color="#fff" />
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      </ScrollView>
      
      {/* Action Buttons Footer */}
      <View style={[styles.footer, { backgroundColor: colors.surface, borderTopColor: colors.border }]}>
        <Text style={[styles.footerTitle, { color: colors.text }]}>{t("common.addAttachments")}</Text>
        
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: `${colors.info}15` }]}
            onPress={pickImage}
          >
            <Upload size={24} color={colors.info} />
            <Text style={[styles.actionButtonText, { color: colors.info }]}>{t("common.gallery")}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: `${colors.warning}15` }]}
            onPress={takePhoto}
          >
            <Camera size={24} color={colors.warning} />
            <Text style={[styles.actionButtonText, { color: colors.warning }]}>{t("common.camera")}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.actionButton,
              isRecording
                ? { backgroundColor: `${colors.error}15` }
                : { backgroundColor: `${colors.primary}15` }
            ]}
            onPress={isRecording ? stopRecording : startRecording}
          >
            {isRecording ? (
              <>
                <Pause size={24} color={colors.error} />
                <Text style={[styles.actionButtonText, { color: colors.error }]}>{t("common.stopRecording")}</Text>
              </>
            ) : (
              <>
                <Mic size={24} color={colors.primary} />
                <Text style={[styles.actionButtonText, { color: colors.primary }]}>{t("common.recordVoiceNote")}</Text>
              </>
            )}
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.actionButton,
              isRecognizing
                ? { backgroundColor: `${colors.error}15` }
                : { backgroundColor: `${colors.success}15` }
            ]}
            onPress={isRecognizing ? stopVoiceRecognition : startVoiceRecognition}
          >
            {isRecognizing ? (
              <>
                <MicOff size={24} color={colors.error} />
                <Text style={[styles.actionButtonText, { color: colors.error }]}>{t("common.stopListening")}</Text>
              </>
            ) : (
              <>
                <Mic size={24} color={colors.success} />
                <Text style={[styles.actionButtonText, { color: colors.success }]}>{t("common.voiceNote")}</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
        
        <TouchableOpacity
          style={[styles.saveButton, { backgroundColor: colors.primary }]}
          onPress={handleSave}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <>
              <Save size={22} color="#fff" />
              <Text style={styles.saveButtonText}>{t("common.saveNote")}</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
      
      {/* Voice Recognition Modal */}
      <Modal
        visible={isRecognizing}
        transparent={true}
        animationType="fade"
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
            <LinearGradient
              colors={[colors.primary, colors.primaryDark]}
              style={styles.modalGradient}
            >
              <View style={styles.modalIconContainer}>
                <Mic size={48} color="#fff" />
              </View>
            </LinearGradient>
            
            <View style={styles.modalBody}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>{t("common.listening")}</Text>
              <Text style={[styles.modalSubtitle, { color: colors.textSecondary }]}>
                {t("common.speakClearly")}
              </Text>
              
              <View style={styles.modalWaveform}>
                {[...Array(7)].map((_, i) => (
                  <View 
                    key={i} 
                    style={[
                      styles.waveformBar, 
                      { 
                        height: 8 + Math.random() * 32,
                        backgroundColor: colors.primary,
                        opacity: 0.4 + Math.random() * 0.6
                      }
                    ]} 
                  />
                ))}
              </View>
              
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.error }]}
                onPress={stopVoiceRecognition}
              >
                <MicOff size={20} color="#fff" />
                <Text style={styles.modalButtonText}>{t("common.stopListening")}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 200, // Extra padding for footer
  },
  headerBanner: {
    margin: 16,
    marginBottom: 8,
    borderRadius: 16,
    padding: 20,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "700",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 15,
    lineHeight: 20,
  },
  formCard: {
    margin: 16,
    marginTop: 8,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  farmBadge: {
    borderRadius: 12,
    paddingVertical: 10,
    paddingHorizontal: 16,
    marginBottom: 20,
    alignSelf: "flex-start",
  },
  farmBadgeText: {
    fontSize: 15,
    fontWeight: "600",
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
  },
  titleInput: {
    height: 52,
    borderWidth: 1.5,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    fontWeight: "500",
  },
  contentInput: {
    height: 180,
    borderWidth: 1.5,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingTop: 16,
    fontSize: 16,
    lineHeight: 24,
    textAlignVertical: "top",
  },
  recognitionContainer: {
    marginBottom: 20,
  },
  recognitionBanner: {
    borderRadius: 12,
    padding: 16,
  },
  recognitionContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  recognitionText: {
    marginLeft: 12,
    fontSize: 15,
    fontWeight: "500",
  },
  recognizedTextContainer: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
  },
  recognizedTextLabel: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 8,
  },
  recognizedTextContent: {
    fontSize: 15,
    fontStyle: "italic",
    lineHeight: 22,
  },
  attachmentsSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
  },
  imagesScrollView: {
    marginHorizontal: -4,
  },
  imagePreviewContainer: {
    position: "relative",
    marginHorizontal: 4,
  },
  imagePreview: {
    width: 120,
    height: 120,
    borderRadius: 12,
  },
  removeImageButton: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    borderRadius: 16,
    width: 32,
    height: 32,
    justifyContent: "center",
    alignItems: "center",
  },
  audioPlayer: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
  },
  audioControlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
  },
  audioInfo: {
    flex: 1,
    marginHorizontal: 16,
  },
  audioText: {
    fontSize: 15,
    fontWeight: "600",
    marginBottom: 2,
  },
  audioSubtext: {
    fontSize: 13,
  },
  footer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: Platform.OS === "ios" ? 32 : 16,
  },
  footerTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
    textAlign: "center",
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  actionButton: {
    flex: 1,
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 12,
    marginHorizontal: 4,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: "600",
    marginTop: 4,
  },
  saveButton: {
    height: 52,
    borderRadius: 16,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  saveButtonText: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "600",
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  modalContent: {
    width: "100%",
    maxWidth: 320,
    borderRadius: 20,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  modalGradient: {
    height: 140,
    justifyContent: "center",
    alignItems: "center",
  },
  modalIconContainer: {
    width: 88,
    height: 88,
    borderRadius: 44,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalBody: {
    padding: 24,
    alignItems: "center",
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: "700",
    marginBottom: 8,
  },
  modalSubtitle: {
    fontSize: 15,
    textAlign: "center",
    lineHeight: 22,
    marginBottom: 24,
  },
  modalWaveform: {
    flexDirection: "row",
    alignItems: "flex-end",
    height: 48,
    marginBottom: 24,
  },
  waveformBar: {
    width: 4,
    marginHorizontal: 2,
    borderRadius: 2,
  },
  modalButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    minWidth: 160,
  },
  modalButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
});
import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, TouchableOpacity, Switch, TextInput, ScrollView, Platform, ActivityIndicator, Alert, Dimensions } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Image } from "expo-image";
import * as ImagePicker from "expo-image-picker";
import { useAuth } from "@/context/auth-context";
import { useFarm } from "@/context/farm-context";
import { updateUserProfile } from "@/services/user-service";
import { uploadUserProfileImage, deleteImageFromStorage } from "@/services/storage-service";
import { getInventoryRequests, getRequestsByType } from "@/services/request-service";
import { getInventoryItems } from "@/services/inventory-service";
import { getMachineryByFarm } from "@/services/machinery-service";
import { getNotesByFarm } from "@/services/notes-service";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { LogOut, Camera, Sun, Moon, Settings, MapPin, ClipboardList, Globe, Package, CheckCircle, Truck, User, Mail, Phone, Calendar, Edit3, FileText, Lock, Shield, ChevronRight } from "lucide-react-native";
import Constants from "expo-constants";
import { router } from "expo-router";

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Helper functions for responsive design
const getResponsivePadding = () => {
  if (screenWidth < 380) return 16;
  if (screenWidth < 420) return 20;
  return 24;
};

const getResponsiveFontSize = (baseSize: number) => {
  if (screenWidth < 380) return baseSize - 2;
  if (screenWidth < 420) return baseSize - 1;
  return baseSize;
};

const getResponsiveImageSize = () => {
  if (screenWidth < 380) return 80;
  if (screenWidth < 420) return 90;
  return 100;
};

const getResponsiveMargin = () => {
  if (screenWidth < 380) return 12;
  if (screenWidth < 420) return 16;
  return 24;
};

const getResponsiveIconSize = () => {
  if (screenWidth < 380) return 16;
  if (screenWidth < 420) return 18;
  return 20;
};

export default function ProfileScreen() {
  const { user, signOut, refreshUserData } = useAuth();
  const { selectedFarm } = useFarm();
  const { theme, toggleTheme } = useTheme();
  const { language, setLanguage, isRTL, t } = useLanguage();
  const [uploading, setUploading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState({
    assignedFarm: '',
    availableItems: 0,
    availableMachinery: 0,
    pendingRequests: 0,
    memberSince: '',
    lastLogin: '',
  });

  useEffect(() => {
    loadProfileStats();
  }, [user, selectedFarm]);



  const loadProfileStats = async () => {
    if (!user) return;
    
    try {
      // Get assigned farm name
      const assignedFarm = selectedFarm ? selectedFarm.name : t("profile.notProvided");
      
      let availableItems = 0;
      let availableMachinery = 0;
      let pendingRequests = 0;
      
      if (selectedFarm) {
        // Get inventory items for selected farm
        const items = await getInventoryItems(selectedFarm.id);
        availableItems = items.filter(item => item.quantity > 0).length;
        
        // Get machinery for selected farm
        const machinery = await getMachineryByFarm(selectedFarm.id);
        availableMachinery = machinery.filter(m => m.status === 'working').length;
        
        // Get pending requests for this user
        const pending = await getInventoryRequests(selectedFarm.id, user.uid, 'pending');
        pendingRequests = pending.length;
      }
      
      // Calculate member since date using createdAt field
      const memberSince = user.createdAt 
        ? (typeof user.createdAt === 'string' ? new Date(user.createdAt).toLocaleDateString() : new Date().toLocaleDateString())
        : new Date().toLocaleDateString();
      
      // Calculate last login using lastLogin field
      const lastLogin = user.lastLogin
        ? (typeof user.lastLogin === 'string' ? new Date(user.lastLogin).toLocaleDateString() : new Date().toLocaleDateString())
        : new Date().toLocaleDateString();
      
      setStats({
        assignedFarm,
        availableItems,
        availableMachinery,
        pendingRequests,
        memberSince,
        lastLogin,
      });
    } catch (error) {
      console.error('Error loading profile stats:', error);
    }
  };



  const pickImage = async () => {
    if (!user) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setUploading(true);
        const imageUri = result.assets[0].uri;

        try {
          // Delete old image from Storage if it exists
          if (user.photoURL) {
            await deleteImageFromStorage(user.photoURL);
          }

          // Upload new image to Firebase Storage
          const downloadURL = await uploadUserProfileImage(imageUri, user.uid);
          console.log("Image uploaded to Storage:", downloadURL);

          // Update user profile with the Firebase Storage download URL
          await updateUserProfile(user.uid, { photoURL: downloadURL });
          await refreshUserData();
        } catch (error) {
          console.error("Error uploading image:", error);
          Alert.alert("Error", "Failed to upload image. Please try again.");
        }
      }
    } catch (error) {
      console.error("Error updating profile picture:", error);
    } finally {
      setUploading(false);
    }
  };

  const handleLogout = async () => {
    setLoading(true);
    try {
      await signOut();
      // Navigation is handled by the auth context
    } catch (error) {
      console.error("Error signing out:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleLanguageToggle = async () => {
    const newLanguage = language === 'en' ? 'ur' : 'en';
    await setLanguage(newLanguage);
  };

  const handleChangePassword = () => {
    router.push("/(app)/change-password");
  };

  const isDarkMode = theme === "dark";
  const backgroundColor = isDarkMode ? "#0f0f0f" : "#f8fafc";
  const cardColor = isDarkMode ? "#1a1a1a" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#1e293b";
  const secondaryTextColor = isDarkMode ? "#94a3b8" : "#64748b";
  const borderColor = isDarkMode ? "#2d2d2d" : "#e2e8f0";
  const primaryColor = "#FF9800";

  if (!user) {
    return (
      <View style={[styles.container, { backgroundColor }]}>
        <ActivityIndicator size="large" color={primaryColor} />
      </View>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={["bottom"]}>
      <ScrollView style={[styles.scrollView, { direction: isRTL ? 'rtl' : 'ltr' }]} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={[styles.header, isRTL && styles.headerRTL]}>
          <Text style={[styles.title, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}>
            {t("profile.title")}
          </Text>
        </View>

        {/* Profile Card */}
        <View style={[styles.profileCard, { backgroundColor: cardColor }]}>
          <View style={styles.profileImageContainer}>
            {uploading ? (
              <View style={[styles.profileImage, styles.uploadingContainer, { backgroundColor: `${primaryColor}20` }]}>
                <ActivityIndicator color={primaryColor} size="large" />
              </View>
            ) : (
              <>
                {user.photoURL ? (
                  <Image source={{ uri: user.photoURL }} style={styles.profileImage} />
                ) : (
                  <View style={[styles.profileImagePlaceholder, { backgroundColor: primaryColor }]}>
                    <Text style={styles.profileImagePlaceholderText}>
                      {user.displayName ? user.displayName.substring(0, 2).toUpperCase() : "U"}
                    </Text>
                  </View>
                )}

              </>
            )}
          </View>

          <View style={styles.profileInfo}>
            <Text style={[styles.userName, { color: textColor, textAlign: 'center' }]}>
              {user?.displayName || t("common.unknown")}
            </Text>
            
            <View style={[styles.roleBadge, { backgroundColor: `${primaryColor}15` }]}>
              <Text style={[styles.roleText, { color: primaryColor }]}>
                {t(`profile.${user.role}`)}
              </Text>
            </View>
            
            <Text style={[styles.userEmail, { color: secondaryTextColor, textAlign: 'center' }]}>
              {user.email}
            </Text>
          </View>
        </View>

        {/* Stats Grid */}
        <View style={styles.statsGrid}>
          <View style={[styles.statCard, { backgroundColor: cardColor }]}>
            <View style={[styles.statIcon, { backgroundColor: '#10b98115' }]}>
              <Package size={getResponsiveIconSize()} color="#10b981" />
            </View>
            <Text style={[styles.statNumber, { color: textColor }]}>{stats.availableItems}</Text>
            <Text style={[styles.statLabel, { color: secondaryTextColor }]}>
              {t("dashboard.availableItems")}
            </Text>
          </View>

          <View style={[styles.statCard, { backgroundColor: cardColor }]}>
            <View style={[styles.statIcon, { backgroundColor: '#3b82f615' }]}>
              <Truck size={getResponsiveIconSize()} color="#3b82f6" />
            </View>
            <Text style={[styles.statNumber, { color: textColor }]}>{stats.availableMachinery}</Text>
            <Text style={[styles.statLabel, { color: secondaryTextColor }]}>
              {t("machinery.title")}
            </Text>
          </View>

          <View style={[styles.statCard, { backgroundColor: cardColor }]}>
            <View style={[styles.statIcon, { backgroundColor: '#f59e0b15' }]}>
              <ClipboardList size={getResponsiveIconSize()} color="#f59e0b" />
            </View>
            <Text style={[styles.statNumber, { color: textColor }]}>{stats.pendingRequests}</Text>
            <Text style={[styles.statLabel, { color: secondaryTextColor }]}>
              {t("common.pending")}
            </Text>
          </View>
        </View>

        {/* Personal Information */}
        <View style={[styles.infoCard, { backgroundColor: cardColor }]}>
          <View style={[styles.sectionHeader, isRTL && styles.sectionHeaderRTL]}>
            <Text style={[styles.sectionTitle, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}>
              {t("profile.personalInformation")}
            </Text>

            <TouchableOpacity
              style={[styles.editButton, { backgroundColor: `${primaryColor}15` }]}
              onPress={() => router.push('/(app)/profile-details')}
            >
              <ChevronRight size={16} color={primaryColor} />
              <Text style={[styles.editButtonText, { color: primaryColor, marginLeft: isRTL ? 0 : 4, marginRight: isRTL ? 4 : 0 }]}>
                {t("settings.viewDetails")}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Name */}
          <View style={[styles.infoRow, isRTL && styles.infoRowRTL]}>
            <View style={[styles.infoRowLeft, isRTL && styles.infoRowLeftRTL]}>
              <User size={18} color={secondaryTextColor} />
              <Text style={[styles.infoLabel, { color: secondaryTextColor, marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
                {t("profile.name")}
              </Text>
            </View>
            <Text style={[styles.infoValue, { color: textColor, textAlign: isRTL ? 'left' : 'right' }]}>
              {user?.displayName || t("profile.notSet")}
            </Text>
          </View>

          {/* Phone */}
          <View style={[styles.infoRow, isRTL && styles.infoRowRTL]}>
            <View style={[styles.infoRowLeft, isRTL && styles.infoRowLeftRTL]}>
              <Phone size={18} color={secondaryTextColor} />
              <Text style={[styles.infoLabel, { color: secondaryTextColor, marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
                {t("profile.phone")}
              </Text>
            </View>
            <Text style={[styles.infoValue, { color: textColor, textAlign: isRTL ? 'left' : 'right' }]}>
              {user?.phone || user?.phoneNumber || t("profile.notSet")}
            </Text>
          </View>
        </View>


        {/* Settings */}
        <View style={[styles.settingsCard, { backgroundColor: cardColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}>
            {t("settings.title")}
          </Text>
          
          <View style={[styles.settingItem, isRTL && styles.settingItemRTL]}>
            <View style={[styles.settingLeft, isRTL && styles.settingLeftRTL]}>
              {isDarkMode ? (
                <Moon size={20} color={secondaryTextColor} />
              ) : (
                <Sun size={20} color={secondaryTextColor} />
              )}
              <Text style={[styles.settingLabel, { color: textColor, marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
                {isDarkMode ? t("profile.darkMode") : t("profile.lightMode")}
              </Text>
            </View>
            <Switch
              value={isDarkMode}
              onValueChange={toggleTheme}
              trackColor={{ false: "#e2e8f0", true: `${primaryColor}40` }}
              thumbColor={isDarkMode ? primaryColor : "#ffffff"}
            />
          </View>

          <View style={[styles.settingItem, isRTL && styles.settingItemRTL]}>
            <View style={[styles.settingLeft, isRTL && styles.settingLeftRTL]}>
              <Globe size={20} color={secondaryTextColor} />
              <Text style={[styles.settingLabel, { color: textColor, marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
                {t("common.language")}
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.languageButton, { backgroundColor: `${primaryColor}15` }]}
              onPress={handleLanguageToggle}
            >
              <Text style={[styles.languageButtonText, { color: primaryColor }]}>
                {language === 'en' ? t("profile.urdu") : t("profile.english")}
              </Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity 
            style={[styles.settingItem, styles.changePasswordItem, isRTL && styles.settingItemRTL]}
            onPress={handleChangePassword}
          >
            <View style={[styles.settingLeft, isRTL && styles.settingLeftRTL]}>
              <View style={[styles.changePasswordIcon, { backgroundColor: `${primaryColor}15` }]}>
                <Shield size={screenWidth < 380 ? 16 : 18} color={primaryColor} />
              </View>
              <View style={styles.changePasswordContent}>
                <Text
                  style={[styles.changePasswordTitle, { color: textColor }]}
                  numberOfLines={1}
                  adjustsFontSizeToFit={screenWidth < 380}
                  minimumFontScale={0.8}
                >
                  {t("changePassword.title")}
                </Text>
              </View>
            </View>
            <View style={[styles.changePasswordButton, { backgroundColor: `${primaryColor}15` }]}>
              <Text
                style={[styles.changePasswordButtonText, { color: primaryColor }]}
                numberOfLines={1}
                adjustsFontSizeToFit={screenWidth < 380}
                minimumFontScale={0.7}
              >
                {t("changePassword.changePassword")}
              </Text>
              <ChevronRight
                size={screenWidth < 380 ? 14 : 16}
                color={primaryColor}
                style={{ marginLeft: isRTL ? 0 : 4, marginRight: isRTL ? 4 : 0 }}
              />
            </View>
          </TouchableOpacity>
        </View>

        {/* Logout Button */}
        <TouchableOpacity 
          style={[styles.logoutButton, { backgroundColor: '#ef444415' }]}
          onPress={handleLogout}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#ef4444" />
          ) : (
            <>
              <LogOut size={20} color="#ef4444" />
              <Text style={[styles.logoutButtonText, { color: '#ef4444', marginLeft: isRTL ? 0 : 8, marginRight: isRTL ? 8 : 0 }]}>
                {t("common.logout")}
              </Text>
            </>
          )}
        </TouchableOpacity>

        {/* Version */}
        <Text style={[styles.versionText, { color: secondaryTextColor, textAlign: 'center' }]}>
          {t("common.version")} {Constants.expoConfig?.version || "1.0.0"}
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: getResponsivePadding(),
    paddingTop: screenWidth < 380 ? 16 : 20,
    paddingBottom: screenWidth < 380 ? 12 : 16,
  },
  headerRTL: {
    alignItems: 'flex-end',
  },
  title: {
    fontSize: getResponsiveFontSize(32),
    fontWeight: "700",
    letterSpacing: -0.5,
  },
  profileCard: {
    marginHorizontal: getResponsiveMargin(),
    borderRadius: screenWidth < 380 ? 16 : 20,
    padding: getResponsivePadding(),
    alignItems: "center",
    marginBottom: getResponsiveMargin(),
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  profileImageContainer: {
    position: "relative",
    marginBottom: screenWidth < 380 ? 16 : 20,
  },
  profileImage: {
    width: getResponsiveImageSize(),
    height: getResponsiveImageSize(),
    borderRadius: getResponsiveImageSize() / 2,
  },
  uploadingContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  profileImagePlaceholder: {
    width: getResponsiveImageSize(),
    height: getResponsiveImageSize(),
    borderRadius: getResponsiveImageSize() / 2,
    justifyContent: "center",
    alignItems: "center",
  },
  profileImagePlaceholderText: {
    fontSize: getResponsiveFontSize(36),
    fontWeight: "700",
    color: "#fff",
  },
  cameraButton: {
    position: "absolute",
    bottom: 2,
    right: 2,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 3,
    borderColor: "#fff",
  },
  profileInfo: {
    alignItems: "center",
  },
  userName: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: "700",
    marginBottom: 8,
  },
  nameInput: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: "700",
    marginBottom: 8,
    borderWidth: 1,
    borderRadius: 8,
    padding: 8,
    minWidth: screenWidth < 380 ? 150 : 200,
  },
  roleBadge: {
    paddingHorizontal: screenWidth < 380 ? 12 : 16,
    paddingVertical: 6,
    borderRadius: 20,
    marginBottom: 8,
  },
  roleText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: "600",
  },
  userEmail: {
    fontSize: getResponsiveFontSize(16),
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: getResponsivePadding(),
    marginBottom: getResponsiveMargin(),
    gap: screenWidth < 380 ? 6 : 8,
  },
  statCard: {
    // Always show 3 cards per row for all screen sizes
    width: '31%',
    alignItems: 'center',
    padding: screenWidth < 380 ? 12 : screenWidth < 420 ? 14 : 16,
    borderRadius: screenWidth < 380 ? 10 : 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    // Ensure minimum width for 3 cards per row
    minWidth: (screenWidth / 3) - 16,
  },
  statIcon: {
    width: screenWidth < 380 ? 32 : 36,
    height: screenWidth < 380 ? 32 : 36,
    borderRadius: screenWidth < 380 ? 16 : 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: screenWidth < 380 ? 8 : 10,
  },
  statNumber: {
    fontSize: screenWidth < 380 ? 16 : screenWidth < 420 ? 18 : 20,
    fontWeight: '700',
    marginBottom: 4,
    textAlign: 'center',
  },
  statLabel: {
    fontSize: screenWidth < 380 ? 10 : 11,
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: screenWidth < 380 ? 12 : 14,
  },
  infoCard: {
    marginHorizontal: 24,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  sectionHeaderRTL: {
    flexDirection: "row-reverse",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  editButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    gap: 4,
  },
  editButtonText: {
    fontWeight: "600",
    fontSize: 14,
  },
  editActions: {
    flexDirection: "row",
    gap: 8,
  },
  editActionsRTL: {
    flexDirection: "row-reverse",
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  actionButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 14,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  infoRowRTL: {
    flexDirection: 'row-reverse',
  },
  bioRow: {
    alignItems: 'flex-start',
  },
  infoRowLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  infoRowLeftRTL: {
    flexDirection: 'row-reverse',
  },
  infoLabel: {
    fontSize: 15,
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 15,
    fontWeight: '600',
    flex: 1,
  },
  infoInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 8,
    fontSize: 15,
    fontWeight: '600',
  },
  bioInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 8,
    fontSize: 15,
    fontWeight: '600',
    height: 80,
  },
  settingsCard: {
    marginHorizontal: getResponsiveMargin(),
    borderRadius: screenWidth < 380 ? 12 : 16,
    padding: getResponsivePadding(),
    marginBottom: getResponsiveMargin(),
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  settingItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
  },
  settingItemRTL: {
    flexDirection: "row-reverse",
  },
  changePasswordItem: {
    paddingVertical: screenWidth < 380 ? 14 : 16,
    paddingHorizontal: screenWidth < 380 ? 12 : 16,
    borderRadius: 12,
    marginVertical: 4,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  settingLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    minWidth: 0, // Allow text to shrink
  },
  settingLeftRTL: {
    flexDirection: "row-reverse",
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  languageButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
  },
  languageButtonText: {
    fontWeight: "600",
    fontSize: 14,
  },
  changePasswordIcon: {
    width: screenWidth < 380 ? 36 : 40,
    height: screenWidth < 380 ? 36 : 40,
    borderRadius: screenWidth < 380 ? 18 : 20,
    justifyContent: "center",
    alignItems: "center",
    flexShrink: 0,
  },
  changePasswordContent: {
    flex: 1,
    marginHorizontal: screenWidth < 380 ? 10 : 12,
    minWidth: 0, // Allow text to shrink
  },
  changePasswordTitle: {
    fontSize: screenWidth < 380 ? 14 : 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  changePasswordSubtitle: {
    fontSize: 13,
    lineHeight: 18,
  },
  changePasswordButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: screenWidth < 380 ? 8 : 12,
    paddingVertical: screenWidth < 380 ? 6 : 8,
    borderRadius: 8,
    flexShrink: 0,
    maxWidth: screenWidth < 380 ? 100 : 140,
  },
  changePasswordButtonText: {
    fontWeight: "600",
    fontSize: screenWidth < 380 ? 12 : 14,
    flexShrink: 1,
  },
  logoutButton: {
    marginHorizontal: 24,
    borderRadius: 16,
    padding: 18,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 16,
  },
  logoutButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
  versionText: {
    fontSize: 14,
    marginBottom: 32,
    fontWeight: '500',
  },
});
import React, { useState, useEffect, useCallback } from "react";
import { StyleSheet, Text, View, ScrollView, RefreshControl, TouchableOpacity, Dimensions } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter } from "expo-router";
import { getDashboardStats, subscribeToActivities, Activity } from "@/services/inventory-service";
import { getMachineryByFarm } from "@/services/machinery-service";
import { getUsersByFarm } from "@/services/user-service";
import { getFarmInventoryStats } from "@/services/farm-service";
import { getUserRequests } from "@/services/request-service";
import { getNotesByFarm } from "@/services/notes-service";
import ActivityItem from "@/components/ActivityItem";
import { DashboardCard } from "@/components/DashboardCard";
import FarmSelector from "@/components/FarmSelector";
import { useFarm } from "@/context/farm-context";
import { useAuth } from "@/context/auth-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { Warehouse, Users, ClipboardList, AlertTriangle, FileText, Truck, Wrench, Heart, Bell, MapPin, Calendar, Bot } from "lucide-react-native";
import { useFocusEffect } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import AsyncStorage from "@react-native-async-storage/async-storage";

const { width: screenWidth } = Dimensions.get('window');

// Helper function for responsive icon size
const getResponsiveIconSize = () => {
  if (screenWidth < 380) return 18;
  if (screenWidth < 420) return 20;
  return 24;
};

export default function CaretakerDashboardScreen() {
  const router = useRouter();
  const { selectedFarm } = useFarm();
  const { user } = useAuth();
  const { colors } = useTheme();
  const { t } = useLanguage();
  const iconSize = getResponsiveIconSize();

  const [stats, setStats] = useState({
    totalItems: 0,
    lowStockCount: 0,
    expiryAlerts: 0,
    inventoryRequests: 0,
    machineryRequests: 0,
    caretakers: 0,
    notes: 0,
    totalMachinery: 0,
    activeMachinery: 0,
    maintenanceDue: 0,
    fuelRequests: 0,
  });
  const [recentActivity, setRecentActivity] = useState<Activity[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [alerts, setAlerts] = useState<any[]>([]);
  const [lastFarmId, setLastFarmId] = useState<string | null>(null);
  const [lastLoadTime, setLastLoadTime] = useState<number>(0);
  
  // Clear cache for fresh data
  const clearDashboardCache = async (farmId: string) => {
    try {
      const keysToRemove = [
        `inventory_${farmId}`,
        `activities_${farmId}`,
        `user_requests_${farmId}_${user?.uid}_all_all`,
        `user_requests_${farmId}_${user?.uid}_pending_all`,
        `users_cache`,
        `userFarms_${user?.uid}`,
      ];
      await AsyncStorage.multiRemove(keysToRemove);
      console.log("Dashboard cache cleared for fresh data");
    } catch (error) {
      console.error("Error clearing dashboard cache:", error);
    }
  };
  
  const loadDashboardData = useCallback(async (forceRefresh = false) => {
    if (!selectedFarm || !user) {
      setLoading(false);
      return;
    }

    // Prevent multiple simultaneous calls
    if (loading && !forceRefresh) {
      console.log("Caretaker Dashboard: Already loading, skipping duplicate call");
      return;
    }

    // If we have recent data and not forcing refresh, skip loading
    if (!forceRefresh && lastLoadTime > 0 && (Date.now() - lastLoadTime) < 2 * 60 * 1000) {
      console.log("Caretaker Dashboard: Data is recent (less than 2 minutes), skipping load");
      setLoading(false);
      return;
    }

    // Clear cache for fresh data if force refresh or farm changed
    if (forceRefresh || lastFarmId !== selectedFarm.id) {
      console.log("Caretaker Dashboard: Clearing cache", { forceRefresh, farmChanged: lastFarmId !== selectedFarm.id });
      await clearDashboardCache(selectedFarm.id);
      setLastFarmId(selectedFarm.id);
    } else {
      console.log("Caretaker Dashboard: Using cached data");
    }
    
    console.log("Caretaker Dashboard: Loading data for farm:", selectedFarm.name);
    setLoading(true);
    setLastLoadTime(Date.now());
    
    try {
      // Fetch all data in parallel for better performance
      // For caretakers, filter requests by their user ID
      const [
        caretakers,
        inventoryStats,
        inventoryRequests,
        machineryRequests,
        allMachineryRequests,
        notes,
        machinery,
        activities
      ] = await Promise.all([
        getUsersByFarm(selectedFarm.id),
        getFarmInventoryStats(selectedFarm.id),
        getUserRequests(selectedFarm.id, user.uid, "pending", "inventory"), // Caretaker's own pending inventory requests
        getUserRequests(selectedFarm.id, user.uid, "pending", "machinery"), // Caretaker's own pending machinery requests
        getUserRequests(selectedFarm.id, user.uid, undefined, "machinery"), // All caretaker's machinery requests
        getNotesByFarm(selectedFarm.id),
        getMachineryByFarm(selectedFarm.id),
        getDashboardStats("caretaker", selectedFarm.id)
      ]);
      
      // Calculate machinery stats
      const totalMachinery = machinery.length;
      const activeMachinery = machinery.filter(m => m.status === 'working').length;
      
      // Calculate maintenance due
      const today = new Date();
      const maintenanceDue = machinery.filter(m => {
        if (!m.nextMaintenanceDate) return false;
        const maintenanceDate = new Date(m.nextMaintenanceDate);
        return maintenanceDate <= today;
      }).length;
      
      // Get fuel requests count (caretaker's machinery requests with fuel sub-type)
      const fuelRequests = allMachineryRequests.filter(req => req.requestSubType === 'fuel').length;
      
      // Prepare alerts based on real data
      const alertsData = [];
      
      if (inventoryStats.lowStockCount > 0) {
        alertsData.push({
          type: 'warning',
          title: t("dashboard.lowStockAlert"),
          message: t("dashboard.lowStockMessage", { count: inventoryStats.lowStockCount }),
          icon: AlertTriangle,
          action: () => router.push("/(app)/(caretaker)/inventory")
        });
      }
      
      if (inventoryStats.expiryAlerts > 0) {
        alertsData.push({
          type: 'error',
          title: t("dashboard.expiryAlert"),
          message: t("dashboard.expiryMessage", { count: inventoryStats.expiryAlerts }),
          icon: Calendar,
          action: () => router.push("/(app)/(caretaker)/inventory")
        });
      }
      
      if (maintenanceDue > 0) {
        alertsData.push({
          type: 'warning',
          title: t("dashboard.maintenanceAlert"),
          message: t("dashboard.maintenanceMessage", { count: maintenanceDue }),
          icon: Wrench,
          action: () => router.push("/(app)/(caretaker)/machinery")
        });
      }
      
      if (inventoryRequests.length > 0) {
        alertsData.push({
          type: 'info',
          title: t("dashboard.myPendingRequests"),
          message: t("dashboard.myPendingRequestsMessage", { count: inventoryRequests.length }),
          icon: Bell,
          action: () => router.push("/(app)/(caretaker)/requests")
        });
      }
      
      // Set all stats with real data
      setStats({
        totalItems: inventoryStats.totalItems || 0,
        lowStockCount: inventoryStats.lowStockCount || 0,
        expiryAlerts: inventoryStats.expiryAlerts || 0,
        inventoryRequests: inventoryRequests.length,
        machineryRequests: machineryRequests.length,
        caretakers: caretakers.length,
        notes: notes.length,
        totalMachinery,
        activeMachinery,
        maintenanceDue,
        fuelRequests,
      });
      
      setAlerts(alertsData);
      setRecentActivity(activities.recentActivity || []);
      
      console.log("Caretaker Dashboard: Fresh data loaded successfully", {
        totalItems: inventoryStats.totalItems,
        lowStockCount: inventoryStats.lowStockCount,
        inventoryRequests: inventoryRequests.length,
        machineryRequests: machineryRequests.length,
        caretakers: caretakers.length
      });
    } catch (error) {
      console.error("Error loading dashboard data:", error);
      // Reset to zero values on error to show accurate state
      setStats({
        totalItems: 0,
        lowStockCount: 0,
        expiryAlerts: 0,
        inventoryRequests: 0,
        machineryRequests: 0,
        caretakers: 0,
        notes: 0,
        totalMachinery: 0,
        activeMachinery: 0,
        maintenanceDue: 0,
        fuelRequests: 0,
      });
      setAlerts([]);
      setRecentActivity([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [selectedFarm, user, t, router, lastFarmId]);
  
  // Load data when component mounts or farm changes
  useEffect(() => {
    if (selectedFarm && user) {
      // Only force refresh if farm actually changed
      const farmChanged = lastFarmId !== selectedFarm.id;
      console.log("Caretaker Dashboard: useEffect triggered", { farmChanged, farmId: selectedFarm.id, lastFarmId });

      if (farmChanged) {
        loadDashboardData(true); // Force refresh on farm change
      } else {
        loadDashboardData(false); // Use cache if same farm
      }
    }
  }, [selectedFarm?.id, user?.uid]);
  
  // Only load data when screen comes into focus if we don't have data yet
  useFocusEffect(
    useCallback(() => {
      if (selectedFarm && user && !loading) {
        // Check if we have data and if it's recent (less than 5 minutes old)
        const hasData = stats.inventoryRequests > 0 || stats.machineryRequests > 0 || recentActivity.length > 0;
        const isDataFresh = lastLoadTime > 0 && (Date.now() - lastLoadTime) < 5 * 60 * 1000; // 5 minutes

        if (!hasData && !isDataFresh) {
          console.log("Caretaker Dashboard: No fresh data found, loading...");
          loadDashboardData(false); // Don't force refresh, use cache if available
        } else {
          console.log("Caretaker Dashboard: Fresh data available, skipping refresh");
        }
      }
    }, [selectedFarm?.id, user?.uid, loading, stats.inventoryRequests, stats.machineryRequests, recentActivity.length, lastLoadTime])
  );
  
  // Set up real-time listener for activities
  useEffect(() => {
    if (selectedFarm) {
      const unsubscribe = subscribeToActivities(3, selectedFarm.id, (updatedActivities) => {
        setRecentActivity(updatedActivities);
      });
      
      return () => {
        unsubscribe();
      };
    }
  }, [selectedFarm?.id]);
  
  // Pull to refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData(true);
  };
  
  const quickActions = [
    {
      title: t("dashboard.addNote"),
      icon: FileText,
      color: colors.success,
      action: () => router.push("/(app)/notes/create")
    },
    {
      title: t("dashboard.viewRequests"),
      icon: ClipboardList,
      color: colors.warning,
      action: () => router.push("/(app)/(caretaker)/requests")
    },
    {
      title: t("farm.assignedFarms"),
      icon: MapPin,
      color: colors.info,
      action: () => router.push("/(app)/(caretaker)/farms")
    },
    {
      title: t("allocations.myAllocations"),
      icon: Users,
      color: colors.info,
      action: () => router.push(`/allocations/user/${user?.uid}?name=${encodeURIComponent(user?.displayName || 'My Allocations')}&role=${user?.role}`)
    }
  ];
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t("dashboard.title"),
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: "#fff",
        }}
      />
      
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.farmSelectorContainer}>
          <FarmSelector isLoading={loading} />
        </View>
        
        {selectedFarm && (
          <View style={styles.welcomeContainer}>
            <LinearGradient
              colors={[colors.primary, colors.primaryDark]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.welcomeCard}
            >
              <View style={styles.welcomeContent}>
                <View style={styles.welcomeTextContainer}>
                  <Text style={styles.welcomeTitle}>{t("dashboard.welcome")}, {user?.displayName || "Caretaker"}</Text>
                  <Text style={styles.welcomeSubtitle}>
                    {selectedFarm.name} • {new Date().toLocaleDateString()}
                  </Text>
                </View>
                <View style={styles.welcomeIconContainer}>
                  <Heart size={24} color="rgba(255, 255, 255, 0.8)" />
                </View>
              </View>
            </LinearGradient>
          </View>
        )}
        
        <View style={styles.statsContainer}>
          <DashboardCard
            title={t("dashboard.totalInventory")}
            value={loading ? "..." : stats.totalItems.toString()}
            icon={<Warehouse size={iconSize} color={colors.success} />}
            backgroundColor={colors.surface}
            textColor={colors.text}
            secondaryTextColor={colors.textSecondary}
            borderColor={colors.border}
            gradientColors={[colors.success + '15', colors.success + '25']}
            onPress={() => router.push("/(app)/(caretaker)/inventory")}
          />
          
          <DashboardCard
            title={t("dashboard.activeMachinery")}
            value={loading ? "..." : stats.activeMachinery.toString()}
            icon={<Truck size={iconSize} color={colors.info} />}
            backgroundColor={colors.surface}
            textColor={colors.text}
            secondaryTextColor={colors.textSecondary}
            borderColor={colors.border}
            gradientColors={[colors.info + '15', colors.info + '25']}
            onPress={() => router.push("/(app)/(caretaker)/machinery")}
          />
          
          <DashboardCard
            title={t("dashboard.lowStockItems")}
            value={loading ? "..." : stats.lowStockCount.toString()}
            icon={<AlertTriangle size={iconSize} color={colors.error} />}
            backgroundColor={colors.surface}
            textColor={colors.text}
            secondaryTextColor={colors.textSecondary}
            borderColor={colors.border}
            gradientColors={[colors.error + '15', colors.error + '25']}
            onPress={() => router.push("/(app)/(caretaker)/inventory")}
          />
          
          <DashboardCard
            title={t("dashboard.myInventoryRequests")}
            value={loading ? "..." : stats.inventoryRequests.toString()}
            icon={<ClipboardList size={iconSize} color={colors.primary} />}
            backgroundColor={colors.surface}
            textColor={colors.text}
            secondaryTextColor={colors.textSecondary}
            borderColor={colors.border}
            gradientColors={[colors.primary + '15', colors.primary + '25']}
            onPress={() => router.push("/(app)/(caretaker)/requests")}
          />
          
          <DashboardCard
            title={t("dashboard.myMachineryRequests")}
            value={loading ? "..." : stats.machineryRequests.toString()}
            icon={<Truck size={iconSize} color={colors.info} />}
            backgroundColor={colors.surface}
            textColor={colors.text}
            secondaryTextColor={colors.textSecondary}
            borderColor={colors.border}
            gradientColors={[colors.info + '15', colors.info + '25']}
            onPress={() => router.push("/(app)/(caretaker)/requests")}
          />
          
          <DashboardCard
            title={t("dashboard.caretakers")}
            value={loading ? "..." : stats.caretakers.toString()}
            icon={<Users size={iconSize} color={colors.textSecondary} />}
            backgroundColor={colors.surface}
            textColor={colors.text}
            secondaryTextColor={colors.textSecondary}
            borderColor={colors.border}
            gradientColors={[colors.textSecondary + '15', colors.textSecondary + '25']}
            onPress={() => {}}
          />
        </View>
        
        {/* Alerts & Notifications Section */}
        {alerts.length > 0 && (
          <>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>{t("dashboard.alertsNotifications")}</Text>
            </View>
            
            <View style={styles.alertsContainer}>
              {alerts.map((alert, index) => (
                <TouchableOpacity
                  key={index}
                  style={[styles.alertCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
                  onPress={alert.action}
                  activeOpacity={0.7}
                >
                  <View style={[styles.alertIconContainer, { 
                    backgroundColor: alert.type === 'error' ? colors.error + '20' : 
                                   alert.type === 'warning' ? colors.warning + '20' : 
                                   colors.info + '20' 
                  }]}>
                    <alert.icon 
                      size={20} 
                      color={alert.type === 'error' ? colors.error : 
                             alert.type === 'warning' ? colors.warning : 
                             colors.info} 
                    />
                  </View>
                  <View style={styles.alertContent}>
                    <Text style={[styles.alertTitle, { color: colors.text }]}>{alert.title}</Text>
                    <Text style={[styles.alertMessage, { color: colors.textSecondary }]}>{alert.message}</Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </>
        )}
        
        {/* Quick Actions Section */}
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>{t("dashboard.quickActions")}</Text>
        </View>
        
        <View style={styles.quickActionsContainer}>
          {quickActions.map((action, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.quickActionCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
              onPress={action.action}
              activeOpacity={0.7}
            >
              <View style={[styles.quickActionIconContainer, { backgroundColor: action.color + '20' }]}>
                <action.icon size={24} color={action.color} />
              </View>
              <Text style={[styles.quickActionTitle, { color: colors.text }]}>{action.title}</Text>
            </TouchableOpacity>
          ))}
        </View>
        
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>{t("dashboard.recentActivity")}</Text>
        </View>
        
        <View style={styles.activityContainer}>
          {recentActivity.length > 0 ? (
            recentActivity.map((activity, index) => (
              <ActivityItem
                key={index}
                activity={activity}
                backgroundColor={colors.surface}
                textColor={colors.text}
                secondaryTextColor={colors.textSecondary}
                borderColor={colors.border}
                isDarkMode={colors.background === "#121212"}
              />
            ))
          ) : (
            <View style={[styles.emptyState, { backgroundColor: colors.surface, borderColor: colors.border }]}>
              <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
                {loading ? t("common.loading") : t("dashboard.noRecentActivity")}
              </Text>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Floating AI Assistant Button */}
      <TouchableOpacity
        style={[styles.floatingButton, { backgroundColor: colors.primary }]}
        onPress={() => router.push('/(app)/ai-assistant')}
        activeOpacity={0.8}
        accessibilityLabel={t('chat.aiAssistant')}
        accessibilityHint={t('chat.openAiAssistant')}
      >
        <LinearGradient
          colors={[colors.primary, colors.primaryDark]}
          style={styles.floatingButtonGradient}
        >
          <Bot size={24} color="#fff" />
        </LinearGradient>
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  farmSelectorContainer: {
    margin: 16,
    zIndex: 1000,
  },
  welcomeContainer: {
    paddingHorizontal: screenWidth < 380 ? 12 : 16,
    marginBottom: screenWidth < 380 ? 16 : 20,
  },
  welcomeCard: {
    borderRadius: screenWidth < 380 ? 16 : 20,
    padding: screenWidth < 380 ? 16 : 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  welcomeContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  welcomeTextContainer: {
    flex: 1,
    minWidth: 0, // Allows text to shrink
  },
  welcomeTitle: {
    fontSize: screenWidth < 380 ? 18 : 20,
    fontWeight: "700",
    color: "#fff",
    marginBottom: screenWidth < 380 ? 4 : 6,
    lineHeight: screenWidth < 380 ? 22 : 26,
  },
  welcomeSubtitle: {
    fontSize: screenWidth < 380 ? 13 : 15,
    color: "rgba(255, 255, 255, 0.85)",
    fontWeight: "500",
    lineHeight: screenWidth < 380 ? 17 : 20,
  },
  welcomeIconContainer: {
    width: screenWidth < 380 ? 40 : 48,
    height: screenWidth < 380 ? 40 : 48,
    borderRadius: screenWidth < 380 ? 20 : 24,
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    justifyContent: "center",
    alignItems: "center",
    marginLeft: screenWidth < 380 ? 8 : 12,
  },
  statsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    marginBottom: 24,
    gap: screenWidth < 380 ? 8 : 12,
    alignItems: "flex-start",
  },
  sectionHeader: {
    paddingHorizontal: screenWidth < 380 ? 12 : 16,
    marginTop: screenWidth < 380 ? 6 : 8,
    marginBottom: screenWidth < 380 ? 12 : 16,
  },
  sectionTitle: {
    fontSize: screenWidth < 380 ? 18 : 20,
    fontWeight: "700",
    letterSpacing: 0.5,
    lineHeight: screenWidth < 380 ? 22 : 26,
  },
  alertsContainer: {
    paddingHorizontal: screenWidth < 380 ? 12 : 16,
    marginBottom: screenWidth < 380 ? 20 : 24,
  },
  alertCard: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: screenWidth < 380 ? 12 : 16,
    padding: screenWidth < 380 ? 12 : 16,
    marginBottom: screenWidth < 380 ? 10 : 12,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  alertIconContainer: {
    width: screenWidth < 380 ? 36 : 44,
    height: screenWidth < 380 ? 36 : 44,
    borderRadius: screenWidth < 380 ? 18 : 22,
    justifyContent: "center",
    alignItems: "center",
    marginRight: screenWidth < 380 ? 12 : 16,
  },
  alertContent: {
    flex: 1,
    minWidth: 0, // Allows text to shrink
  },
  alertTitle: {
    fontSize: screenWidth < 380 ? 14 : 16,
    fontWeight: "600",
    marginBottom: screenWidth < 380 ? 3 : 4,
    lineHeight: screenWidth < 380 ? 18 : 20,
  },
  alertMessage: {
    fontSize: screenWidth < 380 ? 12 : 14,
    lineHeight: screenWidth < 380 ? 16 : 20,
  },
  quickActionsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    paddingHorizontal: screenWidth < 380 ? 12 : 16,
    marginBottom: screenWidth < 380 ? 20 : 24,
    gap: screenWidth < 380 ? 8 : 12,
    alignItems: "flex-start",
  },
  quickActionCard: {
    // Always show 2 cards per row for all screen sizes
    width: "48%",
    borderRadius: screenWidth < 380 ? 12 : 16,
    padding: screenWidth < 380 ? 16 : 20,
    alignItems: "center",
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
    // Ensure minimum width is appropriate for 2 cards per row
    minWidth: (screenWidth / 2) - 24,
    marginBottom: screenWidth < 380 ? 8 : 12,
  },
  quickActionIconContainer: {
    width: screenWidth < 380 ? 48 : 56,
    height: screenWidth < 380 ? 48 : 56,
    borderRadius: screenWidth < 380 ? 24 : 28,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: screenWidth < 380 ? 8 : 12,
  },
  quickActionTitle: {
    fontSize: screenWidth < 380 ? 12 : 14,
    fontWeight: "600",
    textAlign: "center",
    lineHeight: screenWidth < 380 ? 16 : 18,
  },
  activityContainer: {
    paddingHorizontal: screenWidth < 380 ? 12 : 16,
  },
  emptyState: {
    borderRadius: screenWidth < 380 ? 12 : 16,
    padding: screenWidth < 380 ? 24 : 32,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
  },
  emptyStateText: {
    fontSize: screenWidth < 380 ? 14 : 16,
    textAlign: "center",
    lineHeight: screenWidth < 380 ? 18 : 20,
  },
  floatingButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    zIndex: 1000,
  },
  floatingButtonGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
} from "react-native";
import { useRouter, useLocalSearchParams, Stack } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { useAuth } from "@/context/auth-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { useTheme } from "@/context/theme-context";
import {
  getAllocationsByUser,
  getReturnDataForAllocations,
  Allocation,
  ReturnRequest
} from "@/services/allocation-service";
import {
  Package,
  Truck,
  Clock,
  CheckCircle,
  AlertTriangle,
  ArrowUpDown,
  ArrowLeft,
  RotateCcw
} from "lucide-react-native";

export default function UserAllocationDetailScreen() {
  const router = useRouter();
  const { id, name, role } = useLocalSearchParams();
  const { user } = useAuth();
  const { selectedFarm } = useFarm();
  const { colors, theme } = useTheme();
  const { t } = useLanguage();

  const [allocations, setAllocations] = useState<Allocation[]>([]);
  const [returnData, setReturnData] = useState<Map<string, ReturnRequest[]>>(new Map());
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'inventory' | 'machinery'>('inventory');
  const [inventoryFilter, setInventoryFilter] = useState<'all' | 'active' | 'returned'>('all');
  const [machineryFilter, setMachineryFilter] = useState<'all' | 'active' | 'returned'>('all');

  const userName = Array.isArray(name) ? name[0] : name;
  const userRole = Array.isArray(role) ? role[0] : role;
  const userId = Array.isArray(id) ? id[0] : id;

  useEffect(() => {
    if (selectedFarm && userId) {
      loadUserAllocations();
    }
  }, [selectedFarm, userId]);

  const loadUserAllocations = async () => {
    if (!selectedFarm || !userId) return;

    try {
      setLoading(true);

      // Load allocations
      const userAllocations = await getAllocationsByUser(selectedFarm.id, userId);
      setAllocations(userAllocations);

      // Load return data for these allocations
      if (userAllocations.length > 0) {
        const allocationIds = userAllocations.map(a => a.id);
        const returnDataMap = await getReturnDataForAllocations(selectedFarm.id, allocationIds);
        setReturnData(returnDataMap);
      } else {
        setReturnData(new Map());
      }
    } catch (error) {
      console.error('Error loading user allocations:', error);
      Alert.alert(t('common.error'), t('allocations.loadError'));
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadUserAllocations();
    setRefreshing(false);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'allocated': return <Clock size={16} color={colors.primary} />;
      case 'partially_returned': return <ArrowUpDown size={16} color={colors.warning} />;
      case 'returned': return <CheckCircle size={16} color={colors.success} />;
      case 'overdue': return <AlertTriangle size={16} color={colors.error} />;
      case 'damaged': return <AlertTriangle size={16} color={colors.warning} />;
      case 'lost': return <AlertTriangle size={16} color={colors.error} />;
      default: return <Clock size={16} color={colors.textSecondary} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'allocated': return colors.primary;
      case 'partially_returned': return colors.warning;
      case 'returned': return colors.success;
      case 'overdue': return colors.error;
      case 'damaged': return colors.warning;
      case 'lost': return colors.error;
      default: return colors.textSecondary;
    }
  };

  const getTypeIcon = (type: string) => {
    return type === 'inventory' ?
      <Package size={20} color={colors.primary} /> :
      <Truck size={20} color={colors.secondary} />;
  };

  const handleReturnItem = (allocation: Allocation) => {
    // Navigate to return request screen
    router.push(`/requests/return?allocationId=${allocation.id}&itemName=${encodeURIComponent(allocation.itemName)}&type=${allocation.type}`);
  };

  const handleAllocationCardPress = (allocation: Allocation) => {
    const returnInfo = getReturnInfo(allocation.id);
    const status = getReturnStatus(allocation);

    // If the allocation has been returned, navigate to return details screen
    if ((status === 'returned' || status === 'partially_returned') && returnInfo.latestReturn) {
      if (allocation.type === 'machinery') {
        // Navigate to machinery return details screen
        router.push(`/machinery/return/${returnInfo.latestReturn.id}`);
      } else {
        // Navigate to inventory return details screen
        router.push(`/return/${returnInfo.latestReturn.id}`);
      }
    } else {
      // For non-returned items, navigate to request details screen
      if (allocation.requestId) {
        // Navigate to request details screen using the requestId
        router.push(`/request/${allocation.requestId}?farmId=${selectedFarm?.id}`);
      } else {
        console.log('No request ID available for this allocation');
      }
    }
  };

  // Helper functions for return data
  const getReturnInfo = (allocationId: string) => {
    const returns = returnData.get(allocationId) || [];
    const totalReturned = returns.reduce((sum, ret) => sum + ret.returnQuantity, 0);
    const latestReturn = returns.length > 0 ? returns[returns.length - 1] : null;

    return {
      totalReturned,
      latestReturn,
      hasReturns: returns.length > 0,
      returns
    };
  };

  const getReturnStatus = (allocation: Allocation) => {
    const returnInfo = getReturnInfo(allocation.id);

    // First check the allocation's own status (updated by return approval)
    if (allocation.status === 'returned') {
      return 'returned';
    } else if (allocation.status === 'partially_returned') {
      return 'partially_returned';
    }

    if (allocation.type === 'machinery') {
      // Machinery return is mandatory
      // Check both allocation status and return data
      return (allocation.status === 'returned' || returnInfo.hasReturns) ? 'returned' : 'in_use';
    } else {
      // Inventory - check allocation status first, then return data
      if (allocation.status === 'returned') {
        return 'returned';
      } else if (allocation.status === 'partially_returned') {
        return 'partially_returned';
      } else {
        // Fallback to return data calculation
        const allocatedQty = allocation.quantity || 0;
        const returnedQty = returnInfo.totalReturned;

        if (returnedQty >= allocatedQty) {
          return 'returned';
        } else if (returnedQty > 0) {
          return 'partially_returned';
        } else {
          // Check if fully consumed (no return expected)
          return allocatedQty > 0 ? 'consumed' : 'allocated';
        }
      }
    }
  };

  const shouldShowReturnBadge = (allocation: Allocation) => {
    const returnInfo = getReturnInfo(allocation.id);
    const status = getReturnStatus(allocation);

    // Always show return badge if allocation status indicates return
    if (allocation.status === 'returned' || allocation.status === 'partially_returned') {
      return true;
    }

    // Always show return badge if there are returns from return module
    if (returnInfo.hasReturns) {
      return true;
    }

    // For machinery, show "In Use" if not returned
    if (allocation.type === 'machinery' && status === 'in_use') {
      return true;
    }

    // For inventory, don't show badge if fully consumed
    return false;
  };

  // Filter allocations based on selected filter
  const getFilteredAllocations = (type: 'inventory' | 'machinery') => {
    const typeAllocations = allocations.filter(a => a.type === type);
    const filter = type === 'inventory' ? inventoryFilter : machineryFilter;

    if (filter === 'all') {
      return typeAllocations;
    }

    return typeAllocations.filter(allocation => {
      const status = getReturnStatus(allocation);

      if (filter === 'returned') {
        return status === 'returned' || status === 'partially_returned';
      } else if (filter === 'active') {
        return status !== 'returned' && status !== 'partially_returned';
      }

      return true;
    });
  };

  const inventoryAllocations = allocations.filter(a => a.type === 'inventory');
  const machineryAllocations = allocations.filter(a => a.type === 'machinery');

  if (!selectedFarm) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <Stack.Screen options={{
          title: t('allocations.userAllocations'),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { color: 'white', fontWeight: '600' },
        }} />
        <View style={styles.centerContainer}>
          <Text style={[styles.message, { color: colors.text }]}>
            {t('farm.selectFarmFirst')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: userName || t('allocations.userAllocations'),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { color: 'white', fontWeight: '600' },
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()}>
              <ArrowLeft size={24} color="white" />
            </TouchableOpacity>
          ),
        }}
      />

      {/* User Info Header */}
      <View style={[styles.userHeader, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <Text style={[styles.userName, { color: colors.text }]}>
          {userName}
        </Text>
        <Text style={[styles.userRole, { color: colors.textSecondary }]}>
          {t(`common.${userRole}`)} • {allocations.length} {t('allocations.allocations')}
        </Text>
      </View>

      {/* Tab Navigation */}
      <View style={[styles.tabContainer, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'inventory' && { borderBottomColor: colors.primary }
          ]}
          onPress={() => setActiveTab('inventory')}
        >
          <Package size={20} color={activeTab === 'inventory' ? colors.primary : colors.textSecondary} />
          <Text
            style={[
              styles.tabText,
              { color: activeTab === 'inventory' ? colors.primary : colors.textSecondary }
            ]}
          >
            {t('allocations.inventory')} ({getFilteredAllocations('inventory').length})
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'machinery' && { borderBottomColor: colors.primary }
          ]}
          onPress={() => setActiveTab('machinery')}
        >
          <Truck size={20} color={activeTab === 'machinery' ? colors.primary : colors.textSecondary} />
          <Text
            style={[
              styles.tabText,
              { color: activeTab === 'machinery' ? colors.primary : colors.textSecondary }
            ]}
          >
            {t('allocations.machinery')} ({getFilteredAllocations('machinery').length})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Filter Section */}
      <View style={[styles.filterContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <Text style={[styles.filterLabel, { color: colors.textSecondary }]}>
          {t('allocations.filter')}:
        </Text>
        <View style={styles.filterButtons}>
          {(['all', 'active', 'returned'] as const).map((filter) => {
            const currentFilter = activeTab === 'inventory' ? inventoryFilter : machineryFilter;
            const isActive = currentFilter === filter;

            return (
              <TouchableOpacity
                key={filter}
                style={[
                  styles.filterButton,
                  {
                    backgroundColor: isActive ? colors.primary + '20' : 'transparent',
                    borderColor: isActive ? colors.primary : colors.border
                  }
                ]}
                onPress={() => {
                  if (activeTab === 'inventory') {
                    setInventoryFilter(filter);
                  } else {
                    setMachineryFilter(filter);
                  }
                }}
              >
                <Text style={[
                  styles.filterButtonText,
                  { color: isActive ? colors.primary : colors.textSecondary }
                ]}>
                  {t(`allocations.${filter}`)}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : (
          <View style={styles.content}>
            {activeTab === 'inventory' ? (
              getFilteredAllocations('inventory').length === 0 ? (
                <View style={styles.emptyContainer}>
                  <Package size={48} color={colors.textSecondary} />
                  <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                    {inventoryFilter === 'all'
                      ? t('allocations.noInventoryAllocations')
                      : t(`allocations.no${inventoryFilter.charAt(0).toUpperCase() + inventoryFilter.slice(1)}Inventory`)
                    }
                  </Text>
                </View>
              ) : (
                getFilteredAllocations('inventory').map((allocation) => (
                  <TouchableOpacity
                    key={allocation.id}
                    style={[styles.allocationCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
                    onPress={() => handleAllocationCardPress(allocation)}
                  >
                    <View style={styles.cardHeader}>
                      <View style={styles.cardLeft}>
                        <View style={[styles.typeIcon, { backgroundColor: colors.primary + '20' }]}>
                          <Package size={24} color={colors.primary} />
                        </View>
                        <View style={styles.cardInfo}>
                          <Text style={[styles.itemName, { color: colors.text }]}>
                            {allocation.itemName}
                          </Text>
                          <Text style={[styles.allocationDate, { color: colors.textSecondary }]}>
                            {t('allocations.allocatedOn')}: {formatDate(allocation.allocationDate)}
                          </Text>
                        </View>
                      </View>

                      {/* Return Badge with Condition */}
                      {(() => {
                        const returnInfo = getReturnInfo(allocation.id);
                        const status = getReturnStatus(allocation);

                        // Check allocation status first, then return data
                        const isReturned = status === 'returned' || status === 'partially_returned';
                        const isPartiallyReturned = status === 'partially_returned';

                        if (isReturned) {
                          // Get condition from allocation or return data
                          const condition = allocation.condition || returnInfo.latestReturn?.condition;

                          return (
                            <View style={styles.returnBadgeContainer}>
                              <View style={[styles.returnBadge, {
                                backgroundColor: isPartiallyReturned ? colors.warning + '20' : colors.success + '20'
                              }]}>
                                <Text style={[styles.returnBadgeText, {
                                  color: isPartiallyReturned ? colors.warning : colors.success
                                }]}>
                                  {isPartiallyReturned ? t('allocations.partiallyReturned') : t('allocations.returned')}
                                </Text>
                              </View>
                              {condition && (
                                <View style={[styles.conditionBadge, { backgroundColor: colors.textSecondary + '20' }]}>
                                  <Text style={[styles.conditionBadgeText, { color: colors.textSecondary }]}>
                                    {t(`allocations.${condition}`)}
                                  </Text>
                                </View>
                              )}
                            </View>
                          );
                        } else if (shouldShowReturnBadge(allocation)) {
                          return (
                            <View style={[styles.returnBadge, { backgroundColor: colors.primary + '20' }]}>
                              <Text style={[styles.returnBadgeText, { color: colors.primary }]}>
                                {t('allocations.allocated')}
                              </Text>
                            </View>
                          );
                        }
                        return null;
                      })()}
                    </View>

                    {allocation.quantity && (
                      <View style={styles.quantitySection}>
                        <View style={styles.quantityRow}>
                          <Text style={[styles.quantityLabel, { color: colors.textSecondary }]}>
                            {t('allocations.allocated')}:
                          </Text>
                          <Text style={[styles.quantityValue, { color: colors.text }]}>
                            {allocation.quantity} {allocation.unit || ''}
                          </Text>
                        </View>

                        {(() => {
                          const returnInfo = getReturnInfo(allocation.id);
                          const status = getReturnStatus(allocation);

                          // Show return information if returned or partially returned
                          if (status === 'returned' || status === 'partially_returned') {
                            // Use allocation data first, fallback to return data
                            const returnedQty = allocation.returnedQuantity || returnInfo.totalReturned;
                            const condition = allocation.condition || returnInfo.latestReturn?.condition;
                            const returnDate = allocation.actualReturnDate || returnInfo.latestReturn?.approvedAt;

                            return (
                              <>
                                <View style={styles.quantityRow}>
                                  <Text style={[styles.quantityLabel, { color: colors.textSecondary }]}>
                                    {t('allocations.returned')}:
                                  </Text>
                                  <Text style={[styles.quantityValue, { color: colors.success }]}>
                                    {returnedQty} {allocation.unit || ''}
                                  </Text>
                                </View>

                                {returnDate && (
                                  <Text style={[styles.returnDate, { color: colors.success }]}>
                                    {t('allocations.returnedOn')}: {formatDate(returnDate)}
                                  </Text>
                                )}

                                {status !== 'returned' && allocation.remainingQuantity && (
                                  <View style={styles.quantityRow}>
                                    <Text style={[styles.quantityLabel, { color: colors.textSecondary }]}>
                                      {t('allocations.remaining')}:
                                    </Text>
                                    <Text style={[styles.quantityValue, { color: colors.primary }]}>
                                      {allocation.remainingQuantity} {allocation.unit || ''}
                                    </Text>
                                  </View>
                                )}

                                {condition && (
                                  <View style={styles.conditionRow}>
                                    <Text style={[styles.conditionLabel, { color: colors.textSecondary }]}>
                                      {t('allocations.condition')}:
                                    </Text>
                                    <Text style={[styles.conditionValue, { color: colors.text }]}>
                                      {t(`allocations.${condition}`)}
                                    </Text>
                                  </View>
                                )}

                                {returnInfo.latestReturn?.returnNotes && (
                                  <Text style={[styles.returnNotes, { color: colors.textSecondary }]}>
                                    {t('allocations.notes')}: {returnInfo.latestReturn.returnNotes}
                                  </Text>
                                )}
                              </>
                            );
                          }
                          return null;
                        })()}
                      </View>
                    )}

                    {/* Return Button for Active Allocations */}
                    {(() => {
                      const returnInfo = getReturnInfo(allocation.id);
                      const status = getReturnStatus(allocation);

                      // Show return button only if not fully returned and not fully consumed
                      const canReturn = status !== 'returned' && status !== 'consumed';

                      if (canReturn) {
                        return (
                          <TouchableOpacity
                            style={[styles.returnButton, { backgroundColor: colors.warning }]}
                            onPress={() => handleReturnItem(allocation)}
                          >
                            <RotateCcw size={16} color="white" />
                            <Text style={styles.returnButtonText}>{t('allocations.return')}</Text>
                          </TouchableOpacity>
                        );
                      }
                      return null;
                    })()}
                  </TouchableOpacity>
                ))
              )
            ) : (
              getFilteredAllocations('machinery').length === 0 ? (
                <View style={styles.emptyContainer}>
                  <Truck size={48} color={colors.textSecondary} />
                  <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                    {machineryFilter === 'all'
                      ? t('allocations.noMachineryAllocations')
                      : t(`allocations.no${machineryFilter.charAt(0).toUpperCase() + machineryFilter.slice(1)}Machinery`)
                    }
                  </Text>
                </View>
              ) : (
                getFilteredAllocations('machinery').map((allocation) => (
                  <TouchableOpacity
                    key={allocation.id}
                    style={[styles.allocationCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
                    onPress={() => handleAllocationCardPress(allocation)}
                  >
                    <View style={styles.cardHeader}>
                      <View style={styles.cardLeft}>
                        <View style={[styles.typeIcon, { backgroundColor: colors.secondary + '20' }]}>
                          <Truck size={24} color={colors.secondary} />
                        </View>
                        <View style={styles.cardInfo}>
                          <Text style={[styles.itemName, { color: colors.text }]}>
                            {allocation.itemName}
                          </Text>
                          <Text style={[styles.allocationDate, { color: colors.textSecondary }]}>
                            {t('allocations.allocatedOn')}: {formatDate(allocation.allocationDate)}
                          </Text>
                        </View>
                      </View>

                      {/* Machinery Status Badge with Condition */}
                      {(() => {
                        const returnInfo = getReturnInfo(allocation.id);
                        const status = getReturnStatus(allocation);
                        const isReturned = status === 'returned';

                        if (isReturned) {
                          // Get condition from allocation or return data
                          const condition = allocation.condition || returnInfo.latestReturn?.condition;

                          return (
                            <View style={styles.returnBadgeContainer}>
                              <View style={[styles.returnBadge, { backgroundColor: colors.success + '20' }]}>
                                <Text style={[styles.returnBadgeText, { color: colors.success }]}>
                                  {t('allocations.returned')}
                                </Text>
                              </View>
                              {condition && (
                                <View style={[styles.conditionBadge, { backgroundColor: colors.textSecondary + '20' }]}>
                                  <Text style={[styles.conditionBadgeText, { color: colors.textSecondary }]}>
                                    {t(`allocations.${condition}`)}
                                  </Text>
                                </View>
                              )}
                            </View>
                          );
                        } else {
                          return (
                            <View style={[styles.returnBadge, { backgroundColor: colors.warning + '20' }]}>
                              <Text style={[styles.returnBadgeText, { color: colors.warning }]}>
                                {t('allocations.inUse')}
                              </Text>
                            </View>
                          );
                        }
                      })()}
                    </View>

                    <View style={styles.machineryDetails}>
                      {(() => {
                        const returnInfo = getReturnInfo(allocation.id);
                        const status = getReturnStatus(allocation);

                        if (status === 'returned') {
                          // Get condition and return date from allocation or return data
                          const condition = allocation.condition || returnInfo.latestReturn?.condition;
                          const returnDate = allocation.actualReturnDate || returnInfo.latestReturn?.approvedAt;

                          return (
                            <>
                              <Text style={[styles.machineryDescription, { color: colors.textSecondary }]}>
                                {t('allocations.returnedToFarm')}
                              </Text>

                              {returnDate && (
                                <Text style={[styles.returnDate, { color: colors.success }]}>
                                  {t('allocations.returnedOn')}: {formatDate(returnDate)}
                                </Text>
                              )}

                              {condition && (
                                <View style={styles.conditionRow}>
                                  <Text style={[styles.conditionLabel, { color: colors.textSecondary }]}>
                                    {t('allocations.condition')}:
                                  </Text>
                                  <Text style={[styles.conditionValue, { color: colors.text }]}>
                                    {t(`allocations.${condition}`)}
                                  </Text>
                                </View>
                              )}

                              {returnInfo.latestReturn?.returnNotes && (
                                <Text style={[styles.returnNotes, { color: colors.textSecondary }]}>
                                  {t('allocations.notes')}: {returnInfo.latestReturn.returnNotes}
                                </Text>
                              )}
                            </>
                          );
                        } else {
                          return (
                            <>
                              <Text style={[styles.machineryDescription, { color: colors.textSecondary }]}>
                                {t('allocations.currentlyInUse')}
                              </Text>
                              {allocation.expectedReturnDate && (
                                <Text style={[styles.expectedReturn, { color: colors.warning }]}>
                                  {t('allocations.expectedReturn')}: {formatDate(allocation.expectedReturnDate)}
                                </Text>
                              )}
                            </>
                          );
                        }
                      })()}
                    </View>


                  </TouchableOpacity>
                ))
              )
            )}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
  },
  userHeader: {
    padding: 16,
    borderBottomWidth: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  userRole: {
    fontSize: 14,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 12,
    gap: 8,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  loadingContainer: {
    padding: 32,
    alignItems: 'center',
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    gap: 12,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  allocationCard: {
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: 12,
    padding: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  typeIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  allocationDate: {
    fontSize: 12,
  },
  statusTag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  quantitySection: {
    gap: 8,
  },
  quantityRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  quantityLabel: {
    fontSize: 14,
  },
  quantityValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  machineryDetails: {
    gap: 4,
  },
  machineryDescription: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  expectedReturn: {
    fontSize: 12,
    fontWeight: '500',
  },
  returnButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginTop: 12,
    gap: 6,
  },
  returnButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  returnBadgeContainer: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    gap: 4,
  },
  returnBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  returnBadgeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  conditionBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  conditionBadgeText: {
    fontSize: 10,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  conditionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  conditionLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  conditionValue: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  returnDate: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 4,
  },
  returnNotes: {
    fontSize: 11,
    fontStyle: 'italic',
    marginTop: 4,
  },
  filterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 12,
  },
  filterButtons: {
    flexDirection: 'row',
    flex: 1,
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
});

import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
} from "react-native";
import { useRouter, Stack } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { useAuth } from "@/context/auth-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { useTheme } from "@/context/theme-context";
import { 
  getExpensesByFarm, 
  getUserExpenses, 
  getExpenseSummary, 
  Expense, 
  ExpenseSummary,
  ExpenseFilter 
} from "@/services/expense-service";
import {
  DollarSign,
  TrendingUp,
  Calendar,
  Filter,
  Search,
  ArrowLeft,
  PieChart,
  BarChart3,
  Users,
  Building
} from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";

export default function ExpensesScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const { selectedFarm } = useFarm();
  const { colors, theme } = useTheme();
  const { t } = useLanguage();

  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [summary, setSummary] = useState<ExpenseSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState<ExpenseFilter>({ type: 'all' });
  const [activeTab, setActiveTab] = useState<'farm' | 'user' | 'all'>('all');

  useEffect(() => {
    if (selectedFarm && user) {
      loadExpenses();
      loadSummary();
    }
  }, [selectedFarm, user, filter, activeTab]);

  const loadExpenses = async () => {
    if (!selectedFarm || !user) return;

    try {
      setLoading(true);
      let result;

      if (user.role === 'caretaker') {
        // Caretakers only see their own expenses
        result = await getUserExpenses(selectedFarm.id, user.uid, 3);
      } else {
        // Owners and admins see all expenses based on filter
        const currentFilter = { ...filter, farmId: selectedFarm.id };
        if (activeTab !== 'all') {
          currentFilter.type = activeTab;
        }
        result = await getExpensesByFarm(selectedFarm.id, currentFilter, 3);
      }

      setExpenses(result.expenses);
    } catch (error) {
      console.error('Error loading expenses:', error);
      Alert.alert(t('common.error'), t('expenses.loadError'));
    } finally {
      setLoading(false);
    }
  };

  const loadSummary = async () => {
    if (!selectedFarm || !user) return;

    try {
      const summaryData = await getExpenseSummary(selectedFarm.id);
      setSummary(summaryData);
    } catch (error) {
      console.error('Error loading expense summary:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([loadExpenses(), loadSummary()]);
    setRefreshing(false);
  };

  const formatCurrency = (amount: number) => {
    const isNegative = amount < 0;
    const absAmount = Math.abs(amount);
    return `${isNegative ? '-' : ''}Rs ${absAmount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'inventory': return '📦';
      case 'machinery': return '🚜';
      case 'fuel': return '⛽';
      case 'maintenance': return '🔧';
      case 'labor': return '👷';
      case 'utilities': return '💡';
      case 'transport': return '🚛';
      default: return '💰';
    }
  };

  const getExpenseTypeLabel = (type: string) => {
    return type === 'farm' ? t('expenses.farmExpense') : t('expenses.userExpense');
  };

  const formatAmount = (amount: number) => {
    const isNegative = amount < 0;
    const absAmount = Math.abs(amount);
    return `${isNegative ? '-' : ''}Rs. ${absAmount.toLocaleString()}`;
  };

  const getExpenseTypeColor = (type: string) => {
    return type === 'farm' ? colors.primary : colors.secondary;
  };

  if (!selectedFarm) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <Stack.Screen options={{
          title: t('expenses.title'),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { color: 'white', fontWeight: '600' },
        }} />
        <View style={styles.centerContainer}>
          <Text style={[styles.message, { color: colors.text }]}>
            {t('farm.selectFarmFirst')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t('expenses.title'),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { color: 'white', fontWeight: '600' },
        }}
      />

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Enhanced Summary Cards */}
        {summary && (
          <View style={styles.summaryContainer}>
            {/* Total Expenses - Inventory & Machinery */}
            <LinearGradient
              colors={[colors.primary, colors.primary + '80']}
              style={[styles.summaryCard, styles.totalCard]}
            >
              <View style={styles.cardHeader}>
                <DollarSign size={24} color="white" />
                <Text style={styles.summaryLabel}>{t('expenses.totalExpenses')}</Text>
              </View>
              <Text style={styles.summaryAmount}>
                {formatCurrency(summary.totalExpenses)}
              </Text>
              <Text style={styles.summarySubtext}>
                {t('expenses.inventoryMachineryTotal')}
              </Text>
            </LinearGradient>

            <View style={styles.summaryRow}>
              {/* Farm Expenses - User Expenses Total */}
              <View style={[styles.summaryCard, styles.halfCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                <View style={styles.cardIconContainer}>
                  <Building size={20} color={colors.primary} />
                </View>
                <Text style={[styles.summaryAmount, { color: colors.text, fontSize: 18 }]}>
                  {formatCurrency(summary.farmExpenses)}
                </Text>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                  {t('expenses.farmExpenses')}
                </Text>
                <Text style={[styles.summarySubtext, { color: colors.textSecondary }]}>
                  {t('expenses.allUserExpensesTotal')}
                </Text>
              </View>

              {/* Monthly Expenses - User Expenses This Month */}
              <View style={[styles.summaryCard, styles.halfCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                <View style={styles.cardIconContainer}>
                  <Calendar size={20} color={colors.accent} />
                </View>
                <Text style={[styles.summaryAmount, { color: colors.text, fontSize: 18 }]}>
                  {formatCurrency(summary.monthlyExpenses)}
                </Text>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                  {t('expenses.monthlyExpenses')}
                </Text>
                <Text style={[styles.summarySubtext, { color: colors.textSecondary }]}>
                  {t('expenses.userExpensesThisMonth')}
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Filter Tabs */}
        {user?.role !== 'caretaker' && (
          <View style={styles.tabContainer}>
            {['all', 'farm', 'user'].map((tab) => (
              <TouchableOpacity
                key={tab}
                style={[
                  styles.tab,
                  {
                    backgroundColor: activeTab === tab ? colors.primary : colors.surface,
                    borderColor: colors.border,
                  }
                ]}
                onPress={() => setActiveTab(tab as 'farm' | 'user' | 'all')}
              >
                <Text
                  style={[
                    styles.tabText,
                    {
                      color: activeTab === tab ? 'white' : colors.text,
                    }
                  ]}
                >
                  {t(`expenses.${tab}${tab === 'all' ? 'Types' : 'Expenses'}`)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Expenses List */}
        <View style={[styles.card, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              {t('expenses.recentExpenses')}
            </Text>
            <TouchableOpacity
              style={[styles.filterButton, { borderColor: colors.border }]}
              onPress={() => {/* TODO: Open filter modal */}}
            >
              <Filter size={16} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          ) : expenses.length === 0 ? (
            <View style={styles.emptyContainer}>
              <DollarSign size={48} color={colors.textSecondary} />
              <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                {t('expenses.noExpenses')}
              </Text>
            </View>
          ) : (
            <View style={styles.expensesList}>
              {expenses.map((expense) => (
                <TouchableOpacity
                  key={expense.id}
                  style={[styles.expenseItem, { borderBottomColor: colors.border }]}
                  onPress={() => {
                    console.log('🔍 Expense card clicked:', {
                      expenseId: expense.id,
                      requestId: expense.requestId,
                      farmId: expense.farmId,
                      description: expense.description,
                      type: expense.type,
                      category: expense.category,
                      hasRequestId: !!expense.requestId
                    });

                    if (expense.requestId) {
                      // Navigate to the original request details
                      const navigationPath = `/request/${expense.requestId}?farmId=${expense.farmId}`;
                      console.log('✅ Navigating to request details:', navigationPath);
                      router.push(navigationPath);
                    } else {
                      // Fallback to expense details if no request ID
                      console.log('❌ No requestId found, this expense was not created from an approved request');
                      console.log('💡 This might be a farm expense or manually created expense');
                      // For now, just show an alert instead of navigating to expense details
                      alert(`This expense (${expense.description}) was not created from a request and doesn't have an associated request to view.`);
                    }
                  }}
                >
                  <View style={styles.expenseLeft}>
                    <View style={[styles.categoryIcon, { backgroundColor: getExpenseTypeColor(expense.type) + '20' }]}>
                      <Text style={styles.categoryEmoji}>{getCategoryIcon(expense.category)}</Text>
                    </View>
                    <View style={styles.expenseInfo}>
                      <Text style={[styles.expenseDescription, { color: colors.text }]}>
                        {expense.description}
                      </Text>
                      <Text style={[styles.expenseDetails, { color: colors.textSecondary }]}>
                        {t(`expenses.categories.${expense.category}`)} • {formatDate(expense.date)}
                        {expense.userName && ` • ${expense.userName}`}
                      </Text>
                      {expense.relatedItemName && (
                        <Text style={[styles.relatedItem, { color: colors.textSecondary }]}>
                          📋 {expense.relatedItemName}
                        </Text>
                      )}
                      {expense.requestId && (
                        <Text style={[styles.requestLink, { color: colors.primary }]}>
                          📄 {t('expenses.viewRequest')}
                        </Text>
                      )}
                    </View>
                  </View>
                  <View style={styles.expenseRight}>
                    <Text style={[
                      styles.expenseAmount,
                      { color: expense.amount < 0 ? colors.error : expense.amount === 0 ? colors.warning : colors.text }
                    ]}>
                      {expense.amount === 0 ? 'Rs 0 (No Price)' : formatCurrency(expense.amount)}
                    </Text>
                    <View style={[styles.typeTag, { backgroundColor: getExpenseTypeColor(expense.type) + '20' }]}>
                      <Text style={[styles.typeTagText, { color: getExpenseTypeColor(expense.type) }]}>
                        {expense.amount < 0 ? t('expenses.adjustment') :
                         expense.amount === 0 ? t('expenses.tracking') :
                         t(`expenses.${expense.type}Expense`)}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        {/* View All Button */}
        {expenses.length > 0 && (
          <TouchableOpacity
            style={[styles.viewAllButton, { backgroundColor: colors.surface, borderColor: colors.border }]}
            onPress={() => router.push('/expenses/details')}
          >
            <Text style={[styles.viewAllText, { color: colors.primary }]}>
              {t('expenses.viewAll')}
            </Text>
          </TouchableOpacity>
        )}
      </ScrollView>


    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  summaryContainer: {
    padding: 16,
    gap: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    gap: 12,
  },
  summaryCard: {
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
    alignItems: 'center',
  },
  halfCard: {
    flex: 1,
  },
  totalCard: {
    marginBottom: 4,
    paddingVertical: 24,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  cardIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryAmount: {
    fontSize: 24,
    fontWeight: '700',
    color: 'white',
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
    textAlign: 'center',
    marginBottom: 4,
  },
  summarySubtext: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 16,
    gap: 8,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
  },
  card: {
    margin: 16,
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  filterButton: {
    padding: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  loadingContainer: {
    padding: 32,
    alignItems: 'center',
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    gap: 12,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  expensesList: {
    padding: 16,
  },
  expenseItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  expenseLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryEmoji: {
    fontSize: 18,
  },
  expenseInfo: {
    flex: 1,
  },
  expenseDescription: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  expenseDetails: {
    fontSize: 12,
  },
  expenseRight: {
    alignItems: 'flex-end',
    gap: 4,
  },
  expenseAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  typeTag: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  typeTagText: {
    fontSize: 10,
    fontWeight: '500',
  },
  viewAllButton: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
  },
  viewAllText: {
    fontSize: 16,
    fontWeight: '500',
  },
  relatedItem: {
    fontSize: 12,
    marginTop: 2,
    fontStyle: 'italic',
  },
  requestLink: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 2,
  },
});

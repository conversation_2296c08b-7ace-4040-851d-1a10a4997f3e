import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Alert, ActivityIndicator, Image, Linking } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { getUserById, deleteUser, User as UserType } from '@/services/user-service';
import { getFarmsByIds, getLocationDisplay } from '@/services/farm-service';
import { useTheme } from '@/context/theme-context';
import { useLanguage } from '@/context/language-context';
import { useAuth } from '@/context/auth-context';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Shield, 
  Edit, 
  Trash2, 
  Building, 
  Clock,
  UserCheck,
  IdCard,
  Heart,
  ArrowLeft
} from 'lucide-react-native';

export default function UserDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { theme, colors } = useTheme();
  const { t } = useLanguage();
  const { user: currentUser } = useAuth();

  const [user, setUser] = useState<UserType | null>(null);
  const [userFarms, setUserFarms] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [farmsLoading, setFarmsLoading] = useState(false);

  const isDarkMode = theme === 'dark';
  const backgroundColor = isDarkMode ? '#121212' : '#f9f9f9';
  const cardColor = isDarkMode ? '#1e1e1e' : '#ffffff';
  const textColor = isDarkMode ? '#ffffff' : '#333333';
  const secondaryTextColor = isDarkMode ? '#aaaaaa' : '#666666';
  const borderColor = isDarkMode ? '#333333' : '#e0e0e0';

  // Get role-based primary color
  const getRolePrimaryColor = () => {
    if (!currentUser) return colors.primary;
    switch (currentUser.role) {
      case 'owner':
        return isDarkMode ? '#2E7D32' : '#4CAF50';
      case 'admin':
        return isDarkMode ? '#1976D2' : '#2196F3';
      case 'caretaker':
        return isDarkMode ? '#F57C00' : '#FF9800';
      default:
        return colors.primary;
    }
  };

  const primaryColor = getRolePrimaryColor();

  useEffect(() => {
    if (!id) {
      Alert.alert(t('common.error'), t('users.noIdProvided'));
      router.back();
      return;
    }
    loadUserDetails();
  }, [id]);

  const loadUserDetails = async () => {
    try {
      setLoading(true);
      const userData = await getUserById(id);
      
      if (!userData) {
        Alert.alert(t('common.error'), t('users.notFound'));
        router.back();
        return;
      }
      
      setUser(userData);
      
      // Load user's farms if they have any assigned
      if (userData.assignedFarmIds && userData.assignedFarmIds.length > 0) {
        setFarmsLoading(true);
        try {
          const farms = await getFarmsByIds(userData.assignedFarmIds);
          setUserFarms(farms);
        } catch (error) {
          console.error('Error loading user farms:', error);
        } finally {
          setFarmsLoading(false);
        }
      }
    } catch (error) {
      console.error('Error loading user details:', error);
      Alert.alert(t('common.error'), t('users.loadError'));
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    router.push(`/users/edit?id=${user?.uid}`);
  };

  const handleDelete = () => {
    if (!user) return;
    
    Alert.alert(
      t('users.deleteUser'),
      t('users.deleteConfirm'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteUser(user.uid);
              Alert.alert(t('common.success'), t('users.userDeleted'));
              router.back();
            } catch (error) {
              console.error('Error deleting user:', error);
              Alert.alert(t('common.error'), t('users.deleteError'));
            }
          }
        }
      ]
    );
  };

  const handleCall = () => {
    const phoneNumber = user?.phoneNumber || user?.phone;
    if (phoneNumber) {
      Linking.openURL(`tel:${phoneNumber}`);
    }
  };

  const handleEmail = () => {
    if (user?.email) {
      Linking.openURL(`mailto:${user.email}`);
    }
  };

  // Get role color
  const getRoleColor = () => {
    if (!user) return primaryColor;
    switch (user.role) {
      case 'owner':
        return isDarkMode ? '#2E7D32' : '#4CAF50';
      case 'admin':
        return isDarkMode ? '#1976D2' : '#2196F3';
      case 'caretaker':
        return isDarkMode ? '#F57C00' : '#FF9800';
      default:
        return isDarkMode ? '#757575' : '#9E9E9E';
    }
  };

  // Get role name
  const getRoleName = () => {
    if (!user) return '';
    switch (user.role) {
      case 'owner':
        return t('profile.owner');
      case 'admin':
        return t('profile.admin');
      case 'caretaker':
        return t('profile.caretaker');
      default:
        return t('common.unknown');
    }
  };

  // Get user initials
  const getUserInitials = () => {
    if (!user) return 'U';
    const name = user.displayName || user.name || 'U';
    const nameParts = name.split(' ');
    if (nameParts.length === 1) {
      return nameParts[0].charAt(0).toUpperCase();
    } else {
      return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
    }
  };

  // Format date
  const formatDate = (dateValue?: string | any) => {
    if (!dateValue) {
      console.log('formatDate: No date value provided');
      return "Not available";
    }

    try {
      let date: Date;

      if (typeof dateValue === 'string') {
        // Handle ISO string or other string formats
        date = new Date(dateValue);
      } else if (dateValue.seconds) {
        // Firestore timestamp with seconds
        date = new Date(dateValue.seconds * 1000);
      } else if (dateValue.toDate && typeof dateValue.toDate === 'function') {
        // Firestore timestamp with toDate method
        date = dateValue.toDate();
      } else if (dateValue._seconds) {
        // Alternative Firestore timestamp format
        date = new Date(dateValue._seconds * 1000);
      } else if (typeof dateValue === 'number') {
        // Unix timestamp
        date = new Date(dateValue);
      } else {
        // Try to convert directly
        date = new Date(dateValue);
      }

      if (isNaN(date.getTime())) {
        console.log('formatDate: Invalid date after conversion:', dateValue);
        return "Not available";
      }

      return date.toLocaleDateString();
    } catch (error) {
      console.error('formatDate error:', error, 'for value:', dateValue);
      return "Not available";
    }
  };

  // Get address safely - handle location objects
  const getAddress = () => {
    if (!user?.address) return "";
    
    // If address is a string, return it
    if (typeof user.address === 'string') {
      return user.address;
    }
    
    // If address is an object, try to extract meaningful text
    if (typeof user.address === 'object' && user.address !== null) {
      const addressObj = user.address as any;
      if (addressObj.address && typeof addressObj.address === 'string') {
        return addressObj.address;
      }
      
      if (addressObj.latitude && addressObj.longitude) {
        return `${String(addressObj.latitude)}, ${String(addressObj.longitude)}`;
      }
      
      // If it's an object but we can't extract meaningful data, return empty
      return "";
    }
    
    return "";
  };

  // Check if current user can edit this user
  const canEdit = () => {
    if (!currentUser || !user) return false;
    
    // Users can edit themselves
    if (user.uid === currentUser.uid) return true;
    
    // Owner can edit everyone
    if (currentUser.role === 'owner') return true;
    
    // Admin can only edit caretakers
    if (currentUser.role === 'admin' && user.role === 'caretaker') return true;
    
    return false;
  };

  // Check if current user can delete this user
  const canDelete = () => {
    if (!currentUser || !user) return false;
    
    // Users can't delete themselves
    if (user.uid === currentUser.uid) return false;
    
    // Owner can delete everyone except themselves
    if (currentUser.role === 'owner') return true;
    
    // Admin can only delete caretakers
    if (currentUser.role === 'admin' && user.role === 'caretaker') return true;
    
    return false;
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
        <Stack.Screen
          options={{
            title: t('users.userDetails'),
            headerStyle: { backgroundColor: primaryColor },
            headerTintColor: '#fff',
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={primaryColor} />
          <Text style={[styles.loadingText, { color: secondaryTextColor }]}>
            {t('common.loading')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!user) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
        <Stack.Screen
          options={{
            title: t('users.userDetails'),
            headerStyle: { backgroundColor: primaryColor },
            headerTintColor: '#fff',
          }}
        />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: textColor }]}>
            {t('users.notFound')}
          </Text>
          <TouchableOpacity 
            style={[styles.backButton, { backgroundColor: primaryColor }]}
            onPress={() => router.back()}
          >
            <ArrowLeft size={20} color="#fff" />
            <Text style={styles.backButtonText}>{t('common.goBack')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: user.displayName || user.name || t('users.userDetails'),
          headerStyle: { backgroundColor: primaryColor },
          headerTintColor: '#fff',
        }}
      />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <View style={[styles.profileHeader, { backgroundColor: cardColor, borderColor }]}>
          <View style={styles.avatarContainer}>
            {user.photoURL ? (
              <Image 
                source={{ uri: user.photoURL }}
                style={styles.avatar}
                resizeMode="cover"
              />
            ) : (
              <View style={[styles.avatarPlaceholder, { backgroundColor: getRoleColor() + '20' }]}>
                <Text style={[styles.avatarInitials, { color: getRoleColor() }]}>
                  {getUserInitials()}
                </Text>
              </View>
            )}
            
            <View style={[styles.roleBadge, { backgroundColor: getRoleColor() }]}>
              <Text style={styles.roleBadgeText}>{getRoleName()}</Text>
            </View>
          </View>
          
          <View style={styles.profileInfo}>
            <Text style={[styles.userName, { color: textColor }]}>
              {user.displayName || user.name || t('common.unknown')}
            </Text>
            <Text style={[styles.userEmail, { color: secondaryTextColor }]}>
              {user.email}
            </Text>
            
            {user.bio && (
              <Text style={[styles.userBio, { color: secondaryTextColor }]}>
                {user.bio}
              </Text>
            )}
          </View>
        </View>

        {/* Quick Actions */}
        <View style={[styles.quickActions, { backgroundColor: cardColor, borderColor }]}>
          {(user.phoneNumber || user.phone) && (
            <TouchableOpacity style={styles.actionButton} onPress={handleCall}>
              <Phone size={24} color={primaryColor} />
              <Text style={[styles.actionText, { color: textColor }]}>{t('common.phone')}</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity style={styles.actionButton} onPress={handleEmail}>
            <Mail size={24} color={primaryColor} />
            <Text style={[styles.actionText, { color: textColor }]}>{t('profile.email')}</Text>
          </TouchableOpacity>
        </View>

        {/* Personal Information */}
        <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            {t('profile.personalInformation')}
          </Text>
          
          <View style={styles.infoGrid}>
            <View style={styles.infoItem}>
              <User size={20} color={secondaryTextColor} />
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
                  {t('profile.name')}
                </Text>
                <Text style={[styles.infoValue, { color: textColor }]}>
                  {user.displayName || user.name || t('common.notSet')}
                </Text>
              </View>
            </View>

            {user.gender && (
              <View style={styles.infoItem}>
                <Heart size={20} color={secondaryTextColor} />
                <View style={styles.infoContent}>
                  <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
                    Gender
                  </Text>
                  <Text style={[styles.infoValue, { color: textColor }]}>
                    {user.gender}
                  </Text>
                </View>
              </View>
            )}

            {user.dateOfBirth && (
              <View style={styles.infoItem}>
                <Calendar size={20} color={secondaryTextColor} />
                <View style={styles.infoContent}>
                  <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
                    Date of Birth
                  </Text>
                  <Text style={[styles.infoValue, { color: textColor }]}>
                    {user.dateOfBirth}
                  </Text>
                </View>
              </View>
            )}

            {user.cnic && (
              <View style={styles.infoItem}>
                <IdCard size={20} color={secondaryTextColor} />
                <View style={styles.infoContent}>
                  <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
                    CNIC
                  </Text>
                  <Text style={[styles.infoValue, { color: textColor }]}>
                    {user.cnic}
                  </Text>
                </View>
              </View>
            )}

            {getAddress() && (
              <View style={styles.infoItem}>
                <MapPin size={20} color={secondaryTextColor} />
                <View style={styles.infoContent}>
                  <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
                    Address
                  </Text>
                  <Text style={[styles.infoValue, { color: textColor }]}>
                    {getAddress()}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>

        {/* Contact Information */}
        <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            {t('profile.contactInfo')}
          </Text>
          
          <View style={styles.infoGrid}>
            <View style={styles.infoItem}>
              <Mail size={20} color={secondaryTextColor} />
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
                  {t('profile.email')}
                </Text>
                <Text style={[styles.infoValue, { color: textColor }]}>
                  {user.email}
                </Text>
              </View>
            </View>

            {(user.phoneNumber || user.phone) && (
              <View style={styles.infoItem}>
                <Phone size={20} color={secondaryTextColor} />
                <View style={styles.infoContent}>
                  <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
                    {t('profile.phone')}
                  </Text>
                  <Text style={[styles.infoValue, { color: textColor }]}>
                    {user.phoneNumber || user.phone}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>

        {/* Role Information */}
        <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            {t('profile.roleInfo')}
          </Text>
          
          <View style={styles.infoGrid}>
            <View style={styles.infoItem}>
              <Shield size={20} color={secondaryTextColor} />
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
                  {t('profile.role')}
                </Text>
                <Text style={[styles.infoValue, { color: getRoleColor() }]}>
                  {getRoleName()}
                </Text>
              </View>
            </View>

            <View style={styles.infoItem}>
              <UserCheck size={20} color={secondaryTextColor} />
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
                  {t('profile.status')}
                </Text>
                <Text style={[styles.infoValue, { color: '#4CAF50' }]}>
                  {t('users.active')}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Assigned Farms */}
        {userFarms.length > 0 && (
          <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
            <Text style={[styles.sectionTitle, { color: textColor }]}>
              {t('profile.assignedFarms')}
            </Text>
            
            {farmsLoading ? (
              <ActivityIndicator size="small" color={primaryColor} style={styles.farmsLoading} />
            ) : (
              <View style={styles.farmsList}>
                {userFarms.map((farm, index) => (
                  <View key={farm.id || index} style={[styles.farmItem, { borderColor }]}>
                    <Building size={20} color={primaryColor} />
                    <View style={styles.farmInfo}>
                      <Text style={[styles.farmName, { color: textColor }]}>
                        {farm.name || t('common.unknownFarm')}
                      </Text>
                      <Text style={[styles.farmLocation, { color: secondaryTextColor }]}>
                        {getLocationDisplay(farm)}
                      </Text>
                    </View>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}

        {/* Account Information */}
        <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            {t('profile.accountInfo')}
          </Text>
          
          <View style={styles.infoGrid}>
            <View style={styles.infoItem}>
              <Calendar size={20} color={secondaryTextColor} />
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
                  {t('profile.memberSince')}
                </Text>
                <Text style={[styles.infoValue, { color: textColor }]}>
                  {formatDate(user.createdAt || user.updatedAt || user.lastLogin)}
                </Text>
              </View>
            </View>

            {user.lastLogin && (
              <View style={styles.infoItem}>
                <Clock size={20} color={secondaryTextColor} />
                <View style={styles.infoContent}>
                  <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
                    {t('profile.lastLogin')}
                  </Text>
                  <Text style={[styles.infoValue, { color: textColor }]}>
                    {formatDate(user.lastLogin)}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Footer with Edit/Delete buttons */}
      {(canEdit() || canDelete()) && (
        <View style={[styles.footer, { backgroundColor: cardColor, borderTopColor: borderColor }]}>
          {canEdit() && (
            <TouchableOpacity
              style={[styles.button, styles.editButton, { backgroundColor: primaryColor }]}
              onPress={handleEdit}
            >
              <Edit size={20} color="#fff" />
              <Text style={styles.buttonText}>{t('common.edit')}</Text>
            </TouchableOpacity>
          )}
          {canDelete() && (
            <TouchableOpacity
              style={[styles.button, styles.deleteButton, { backgroundColor: '#D32F2F' }]}
              onPress={handleDelete}
            >
              <Trash2 size={20} color="#fff" />
              <Text style={styles.buttonText}>{t('common.delete')}</Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 20,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },

  profileHeader: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarInitials: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  roleBadge: {
    position: 'absolute',
    bottom: -4,
    right: -4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  roleBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  profileInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 16,
    marginBottom: 8,
  },
  userBio: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  quickActions: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    alignItems: 'center',
    flex: 1,
  },
  actionText: {
    marginTop: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  section: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  infoGrid: {
    gap: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  infoContent: {
    marginLeft: 12,
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  farmsList: {
    gap: 12,
  },
  farmItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  farmInfo: {
    marginLeft: 12,
    flex: 1,
  },
  farmName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  farmLocation: {
    fontSize: 14,
  },
  farmsLoading: {
    marginVertical: 20,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
  },
  button: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 6,
    gap: 8,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  editButton: {
    backgroundColor: '#2E7D32',
  },
  deleteButton: {
    backgroundColor: '#D32F2F',
  },
});
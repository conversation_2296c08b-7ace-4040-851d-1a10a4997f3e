import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, RefreshControl, Alert } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter } from "expo-router";
import { Truck, Plus } from "lucide-react-native";
import { useFarm } from "@/context/farm-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { getMachineryByFarm, deleteMachinery, Machinery, subscribeToMachinery } from "@/services/machinery-service";
import MachineryCard from "@/components/MachineryCard";

export default function AdminMachineryScreen() {
  const router = useRouter();
  const { selectedFarm } = useFarm();
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  const [machinery, setMachinery] = useState<Machinery[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState<string>("all");
  
  useEffect(() => {
    // If no farm is selected, don't load machinery
    if (!selectedFarm) {
      setMachinery([]);
      setLoading(false);
      return;
    }

    // Set up real-time listener for machinery changes
    const unsubscribe = subscribeToMachinery(selectedFarm.id, (machineryData) => {
      setMachinery(machineryData);
      setLoading(false);
      setRefreshing(false);
    });

    // Clean up listener on unmount
    return () => {
      unsubscribe();
    };
  }, [selectedFarm]);

  const onRefresh = () => {
    setRefreshing(true);
    // The real-time subscription will automatically update the data
    // Just reset the refreshing state after a short delay
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };
  
  const getFilteredMachinery = () => {
    if (filter === "all") return machinery;
    return machinery.filter(m => m.status === filter);
  };
  
  const handleAddMachinery = () => {
    router.push("/(app)/machinery/add");
  };
  
  const handleDeleteMachinery = async (machineryId: string) => {
    if (!selectedFarm) return;
    
    Alert.alert(
      t("machinery.deleteConfirmTitle"),
      t("machinery.deleteConfirmMessage"),
      [
        {
          text: t("common.cancel"),
          style: "cancel"
        },
        {
          text: t("common.delete"),
          style: "destructive",
          onPress: async () => {
            try {
              await deleteMachinery(machineryId, selectedFarm.id);
              Alert.alert(t("common.success"), t("machinery.deleteSuccess"));
              loadMachinery();
            } catch (error) {
              console.error("Error deleting machinery:", error);
              Alert.alert(t("common.error"), t("machinery.deleteError"));
            }
          }
        }
      ]
    );
  };
  
  const filterOptions = [
    { key: "all", label: t("common.all"), count: machinery.length },
    { key: "working", label: t("machinery.working"), count: machinery.filter(m => m.status === 'working').length },
    { key: "maintenance", label: t("machinery.maintenanceStatus"), count: machinery.filter(m => m.status === 'maintenance').length },
    { key: "malfunction", label: t("machinery.malfunction"), count: machinery.filter(m => m.status === 'malfunction').length },
    { key: "in_use", label: t("machinery.inUse"), count: machinery.filter(m => m.status === 'in_use' || m.status === 'in_use_other_farm').length },
  ];
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t("machinery.farmMachinery"),
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: "#fff",
          headerRight: () => (
            <TouchableOpacity
              onPress={handleAddMachinery}
              style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)" }]}
            >
              <Plus size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.scrollContent}
      >
        {!selectedFarm ? (
          <View style={[styles.noFarmContainer, { backgroundColor: colors.surface }]}>
            <Text style={[styles.noFarmText, { color: colors.text }]}>
              {t("common.pleaseSelectFarm")} {t("machinery.title").toLowerCase()}
            </Text>
          </View>
        ) : (
          <>
            <View style={styles.statsContainer}>
              <View style={[styles.statCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                <Text style={[styles.statValue, { color: colors.text }]}>{machinery.length}</Text>
                <Text style={[styles.statLabel, { color: colors.textSecondary }]}>{t("machinery.totalMachinery")}</Text>
              </View>
              <View style={[styles.statCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                <Text style={[styles.statValue, { color: "#4CAF50" }]}>
                  {machinery.filter(m => m.status === 'working').length}
                </Text>
                <Text style={[styles.statLabel, { color: colors.textSecondary }]}>{t("machinery.available")}</Text>
              </View>
              <View style={[styles.statCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                <Text style={[styles.statValue, { color: "#2196F3" }]}>
                  {machinery.filter(m => m.status === 'in_use' || m.status === 'in_use_other_farm').length}
                </Text>
                <Text style={[styles.statLabel, { color: colors.textSecondary }]}>{t("machinery.inUse")}</Text>
              </View>
            </View>
            
            <View style={styles.filterContainer}>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {filterOptions.map((option) => (
                  <TouchableOpacity
                    key={option.key}
                    style={[
                      styles.filterButton,
                      { 
                        backgroundColor: filter === option.key ? colors.primary : colors.surface,
                        borderColor: filter === option.key ? colors.primary : colors.border
                      }
                    ]}
                    onPress={() => setFilter(option.key)}
                  >
                    <Text style={[
                      styles.filterButtonText,
                      { color: filter === option.key ? "#fff" : colors.text }
                    ]}>
                      {option.label} ({option.count})
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
            
            <View style={styles.machineryContainer}>
              {loading ? (
                <View style={[styles.loadingContainer, { backgroundColor: colors.surface }]}>
                  <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                    {t("machinery.loadingMachinery")}
                  </Text>
                </View>
              ) : getFilteredMachinery().length === 0 ? (
                <View style={[styles.emptyContainer, { backgroundColor: colors.surface }]}>
                  <Truck size={48} color={colors.textSecondary} />
                  <Text style={[styles.emptyText, { color: colors.text }]}>
                    {filter === "all" ? t("machinery.noMachineryFound") : `${t("common.no")} ${filter.replace('_', ' ')} ${t("machinery.title").toLowerCase()}`}
                  </Text>
                  <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
                    {t("machinery.addFirstMachinery")}
                  </Text>
                  <TouchableOpacity
                    style={[styles.addButton, { backgroundColor: colors.primary }]}
                    onPress={handleAddMachinery}
                  >
                    <Plus size={20} color="#fff" style={styles.addButtonIcon} />
                    <Text style={styles.addButtonText}>{t("machinery.addMachinery")}</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                getFilteredMachinery().map((machineryItem) => (
                  <MachineryCard
                    key={machineryItem.id}
                    machinery={machineryItem}
                    userRole="admin"
                    onDeletePress={() => handleDeleteMachinery(machineryItem.id)}
                  />
                ))
              )}
            </View>
          </>
        )}
      </ScrollView>
      
      {/* Floating Action Button */}
      {selectedFarm && (
        <TouchableOpacity
          style={[styles.fab, { backgroundColor: colors.primary }]}
          onPress={handleAddMachinery}
          activeOpacity={0.8}
        >
          <Plus size={28} color="#fff" />
        </TouchableOpacity>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100, // Add padding to account for FAB
  },
  headerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  noFarmContainer: {
    padding: 20,
    borderRadius: 12,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  noFarmText: {
    fontSize: 16,
    textAlign: "center",
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
    marginHorizontal: 4,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statValue: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: "center",
  },
  filterContainer: {
    marginBottom: 16,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: "500",
  },
  machineryContainer: {
    gap: 12,
  },
  loadingContainer: {
    padding: 20,
    borderRadius: 12,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  loadingText: {
    fontSize: 16,
    textAlign: "center",
  },
  emptyContainer: {
    padding: 40,
    borderRadius: 12,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: "600",
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: "center",
    marginBottom: 20,
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 8,
  },
  addButtonIcon: {
    marginRight: 8,
  },
  addButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
  fab: {
    position: "absolute",
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});
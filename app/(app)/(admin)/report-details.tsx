import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Alert, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import { Download, Share, Calendar, FileText, BarChart3, TrendingUp, Settings, Truck, ArrowLeft } from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { useFarm } from "@/context/farm-context";
import { generateReportData, exportReport, scheduleReport } from "@/services/reports-service";
import { getMachineryReport } from "@/services/machinery-service";

export default function AdminReportDetailsScreen() {
  const router = useRouter();
  const { type, farmId } = useLocalSearchParams();
  const { theme } = useTheme();
  const { selectedFarm } = useFarm();
  const isDarkMode = theme === "dark";
  
  const [reportData, setReportData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [exporting, setExporting] = useState(false);
  
  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";
  
  const reportTypes: Record<string, any> = {
    machinery: {
      title: "Machinery Status Report",
      description: "Comprehensive machinery fleet status and performance analysis",
      icon: Truck,
      color: "#1976D2"
    },
    maintenance: {
      title: "Maintenance & Service Report",
      description: "Maintenance schedules, service history, and upcoming requirements",
      icon: Settings,
      color: "#FF9800"
    },
    fuel: {
      title: "Fuel & Resources Report",
      description: "Fuel consumption, costs, and resource allocation analysis",
      icon: BarChart3,
      color: "#F44336"
    },
    utilization: {
      title: "Utilization Trends Report",
      description: "Machinery usage patterns, efficiency metrics, and optimization opportunities",
      icon: TrendingUp,
      color: "#9C27B0"
    }
  };
  
  useEffect(() => {
    loadReportData();
  }, [type, farmId]);
  
  const loadReportData = async () => {
    if (!farmId || !type) return;
    
    try {
      setLoading(true);
      
      if (type === "machinery" || type === "maintenance" || type === "fuel" || type === "utilization") {
        // For machinery-related reports, use machinery service
        const data = await getMachineryReport(farmId as string);
        setReportData(data);
      } else {
        // For other reports, use general report service
        const data = await generateReportData(type as string, farmId as string);
        setReportData(data);
      }
    } catch (error) {
      console.error("Error loading report data:", error);
      Alert.alert("Error", "Failed to load report data");
    } finally {
      setLoading(false);
    }
  };
  
  const handleExport = async (format: "pdf" | "excel") => {
    try {
      setExporting(true);
      await exportReport(type as string, format);
      Alert.alert("Success", `Report exported as ${format.toUpperCase()}`);
    } catch (error) {
      console.error("Error exporting report:", error);
      Alert.alert("Error", "Failed to export report");
    } finally {
      setExporting(false);
    }
  };
  
  const handleSchedule = async () => {
    try {
      await scheduleReport({
        reportType: type as string,
        frequency: "weekly",
        emails: ["<EMAIL>"],
        format: "pdf"
      });
      Alert.alert("Success", "Report scheduled successfully");
    } catch (error) {
      console.error("Error scheduling report:", error);
      Alert.alert("Error", "Failed to schedule report");
    }
  };
  
  const currentReport = reportTypes[type as string];
  const IconComponent = currentReport?.icon || FileText;
  
  const renderMachineryReport = () => {
    if (!reportData) return null;
    
    return (
      <View style={styles.reportContent}>
        <View style={styles.summarySection}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>Summary</Text>
          <View style={styles.summaryGrid}>
            <View style={[styles.summaryCard, { backgroundColor: cardColor, borderColor }]}>
              <Text style={[styles.summaryValue, { color: "#4CAF50" }]}>
                {reportData.summary.totalMachinery}
              </Text>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>
                Total Machinery
              </Text>
            </View>
            <View style={[styles.summaryCard, { backgroundColor: cardColor, borderColor }]}>
              <Text style={[styles.summaryValue, { color: "#2196F3" }]}>
                {reportData.summary.activeMachinery}
              </Text>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>
                Active Units
              </Text>
            </View>
            <View style={[styles.summaryCard, { backgroundColor: cardColor, borderColor }]}>
              <Text style={[styles.summaryValue, { color: "#FF9800" }]}>
                {reportData.summary.maintenanceDue}
              </Text>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>
                Maintenance Due
              </Text>
            </View>
            <View style={[styles.summaryCard, { backgroundColor: cardColor, borderColor }]}>
              <Text style={[styles.summaryValue, { color: "#9C27B0" }]}>
                {reportData.summary.utilizationRate}%
              </Text>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>
                Utilization Rate
              </Text>
            </View>
          </View>
        </View>
        
        <View style={styles.detailsSection}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>Status Breakdown</Text>
          {reportData.statusData.map((status: any, index: number) => (
            <View key={index} style={[styles.statusItem, { backgroundColor: cardColor, borderColor }]}>
              <View style={[styles.statusIndicator, { backgroundColor: status.color }]} />
              <Text style={[styles.statusName, { color: textColor }]}>{status.name}</Text>
              <Text style={[styles.statusCount, { color: secondaryTextColor }]}>
                {status.count} units
              </Text>
            </View>
          ))}
        </View>
        
        <View style={styles.detailsSection}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>Key Metrics</Text>
          <View style={[styles.metricsCard, { backgroundColor: cardColor, borderColor }]}>
            <View style={styles.metricRow}>
              <Text style={[styles.metricLabel, { color: secondaryTextColor }]}>
                Total Fuel Consumption
              </Text>
              <Text style={[styles.metricValue, { color: textColor }]}>
                {reportData.summary.fuelConsumption}L
              </Text>
            </View>
            <View style={styles.metricRow}>
              <Text style={[styles.metricLabel, { color: secondaryTextColor }]}>
                Inter-farm Requests
              </Text>
              <Text style={[styles.metricValue, { color: textColor }]}>
                {reportData.summary.interFarmRequests}
              </Text>
            </View>
            <View style={styles.metricRow}>
              <Text style={[styles.metricLabel, { color: secondaryTextColor }]}>
                Pending Requests
              </Text>
              <Text style={[styles.metricValue, { color: textColor }]}>
                {reportData.summary.pendingRequests}
              </Text>
            </View>
            <View style={styles.metricRow}>
              <Text style={[styles.metricLabel, { color: secondaryTextColor }]}>
                Malfunction Count
              </Text>
              <Text style={[styles.metricValue, { color: textColor }]}>
                {reportData.summary.malfunctionCount}
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };
  
  const renderReportContent = () => {
    if (type === "machinery" || type === "maintenance" || type === "fuel" || type === "utilization") {
      return renderMachineryReport();
    }
    
    // For other report types, show a generic view
    return (
      <View style={styles.reportContent}>
        <View style={[styles.placeholderCard, { backgroundColor: cardColor, borderColor }]}>
          <IconComponent size={48} color={currentReport?.color || "#666"} />
          <Text style={[styles.placeholderTitle, { color: textColor }]}>
            {currentReport?.title || "Report"}
          </Text>
          <Text style={[styles.placeholderDescription, { color: secondaryTextColor }]}>
            {currentReport?.description || "Report data will be displayed here"}
          </Text>
        </View>
      </View>
    );
  };
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: currentReport?.title || "Report Details",
          headerStyle: {
            backgroundColor: isDarkMode ? "#1e1e1e" : colors.primary,
          },
          headerTintColor: "#fff",
          headerLeft: () => (
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)", marginLeft: 10 }]}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      
      <View style={[styles.header, { backgroundColor: cardColor, borderColor }]}>
        <View style={styles.headerContent}>
          <IconComponent size={32} color={currentReport?.color || "#666"} />
          <View style={styles.headerText}>
            <Text style={[styles.headerTitle, { color: textColor }]}>
              {currentReport?.title || "Report"}
            </Text>
            <Text style={[styles.headerSubtitle, { color: secondaryTextColor }]}>
              {selectedFarm?.name} • Generated {new Date().toLocaleDateString()}
            </Text>
          </View>
        </View>
        
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: "#1976D2" }]}
            onPress={() => handleExport("pdf")}
            disabled={exporting}
          >
            {exporting ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Download size={20} color="#fff" />
            )}
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: "#FF9800" }]}
            onPress={() => handleExport("excel")}
            disabled={exporting}
          >
            <Share size={20} color="#fff" />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: "#4CAF50" }]}
            onPress={handleSchedule}
          >
            <Calendar size={20} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>
      
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {loading ? (
          <View style={[styles.loadingContainer, { backgroundColor: cardColor }]}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.loadingText, { color: secondaryTextColor }]}>
              Loading report data...
            </Text>
          </View>
        ) : (
          renderReportContent()
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 24,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  headerText: {
    marginLeft: 12,
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
  },
  actionButtons: {
    flexDirection: "row",
    gap: 8,
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingContainer: {
    padding: 40,
    borderRadius: 12,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 12,
  },
  reportContent: {
    gap: 20,
  },
  summarySection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    gap: 12,
  },
  summaryCard: {
    width: "48%",
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryValue: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    textAlign: "center",
  },
  detailsSection: {
    marginBottom: 20,
  },
  statusItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  statusName: {
    fontSize: 16,
    fontWeight: "500",
    flex: 1,
  },
  statusCount: {
    fontSize: 14,
  },
  metricsCard: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  metricRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  metricLabel: {
    fontSize: 14,
    flex: 1,
  },
  metricValue: {
    fontSize: 16,
    fontWeight: "600",
  },
  placeholderCard: {
    padding: 40,
    borderRadius: 12,
    alignItems: "center",
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  placeholderTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginTop: 16,
    marginBottom: 8,
  },
  placeholderDescription: {
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
});
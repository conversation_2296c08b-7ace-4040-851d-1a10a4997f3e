import { useState, useEffect, useCallback } from "react";
import { StyleSheet, Text, View, FlatList, TouchableOpacity, TextInput, RefreshControl, ScrollView as RNScrollView } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter, Stack } from "expo-router";
import { FilterButton } from "@/components/FilterButton";
import { NoteCard } from "@/components/NoteCard";
import { Search, Plus, ArrowLeft } from "lucide-react-native";
import { getNotesByFarm, Note } from "@/services/notes-service";
import { useFarm } from "@/context/farm-context";
import { useAuth } from "@/context/auth-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useFocusEffect } from "@react-navigation/native";

export default function AdminNotesScreen() {
  const [notes, setNotes] = useState<Note[]>([]);
  const [filteredNotes, setFilteredNotes] = useState<Note[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeFilter, setActiveFilter] = useState("all");
  const router = useRouter();
  const { selectedFarm } = useFarm();
  const { user } = useAuth();
  const { theme, colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const isDarkMode = theme === "dark";

  // Theme colors
  const backgroundColor = isDarkMode ? "#0f0f0f" : "#f8fafc";
  const cardColor = isDarkMode ? "#1a1a1a" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#1e293b";
  const secondaryTextColor = isDarkMode ? "#94a3b8" : "#64748b";
  const borderColor = isDarkMode ? "#374151" : "#e2e8f0";
  const primaryColor = colors.primary;

  const loadNotes = useCallback(async () => {
    if (!selectedFarm) return;
    
    try {
      setLoading(true);
      console.log("Loading notes for farm:", selectedFarm.id);
      const data = await getNotesByFarm(selectedFarm.id);
      console.log("Loaded notes:", data.length);
      setNotes(data);
      setFilteredNotes(data);
    } catch (error) {
      console.error("Error loading notes:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [selectedFarm]);

  // Load notes when farm changes
  useEffect(() => {
    if (selectedFarm) {
      loadNotes();
    }
  }, [selectedFarm, loadNotes]);

  // Refresh notes when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      if (selectedFarm) {
        loadNotes();
      }
      return () => {};
    }, [selectedFarm, loadNotes])
  );

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    if (text) {
      const filtered = notes.filter(
        (note) =>
          note.title.toLowerCase().includes(text.toLowerCase()) ||
          note.content.toLowerCase().includes(text.toLowerCase()) ||
          (note.createdByName || note.userName || "").toLowerCase().includes(text.toLowerCase())
      );
      setFilteredNotes(filtered);
    } else {
      setFilteredNotes(notes);
    }
  };

  const applyFilter = (filter: string) => {
    setActiveFilter(filter);
    
    if (filter === "all") {
      setFilteredNotes(notes);
    } else if (filter === "withAudio") {
      setFilteredNotes(notes.filter((note) => note.hasAudio));
    } else if (filter === "withImages") {
      setFilteredNotes(notes.filter((note) => note.images && note.images.length > 0));
    } else if (filter === "text") {
      setFilteredNotes(notes.filter((note) => !note.hasAudio && (!note.images || note.images.length === 0)));
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadNotes();
  };

  const handleAddNote = () => {
    if (!selectedFarm) {
      return;
    }
    router.push("/notes/create");
  };

  const renderNoteItem = ({ item }: { item: Note }) => (
    <NoteCard
      note={item}
      onPress={() => router.push(`/notes/${item.id}?farmId=${selectedFarm?.id}`)}
      showEditOptions={false}
      isDarkMode={isDarkMode}
      role="admin"
      currentUserId={user?.uid}
    />
  );

  const renderEmptyComponent = () => {
    if (loading) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: secondaryTextColor }]}>
            {t("common.loading")}
          </Text>
        </View>
      );
    }
    
    if (!selectedFarm) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: secondaryTextColor }]}>
            {t("common.noFarmSelected")}
          </Text>
        </View>
      );
    }
    
    return (
      <View style={styles.emptyContainer}>
        <Text style={[styles.emptyText, { color: secondaryTextColor }]}>
          {searchQuery ? t("notes.noNotesFound") : t("notes.noNotes")}
        </Text>
        <TouchableOpacity 
          style={[styles.createNoteButton, { backgroundColor: primaryColor }]}
          onPress={handleAddNote}
        >
          <Text style={styles.createNoteText}>{t("notes.addNote")}</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t('notes.title'),
          headerStyle: {
            backgroundColor: primaryColor,
          },
          headerTintColor: "#fff",
          headerLeft: () => (
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)", marginLeft: 10 }]}
              onPress={() => router.push("/(app)/(admin)")}
            >
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      <View style={styles.searchContainer}>
        <View style={[styles.searchBar, { backgroundColor: cardColor, borderColor }]}>
          <Search size={20} color={secondaryTextColor} style={styles.searchIcon} />
          <TextInput
            style={[styles.searchInput, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}
            placeholder={t("notes.searchNotes")}
            placeholderTextColor={secondaryTextColor}
            value={searchQuery}
            onChangeText={handleSearch}
          />
        </View>
      </View>

      <View style={styles.filterContainer}>
        <RNScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.filterScroll}>
          <FilterButton
            label={t("notes.filters.all")}
            active={activeFilter === "all"}
            onPress={() => applyFilter("all")}
            isDarkMode={isDarkMode}
          />
          <FilterButton
            label={t("notes.filters.withAudio")}
            active={activeFilter === "withAudio"}
            onPress={() => applyFilter("withAudio")}
            isDarkMode={isDarkMode}
          />
          <FilterButton
            label={t("notes.filters.withImages")}
            active={activeFilter === "withImages"}
            onPress={() => applyFilter("withImages")}
            isDarkMode={isDarkMode}
          />
          <FilterButton
            label={t("notes.filters.textOnly")}
            active={activeFilter === "text"}
            onPress={() => applyFilter("text")}
            isDarkMode={isDarkMode}
          />
        </RNScrollView>
      </View>

      <FlatList
        data={filteredNotes}
        renderItem={renderNoteItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={renderEmptyComponent}
      />

      {/* Floating Action Button */}
      <TouchableOpacity
        style={[styles.fab, { backgroundColor: primaryColor }]}
        onPress={handleAddNote}
        disabled={!selectedFarm}
      >
        <Plus size={24} color="#fff" />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 44,
    fontSize: 16,
  },
  filterContainer: {
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  filterScroll: {
    paddingVertical: 8,
    gap: 8,
  },
  listContent: {
    padding: 16,
    paddingBottom: 100,
  },
  emptyContainer: {
    padding: 20,
    alignItems: "center",
  },
  emptyText: {
    fontSize: 16,
    marginBottom: 20,
  },
  createNoteButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createNoteText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  fab: {
    position: "absolute",
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    zIndex: 1000,
  },
});
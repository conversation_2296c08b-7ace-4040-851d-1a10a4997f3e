import { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Alert, ActivityIndicator, Image, Dimensions } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useLocalSearchParams, router } from "expo-router";
import { getFarmById, Farm } from "@/services/farm-service";
import { ArrowLeft, MapPin, Package, Calendar, Settings, CheckCircle, AlertCircle, Clock } from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useAuth } from "@/context/auth-context";

const { width } = Dimensions.get('window');



export default function AdminFarmDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { theme, colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const { user } = useAuth();
  
  const [farm, setFarm] = useState<Farm | null>(null);
  const [loading, setLoading] = useState(true);
  
  const isDarkMode = theme === "dark";
  const backgroundColor = isDarkMode ? "#121212" : "#f5f5f5";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";

  useEffect(() => {
    if (id) {
      loadFarmDetails();
    }
  }, [id]);

  const loadFarmDetails = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      
      // Load farm details
      const farmData = await getFarmById(id);
      setFarm(farmData);
      
    } catch (error) {
      console.error("Error loading farm details:", error);
      Alert.alert(t("common.error"), t("farm.loadError"));
    } finally {
      setLoading(false);
    }
  };

  const getLocationDisplay = (farm: Farm) => {
    const parts = [];
    if (farm.city) parts.push(farm.city);
    if (farm.state) parts.push(farm.state);
    if (farm.country) parts.push(farm.country);
    return parts.join(", ") || t("farm.locationNotSet");
  };

  const formatDate = (dateString: string | any): string => {
    if (!dateString) return t('common.notSet');
    try {
      let date: Date;
      if (typeof dateString === 'string') {
        date = new Date(dateString);
      } else if (dateString && typeof dateString === 'object' && dateString.toDate) {
        // Firestore timestamp
        date = dateString.toDate();
      } else if (dateString && typeof dateString === 'object' && dateString.seconds) {
        // Firestore timestamp with seconds
        date = new Date(dateString.seconds * 1000);
      } else {
        return t('common.invalidDate');
      }

      if (isNaN(date.getTime())) {
        return t('common.invalidDate');
      }

      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return t('common.invalidDate');
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'active': return colors.success;
      case 'inactive': return colors.warning;
      case 'maintenance': return colors.error;
      default: return colors.textSecondary;
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'active': return CheckCircle;
      case 'inactive': return AlertCircle;
      case 'maintenance': return Clock;
      default: return CheckCircle;
    }
  };



  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
        <Stack.Screen
          options={{
            title: t("farm.farmDetails"),
            headerStyle: { backgroundColor: colors.primary },
            headerTintColor: "#fff",
            headerLeft: () => (
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => router.back()}
              >
                <ArrowLeft size={24} color="#fff" />
              </TouchableOpacity>
            ),
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: textColor }]}>
            {t("common.loading")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!farm) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
        <Stack.Screen
          options={{
            title: t("farm.farmDetails"),
            headerStyle: { backgroundColor: colors.primary },
            headerTintColor: "#fff",
            headerLeft: () => (
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => router.back()}
              >
                <ArrowLeft size={24} color="#fff" />
              </TouchableOpacity>
            ),
          }}
        />
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: textColor }]}>
            {t("farm.notFound")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: farm.name,
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: "#fff",
          headerLeft: () => (
            <TouchableOpacity
              style={styles.headerButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Farm Photo */}
        {farm.photoURL && (
          <View style={styles.photoContainer}>
            <Image source={{ uri: farm.photoURL }} style={styles.farmPhoto} />
            <View style={[styles.photoOverlay, { backgroundColor: 'rgba(0, 0, 0, 0.3)' }]}>
              <View style={styles.photoInfo}>
                <Text style={styles.farmNameOverlay}>{farm.name}</Text>
                <View style={styles.statusContainer}>
                  {(() => {
                    const StatusIcon = getStatusIcon(farm.status);
                    return <StatusIcon size={16} color={getStatusColor(farm.status)} />;
                  })()}
                  <Text style={[styles.statusText, { color: getStatusColor(farm.status) }]}>
                    {farm.status?.charAt(0).toUpperCase() + farm.status?.slice(1) || t('common.unknown')}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        )}

        {/* Farm Info Card */}
        <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.cardTitle, { color: textColor }]}>
            {t("farm.farmInformation")}
          </Text>
          
          <View style={[styles.infoRow, isRTL && styles.rtlInfoRow]}>
            <MapPin size={20} color={colors.primary} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
                {t("farm.location")}
              </Text>
              <Text style={[styles.infoValue, { color: textColor }]}>
                {getLocationDisplay(farm)}
              </Text>
            </View>
          </View>

          {farm.size && (
            <View style={[styles.infoRow, isRTL && styles.rtlInfoRow]}>
              <Package size={20} color={colors.secondary} />
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
                  {t("farm.size")}
                </Text>
                <Text style={[styles.infoValue, { color: textColor }]}>
                  {farm.size} {farm.sizeUnit || t("farm.acres")}
                </Text>
              </View>
            </View>
          )}

          {farm.type && (
            <View style={[styles.infoRow, isRTL && styles.rtlInfoRow]}>
              <Settings size={20} color={colors.info} />
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
                  {t("farm.farmType")}
                </Text>
                <Text style={[styles.infoValue, { color: textColor }]}>
                  {t(`farm.${farm.type}Farm`) || farm.type.charAt(0).toUpperCase() + farm.type.slice(1)}
                </Text>
              </View>
            </View>
          )}

          <View style={[styles.infoRow, isRTL && styles.rtlInfoRow]}>
            <Calendar size={20} color={colors.warning} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
                {t("common.createdAt")}
              </Text>
              <Text style={[styles.infoValue, { color: textColor }]}>
                {formatDate(farm.createdAt)}
              </Text>
            </View>
          </View>
        </View>



        {/* Admin Notice */}
        {/* <View style={[styles.noticeCard, { backgroundColor: colors.warning + "20", borderColor: colors.warning }]}>
          <Text style={[styles.noticeText, { color: colors.warning }]}>
            {t("farm.adminViewOnly")}
          </Text>
        </View> */}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    marginLeft: 10,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  photoContainer: {
    position: 'relative',
    height: 200,
    marginBottom: 16,
  },
  farmPhoto: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  photoOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
  },
  photoInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  farmNameOverlay: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    flex: 1,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    textAlign: "center",
  },
  card: {
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 16,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "700",
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 16,
    gap: 12,
  },
  rtlInfoRow: {
    flexDirection: "row-reverse",
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: "400",
  },

  noticeCard: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    borderWidth: 1,
  },
  noticeText: {
    fontSize: 14,
    fontWeight: "500",
    textAlign: "center",
  },
});

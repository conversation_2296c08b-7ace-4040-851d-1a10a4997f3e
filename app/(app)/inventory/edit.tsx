import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Image,
  ActivityIndicator,
  Alert,
  Platform,
  KeyboardAvoidingView
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { useTheme } from '@/context/theme-context';
import { useLanguage } from '@/context/language-context';
import { useFarm } from '@/context/farm-context';
import { useAuth } from '@/context/auth-context';
import ModalDropdown from '@/components/ModalDropdown';
import {
  ArrowLeft,
  Camera,
  Upload,
  Calendar,
  ChevronDown,
  Truck,
  AlertTriangle,
  X,
  Save,
  Package,
  Info
} from 'lucide-react-native';
import { getInventoryItemById, updateInventoryItem, createInventoryItem, InventoryItem } from '@/services/inventory-service';
import * as ImagePicker from 'expo-image-picker';
import { uploadInventoryImage, deleteImageFromStorage } from '@/services/storage-service';
import DateTimePicker from '@react-native-community/datetimepicker';
import { LinearGradient } from 'expo-linear-gradient';
import { getCategories, getUnits } from '@/services/settings-service';

export default function EditInventoryItem() {
  const params = useLocalSearchParams();
  const router = useRouter();
  const { theme } = useTheme();
  const { t, isRTL, language } = useLanguage();
  const { selectedFarm } = useFarm();
  const { user } = useAuth();
  const isDarkMode = theme === 'dark';



  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [item, setItem] = useState<InventoryItem | null>(null);
  const [isNewItem, setIsNewItem] = useState(!params.id);
  const [userRole, setUserRole] = useState(params.role as string || user?.role || 'owner');



  // Predefined categories and units with proper structure
  const CATEGORY_OPTIONS = [
    { key: 'inventory.category_seeds', stored: 'Seeds' },
    { key: 'inventory.category_fertilizers', stored: 'Fertilizers' },
    { key: 'inventory.category_pesticides', stored: 'Pesticides' },
    { key: 'inventory.category_tools', stored: 'Tools' },
    { key: 'inventory.category_equipment', stored: 'Equipment' },
    { key: 'inventory.category_feed', stored: 'Feed' },
    { key: 'inventory.category_medication', stored: 'Medication' },
    { key: 'inventory.category_vaccination', stored: 'Vaccination' },
    { key: 'inventory.category_fuel', stored: 'Fuel' },
    { key: 'inventory.category_other_custom', stored: 'Other (Custom)' },
  ];

  const UNIT_OPTIONS = [
    { key: 'inventory.unit_kg', stored: 'kg' },
    { key: 'inventory.unit_g', stored: 'g' },
    { key: 'inventory.unit_l', stored: 'L' },
    { key: 'inventory.unit_ml', stored: 'mL' },
    { key: 'inventory.unit_units', stored: 'units' },
    { key: 'inventory.unit_bags', stored: 'bags' },
    { key: 'inventory.unit_boxes', stored: 'boxes' },
    { key: 'inventory.unit_bottles', stored: 'bottles' },
    { key: 'inventory.unit_packs', stored: 'packs' },
    { key: 'inventory.unit_pieces', stored: 'pieces' },
    { key: 'inventory.unit_other_custom', stored: 'Other (Custom)' },
  ];



  // Helper functions to convert between stored values and translated values
  const getTranslatedCategory = (storedCategory: string): string => {
    const option = CATEGORY_OPTIONS.find(opt => opt.stored === storedCategory);
    return option ? t(option.key) : storedCategory;
  };

  const getTranslatedUnit = (storedUnit: string): string => {
    const option = UNIT_OPTIONS.find(opt => opt.stored === storedUnit);
    return option ? t(option.key) : storedUnit;
  };

  const getStoredCategory = (translatedCategory: string): string => {
    const option = CATEGORY_OPTIONS.find(opt => t(opt.key) === translatedCategory);
    return option ? option.stored : translatedCategory;
  };

  const getStoredUnit = (translatedUnit: string): string => {
    const option = UNIT_OPTIONS.find(opt => t(opt.key) === translatedUnit);
    return option ? option.stored : translatedUnit;
  };

  // Form state
  const [formData, setFormData] = useState({
    name: params.name as string || '',
    category: params.category as string || '',
    quantity: params.quantity as string || '',
    unit: params.unit as string || '',
    minQuantity: params.minQuantity as string || '0',
    description: params.description as string || '',
    supplier: params.supplier as string || '',
    price: params.price as string || '',
    totalAmount: '',
    isConsumable: Boolean(params.isConsumable) || false,
    location: params.location as string || selectedFarm?.id || '',
  });

  // Image state
  const [imageUri, setImageUri] = useState<string | null>(params.imageUrl as string || null);

  // Date states
  const [expiryDate, setExpiryDate] = useState<Date | null>(
    params.expiryDate ? new Date(params.expiryDate as string) : null
  );
  const [purchaseDate, setPurchaseDate] = useState<Date | null>(
    params.purchaseDate ? new Date(params.purchaseDate as string) : null
  );
  const [showExpiryDatePicker, setShowExpiryDatePicker] = useState(false);
  const [showPurchaseDatePicker, setShowPurchaseDatePicker] = useState(false);

  // Calculate total amount
  const calculateTotalAmount = () => {
    const quantity = Number(formData.quantity) || 0;
    const unitPrice = Number(formData.price) || 0;
    return quantity * unitPrice;
  };

  // Update total amount whenever quantity or price changes
  useEffect(() => {
    const totalAmount = calculateTotalAmount();
    setFormData(prev => ({ ...prev, totalAmount: totalAmount.toString() }));
  }, [formData.quantity, formData.price]);

  // Dropdown states
  const [categoryDropdownVisible, setCategoryDropdownVisible] = useState(false);
  const [unitDropdownVisible, setUnitDropdownVisible] = useState(false);
  const [customCategory, setCustomCategory] = useState('');
  const [customUnit, setCustomUnit] = useState('');

  // Validation state
  const [formErrors, setFormErrors] = useState({
    name: '',
    category: '',
    quantity: '',
    unit: '',
    price: '',
    purchaseDate: '',
  });

  // Theme colors based on user role
  const getThemeColors = () => {
    const backgroundColor = isDarkMode ? '#121212' : '#f8f9fa';
    const cardColor = isDarkMode ? '#1e1e1e' : '#ffffff';
    const textColor = isDarkMode ? '#ffffff' : '#333333';
    const placeholderColor = isDarkMode ? '#aaaaaa' : '#888888';
    const borderColor = isDarkMode ? '#333333' : '#e0e0e0';

    // Set primary color based on role
    let primaryColor;
    if (userRole === 'caretaker') {
      primaryColor = '#FF9800'; // Orange for caretaker
    } else if (userRole === 'admin') {
      primaryColor = '#2196F3'; // Blue for admin
    } else {
      primaryColor = '#4CAF50'; // Green for owner/default
    }

    const errorColor = '#f44336';

    return {
      backgroundColor,
      cardColor,
      textColor,
      placeholderColor,
      borderColor,
      primaryColor,
      errorColor
    };
  };

  const colors = getThemeColors();

  // Check permissions - caretakers cannot edit inventory
  useEffect(() => {
    if (user?.role === 'caretaker') {
      Alert.alert(
        t('access_denied_title'),
        t('access_denied_inventory_edit_message'),
        [{ text: t('button_ok'), onPress: () => router.back() }]
      );
      return;
    }
  }, [user?.role]);

  // Load item data
  useEffect(() => {
    const loadItem = async () => {
      if (!params.id) {
        setIsNewItem(true);
        setLoading(false);
        return;
      }

      try {
        const itemData = await getInventoryItemById(params.id as string, selectedFarm?.id);
        if (itemData) {
          setItem(itemData);
          setIsNewItem(false);

          // Check if category is custom (stored value not in our predefined options)
          const isCategoryCustom = itemData.category && !CATEGORY_OPTIONS.find(opt => opt.stored === itemData.category);
          const isUnitCustom = itemData.unit && !UNIT_OPTIONS.find(opt => opt.stored === itemData.unit);

          // Initialize form data from item
          setFormData({
            name: itemData.name || '',
            category: isCategoryCustom ? t('inventory.category_other_custom') : getTranslatedCategory(itemData.category || ''),
            quantity: itemData.quantity ? itemData.quantity.toString() : '',
            unit: isUnitCustom ? t('inventory.unit_other_custom') : getTranslatedUnit(itemData.unit || ''),
            minQuantity: itemData.minQuantity ? itemData.minQuantity.toString() : '0',
            description: itemData.description || '',
            supplier: itemData.supplier || '',
            price: itemData.price ? itemData.price.toString() : '',
            totalAmount: itemData.price && itemData.quantity ? (itemData.price * itemData.quantity).toString() : '',
            isConsumable: Boolean((itemData as any).isConsumable) || false,
            location: itemData.location || selectedFarm?.id || '',
          });

          // Set image and dates
          setImageUri(itemData.imageUrl || null);
          setExpiryDate(itemData.expiryDate ? new Date(itemData.expiryDate) : null);
          setPurchaseDate(itemData.purchaseDate ? new Date(itemData.purchaseDate) : null);

          // Set custom values if they exist
          if (isCategoryCustom) {
            setCustomCategory(itemData.category);
          }

          if (isUnitCustom) {
            setCustomUnit(itemData.unit);
          }
        }
      } catch (error) {
        console.error('Error loading item:', error);
        Alert.alert(t('inventory.error_title'), t('inventory.error_failed_load_item_details'));
      } finally {
        setLoading(false);
      }
    };

    loadItem();
  }, [params.id, selectedFarm]);

  // Update form data when language changes
  useEffect(() => {
    if (formData.category || formData.unit) {
      // Find the stored values for current form data
      const currentCategoryStored = getStoredCategory(formData.category);
      const currentUnitStored = getStoredUnit(formData.unit);

      // Update form data with new translations
      setFormData(prev => ({
        ...prev,
        category: currentCategoryStored ? getTranslatedCategory(currentCategoryStored) : prev.category,
        unit: currentUnitStored ? getTranslatedUnit(currentUnitStored) : prev.unit,
      }));
    }
  }, [language]);

  // Handle form submission
  const handleSubmit = async () => {
    // Check permissions again before submitting
    if (user?.role === 'caretaker') {
      Alert.alert(t('access_denied_title'), t('access_denied_inventory_edit_message'));
      return;
    }

    // Validate form
    if (!validateForm()) {
      return;
    }

    if (!user) {
      Alert.alert(t('inventory.error_title'), t('inventory.error_must_be_logged_in_to_save'));
      return;
    }

    setSubmitting(true);

    try {
      // Determine final category and unit values
      let finalCategory: string;
      let finalUnit: string;

      // Handle category: use custom if provided, otherwise convert translated to stored value
      if (formData.category === t('inventory.category_other_custom') && customCategory) {
        finalCategory = customCategory;
      } else {
        finalCategory = getStoredCategory(formData.category || '');
      }

      // Handle unit: use custom if provided, otherwise convert translated to stored value
      if (formData.unit === t('inventory.unit_other_custom') && customUnit) {
        finalUnit = customUnit;
      } else {
        finalUnit = getStoredUnit(formData.unit || '');
      }

      console.log('Final values:', { finalCategory, finalUnit, customCategory, customUnit });

      let finalImageUrl = imageUri;

      // Upload image to Firebase Storage if a new local image is selected
      if (imageUri && imageUri.startsWith('file://')) {
        console.log("Uploading inventory image to Storage...");

        // Delete old image if it exists and is from Firebase Storage
        if (item?.imageUrl) {
          await deleteImageFromStorage(item.imageUrl);
        }

        // Upload new image
        const itemId = params.id as string || `temp_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
        finalImageUrl = await uploadInventoryImage(imageUri, itemId);
        console.log("Inventory image uploaded:", finalImageUrl);
      }

      // Create item data
      const itemData = {
        name: formData.name || '',
        category: finalCategory,
        quantity: parseInt(formData.quantity) || 0,
        unit: finalUnit,
        minQuantity: parseInt(formData.minQuantity) || 0,
        description: formData.description || undefined,
        supplier: formData.supplier || undefined,
        price: formData.price ? parseFloat(formData.price) : undefined,
        location: formData.location || selectedFarm?.id || '',
        imageUrl: finalImageUrl || undefined,
        expiryDate: expiryDate ? expiryDate.toISOString() : undefined,
        purchaseDate: purchaseDate ? purchaseDate.toISOString() : undefined,
        isConsumable: formData.isConsumable,
      };

      if (isNewItem) {
        // Create new item
        await createInventoryItem(itemData, user.uid);
        Alert.alert(t('inventory.success_title'), t('inventory.success_item_added_successfully'), [
          { text: t('common.ok'), onPress: () => router.back() }
        ]);
      } else {
        // Update existing item
        await updateInventoryItem(params.id as string, itemData, user.uid);
        Alert.alert(t('inventory.success_title'), t('inventory.success_item_updated_successfully'), [
          { text: t('common.ok'), onPress: () => router.back() }
        ]);
      }
    } catch (error) {
      console.error('Error saving item:', error);
      Alert.alert(t('inventory.error_title'), isNewItem ? t('inventory.error_failed_to_add_item') : t('inventory.error_failed_to_update_item'));
    } finally {
      setSubmitting(false);
    }
  };

  // Validate form
  const validateForm = () => {
    let valid = true;
    const errors = {
      name: '',
      category: '',
      quantity: '',
      unit: '',
      price: '',
      purchaseDate: '',
    };

    if (!formData.name || !formData.name.trim()) {
      errors.name = t('validation_item_name_required');
      valid = false;
    }

    // Category validation: check if category is selected or custom category is provided
    const hasCategory = formData.category && formData.category.trim();
    const hasCustomCategory = customCategory && customCategory.trim();
    const isCustomCategorySelected = formData.category === t('inventory.category_other_custom');

    if (!hasCategory) {
      errors.category = t('validation_category_required');
      valid = false;
    } else if (isCustomCategorySelected && !hasCustomCategory) {
      errors.category = t('inventory.enterCustomCategory');
      valid = false;
    }

    if (!formData.quantity || !formData.quantity.trim() || isNaN(Number(formData.quantity))) {
      errors.quantity = t('validation_valid_quantity_required');
      valid = false;
    } else if (Number(formData.quantity) < 0) {
      errors.quantity = t('validation_quantity_must_be_positive');
      valid = false;
    }

    // Unit validation: check if unit is selected or custom unit is provided
    const hasUnit = formData.unit && formData.unit.trim();
    const hasCustomUnit = customUnit && customUnit.trim();
    const isCustomUnitSelected = formData.unit === t('inventory.unit_other_custom');

    if (!hasUnit) {
      errors.unit = t('validation_unit_required');
      valid = false;
    } else if (isCustomUnitSelected && !hasCustomUnit) {
      errors.unit = t('inventory.enterCustomUnit');
      valid = false;
    }

    // Price validation (mandatory)
    if (!formData.price || isNaN(Number(formData.price)) || Number(formData.price) <= 0) {
      errors.price = t('inventory.validation.priceRequired');
      valid = false;
    }

    // Purchase date validation (mandatory)
    if (!purchaseDate) {
      errors.purchaseDate = t('inventory.validation.purchaseDateRequired');
      valid = false;
    }

    setFormErrors(errors);
    return valid;
  };

  // Handle category selection
  const handleCategorySelect = (category: string) => {
    console.log('Category selected:', category);
    console.log('Custom category translation:', t('inventory.category_other_custom'));

    if (category === t('inventory.category_other_custom')) {
      // User selected "Other (Custom)" - clear form category and enable custom input
      setFormData({ ...formData, category: t('inventory.category_other_custom') });
      setCustomCategory('');
    } else {
      // User selected a predefined category
      setFormData({ ...formData, category });
      setCustomCategory('');
    }
    setCategoryDropdownVisible(false);
  };

  // Handle unit selection
  const handleUnitSelect = (unit: string) => {
    console.log('Unit selected:', unit);
    console.log('Custom unit translation:', t('inventory.unit_other_custom'));

    if (unit === t('inventory.unit_other_custom')) {
      // User selected "Other (Custom)" - clear form unit and enable custom input
      setFormData({ ...formData, unit: t('inventory.unit_other_custom') });
      setCustomUnit('');
    } else {
      // User selected a predefined unit
      setFormData({ ...formData, unit });
      setCustomUnit('');
    }
    setUnitDropdownVisible(false);
  };

  // Handle custom category input
  const handleCustomCategoryChange = (text: string) => {
    setCustomCategory(text);
    // Don't update formData.category here - keep it as "Other (Custom)" for validation
  };

  // Handle custom unit input
  const handleCustomUnitChange = (text: string) => {
    setCustomUnit(text);
    // Don't update formData.unit here - keep it as "Other (Custom)" for validation
  };

  // Pick image from gallery
  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });

      if (!result.canceled) {
        setImageUri(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(t('inventory.error_title'), t('inventory.error_failed_to_pick_image'));
    }
  };

  // Take photo with camera
  const takePhoto = async () => {
    if (Platform.OS === 'web') {
      Alert.alert(t('not_available_title'), t('camera_not_available_web_message'));
      return;
    }

    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(t('permission_required_title'), t('camera_permission_required_message'));
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });

      if (!result.canceled) {
        setImageUri(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert(t('inventory.error_title'), t('inventory.error_failed_to_take_photo'));
    }
  };

  // Date picker handlers
  const onExpiryDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || expiryDate;
    setShowExpiryDatePicker(Platform.OS === 'ios');
    if (currentDate) {
      setExpiryDate(currentDate);
    }
  };

  const onPurchaseDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || purchaseDate;
    setShowPurchaseDatePicker(Platform.OS === 'ios');
    if (currentDate) {
      setPurchaseDate(currentDate);
      if (formErrors.purchaseDate) setFormErrors({ ...formErrors, purchaseDate: '' });
    }
  };

  // Format date for display
  const formatDate = (date: Date | null) => {
    if (!date) return '';
    return date.toLocaleDateString();
  };

  const getCategoryIcon = (category: string) => {
    const lowerCategory = category.toLowerCase();
    if (lowerCategory.includes('seed') || lowerCategory.includes(t('category_seeds').toLowerCase())) return "🌱";
    if (lowerCategory.includes('fertilizer') || lowerCategory.includes(t('category_fertilizers').toLowerCase())) return "🧪";
    if (lowerCategory.includes('tool') || lowerCategory.includes(t('category_tools').toLowerCase())) return "🔧";
    if (lowerCategory.includes('pesticide') || lowerCategory.includes(t('category_pesticides').toLowerCase())) return "🚫";
    if (lowerCategory.includes('equipment') || lowerCategory.includes(t('category_equipment').toLowerCase())) return "⚙️";
    if (lowerCategory.includes('feed') || lowerCategory.includes(t('category_feed').toLowerCase())) return "🌾";
    if (lowerCategory.includes('medication') || lowerCategory.includes(t('category_medication').toLowerCase())) return "💊";
    if (lowerCategory.includes('vaccination') || lowerCategory.includes(t('category_vaccination').toLowerCase())) return "💉";
    if (lowerCategory.includes('fuel') || lowerCategory.includes(t('category_fuel').toLowerCase())) return "⛽";
    return "📦";
  };

  // Show access denied for caretakers
  if (user?.role === 'caretaker') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundColor }]}>
        <Stack.Screen
          options={{
            title: isNewItem ? t('inventory.addItem') : t('inventory.editItem'),
            headerStyle: {
              backgroundColor: colors.primaryColor,
            },
            headerTintColor: '#fff',
            headerLeft: () => (
              <TouchableOpacity onPress={() => router.back()} style={{ marginLeft: 16 }}>
                <ArrowLeft size={24} color="#fff" />
              </TouchableOpacity>
            ),
          }}
        />
        <View style={styles.accessDeniedContainer}>
          <AlertTriangle size={64} color={colors.errorColor} />
          <Text style={[styles.accessDeniedTitle, { color: colors.textColor }]}>
            {t('common.error')}
          </Text>
          <Text style={[styles.accessDeniedText, { color: colors.placeholderColor }]}>
            {t('inventory.accessDenied')}
          </Text>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.primaryColor }]}
            onPress={() => router.back()}
          >
            <Text style={styles.backButtonText}>{t('common.back')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundColor }]}>
        <Stack.Screen
          options={{
            title: isNewItem ? t('inventory.addItem') : t('inventory.editItem'),
            headerStyle: {
              backgroundColor: colors.primaryColor,
            },
            headerTintColor: '#fff',
            headerLeft: () => (
              <TouchableOpacity onPress={() => router.back()} style={{ marginLeft: 16 }}>
                <ArrowLeft size={24} color="#fff" />
              </TouchableOpacity>
            ),
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primaryColor} />
          <Text style={[styles.loadingText, { color: colors.textColor }]}>
            {t('common.loading')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: isNewItem ? t('inventory.addItem') : t('inventory.editItem'),
          headerStyle: {
            backgroundColor: colors.primaryColor,
          },
          headerTintColor: '#fff',
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} style={{ marginLeft: 16 }}>
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.content}>
            {/* Header Banner */}
            <LinearGradient
              colors={isDarkMode
                ? [colors.primaryColor, `${colors.primaryColor}80`]
                : [`${colors.primaryColor}20`, `${colors.primaryColor}10`]
              }
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.headerBanner}
            >
              <View style={styles.headerContent}>
                <Package size={32} color={isDarkMode ? '#fff' : colors.primaryColor} />
                <View style={styles.headerText}>
                  <Text style={[styles.headerTitle, { color: isDarkMode ? '#fff' : colors.primaryColor }]}>
                    {isNewItem ? t('inventory.addItem') : t('inventory.editItem')}
                  </Text>
                  <Text style={[styles.headerSubtitle, { color: isDarkMode ? '#eee' : colors.primaryColor + 'CC' }]}>
                    {isNewItem
                      ? t('inventory.addItemDescription')
                      : t('inventory.updateItemDescription', { name: formData.name || t('common.thisItem') })}
                  </Text>
                </View>
              </View>
            </LinearGradient>

            {/* Image Picker */}
            <View style={styles.imagePickerContainer}>
              <Text style={[styles.sectionTitle, {
                color: colors.textColor,
                textAlign: isRTL ? 'right' : 'left'
              }]}>
                {t('inventory.itemImage')}
              </Text>
              {imageUri ? (
                <View style={styles.selectedImageContainer}>
                  <Image source={{ uri: imageUri }} style={styles.selectedImage} />
                  <LinearGradient
                    colors={['transparent', 'rgba(0,0,0,0.7)']}
                    style={styles.imageOverlay}
                  />
                  <View style={styles.imageActions}>
                    <TouchableOpacity
                      style={[styles.imageActionButton, { backgroundColor: colors.primaryColor }]}
                      onPress={pickImage}
                    >
                      <Text style={styles.imageActionText}>{t('inventory.button_change')}</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.imageActionButton, { backgroundColor: colors.errorColor }]}
                      onPress={() => setImageUri(null)}
                    >
                      <Text style={styles.imageActionText}>{t('inventory.button_remove')}</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : (
                <View style={[styles.imagePlaceholder, { borderColor: colors.borderColor }]}>
                  <Text style={styles.categoryEmoji}>{getCategoryIcon(formData.category || 'other')}</Text>
                  <Text style={[styles.imagePlaceholderText, {
                    color: colors.placeholderColor,
                    textAlign: isRTL ? 'right' : 'left'
                  }]}>
                    {t('inventory.uploadImage')}
                  </Text>
                  <View style={[styles.imageButtonsContainer, {
                    flexDirection: isRTL ? 'row-reverse' : 'row'
                  }]}>
                    <TouchableOpacity
                      style={[styles.imageButton, {
                        backgroundColor: colors.primaryColor,
                        flexDirection: isRTL ? 'row-reverse' : 'row'
                      }]}
                      onPress={pickImage}
                    >
                      <Upload size={20} color="#fff" style={{
                        marginLeft: isRTL ? 8 : 0,
                        marginRight: isRTL ? 0 : 8
                      }} />
                      <Text style={[styles.imageButtonText, {
                        textAlign: isRTL ? 'right' : 'left'
                      }]}>{t('inventory.uploadImage')}</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[styles.imageButton, {
                        backgroundColor: colors.primaryColor,
                        flexDirection: isRTL ? 'row-reverse' : 'row'
                      }]}
                      onPress={takePhoto}
                    >
                      <Camera size={20} color="#fff" style={{
                        marginLeft: isRTL ? 8 : 0,
                        marginRight: isRTL ? 0 : 8
                      }} />
                      <Text style={[styles.imageButtonText, {
                        textAlign: isRTL ? 'right' : 'left'
                      }]}>{t('common.camera')}</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            </View>

            {/* Form Card */}
            <View style={[styles.formCard, { backgroundColor: colors.cardColor, borderColor: colors.borderColor }]}>
              <Text style={[styles.formSectionTitle, {
                color: colors.textColor,
                textAlign: isRTL ? 'right' : 'left'
              }]}>
                {t('inventory.basicInformation')}
              </Text>

              {/* Name */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, {
                  color: colors.textColor,
                  textAlign: isRTL ? 'right' : 'left'
                }]}>
                  {t('inventory.itemName')} *
                </Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: colors.textColor,
                      borderColor: formErrors.name ? colors.errorColor : colors.borderColor,
                      backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                      textAlign: isRTL ? 'right' : 'left',
                      writingDirection: isRTL ? 'rtl' : 'ltr'
                    }
                  ]}
                  placeholder={t('inventory.enterItemName')}
                  placeholderTextColor={colors.placeholderColor}
                  value={formData.name}
                  onChangeText={(text) => setFormData({ ...formData, name: text })}
                />
                {formErrors.name ? (
                  <Text style={[styles.errorText, {
                    color: colors.errorColor,
                    textAlign: isRTL ? 'right' : 'left'
                  }]}>
                    {formErrors.name}
                  </Text>
                ) : null}
              </View>

              {/* Category */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, {
                  color: colors.textColor,
                  textAlign: isRTL ? 'right' : 'left'
                }]}>
                  {t('inventory.category')} *
                </Text>
                <ModalDropdown
                  key={`category-${language}`}
                  items={CATEGORY_OPTIONS.map(option => {
                    const translatedLabel = t(option.key);
                    return {
                      label: translatedLabel,
                      value: translatedLabel,
                      icon: getCategoryIcon(option.stored),
                    };
                  })}
                  selectedValue={formData.category}
                  onValueChange={(value) => {
                    handleCategorySelect(value);
                  }}
                  placeholder={t('inventory.selectCategory')}
                  error={!!formErrors.category}
                />

                {(formData.category === t('inventory.category_other_custom') || customCategory) && (
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: colors.textColor,
                        borderColor: formErrors.category ? colors.errorColor : colors.borderColor,
                        backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                        marginTop: 8,
                        textAlign: isRTL ? 'right' : 'left',
                        writingDirection: isRTL ? 'rtl' : 'ltr'
                      }
                    ]}
                    placeholder={t('inventory.enterCustomCategory')}
                    placeholderTextColor={colors.placeholderColor}
                    value={customCategory}
                    onChangeText={handleCustomCategoryChange}
                  />
                )}

                {formErrors.category ? (
                  <Text style={[styles.errorText, {
                    color: colors.errorColor,
                    textAlign: isRTL ? 'right' : 'left'
                  }]}>
                    {formErrors.category}
                  </Text>
                ) : null}
              </View>

              <Text style={[styles.formSectionTitle, {
                color: colors.textColor,
                marginTop: 16,
                textAlign: isRTL ? 'right' : 'left'
              }]}>
                {t('inventory.additionalDetails')}
              </Text>

              {/* Quantity and Unit */}
              <View style={[styles.formRow, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <View style={[styles.formGroup, {
                  flex: 1,
                  marginRight: isRTL ? 0 : 8,
                  marginLeft: isRTL ? 8 : 0
                }]}>
                  <Text style={[styles.label, {
                    color: colors.textColor,
                    textAlign: isRTL ? 'right' : 'left'
                  }]}>
                    {t('inventory.quantity')} *
                  </Text>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: colors.textColor,
                        borderColor: formErrors.quantity ? colors.errorColor : colors.borderColor,
                        backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                        textAlign: isRTL ? 'right' : 'left',
                        writingDirection: isRTL ? 'rtl' : 'ltr'
                      }
                    ]}
                    placeholder="0"
                    placeholderTextColor={colors.placeholderColor}
                    value={formData.quantity}
                    onChangeText={(text) => setFormData({ ...formData, quantity: text })}
                    keyboardType="numeric"
                  />
                  {formErrors.quantity ? (
                    <Text style={[styles.errorText, {
                      color: colors.errorColor,
                      textAlign: isRTL ? 'right' : 'left'
                    }]}>
                      {formErrors.quantity}
                    </Text>
                  ) : null}
                </View>

                <View style={[styles.formGroup, { flex: 1 }]}>
                  <Text style={[styles.label, {
                    color: colors.textColor,
                    textAlign: isRTL ? 'right' : 'left'
                  }]}>
                    {t('inventory.unit')} *
                  </Text>
                  <ModalDropdown
                    key={`unit-${language}`}
                    items={UNIT_OPTIONS.map(option => {
                      const translatedLabel = t(option.key);
                      return {
                        label: translatedLabel,
                        value: translatedLabel,
                      };
                    })}
                    selectedValue={formData.unit}
                    onValueChange={(value) => {
                      handleUnitSelect(value);
                    }}
                    placeholder={t('inventory.selectUnit')}
                    error={!!formErrors.unit}
                  />

                  {(formData.unit === t('inventory.unit_other_custom') || customUnit) && (
                    <TextInput
                      style={[
                        styles.input,
                        {
                          color: colors.textColor,
                          borderColor: formErrors.unit ? colors.errorColor : colors.borderColor,
                          backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                          marginTop: 8,
                          textAlign: isRTL ? 'right' : 'left',
                          writingDirection: isRTL ? 'rtl' : 'ltr',
                          width:"auto"
                        }
                      ]}
                      placeholder={t('inventory.enterCustomUnit')}
                      placeholderTextColor={colors.placeholderColor}
                      value={customUnit}
                      onChangeText={handleCustomUnitChange}
                    />
                  )}

                  {formErrors.unit ? (
                    <Text style={[styles.errorText, {
                      color: colors.errorColor,
                      textAlign: isRTL ? 'right' : 'left'
                    }]}>
                      {formErrors.unit}
                    </Text>
                  ) : null}
                </View>
              </View>

              {/* Min Quantity */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, {
                  color: colors.textColor,
                  textAlign: isRTL ? 'right' : 'left'
                }]}>
                  {t('inventory.minimumQuantity')}
                </Text>
                <View style={[styles.inputWithIcon, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                  <AlertTriangle size={20} color={colors.placeholderColor} style={[
                    styles.inputIcon,
                    { marginLeft: isRTL ? 8 : 0, marginRight: isRTL ? 0 : 8 }
                  ]} />
                  <TextInput
                    style={[
                      styles.inputWithPadding,
                      {
                        color: colors.textColor,
                        borderColor: colors.borderColor,
                        backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                        textAlign: isRTL ? 'right' : 'left',
                        writingDirection: isRTL ? 'rtl' : 'ltr'
                      }
                    ]}
                    placeholder="0"
                    placeholderTextColor={colors.placeholderColor}
                    value={formData.minQuantity}
                    onChangeText={(text) => setFormData({ ...formData, minQuantity: text })}
                    keyboardType="numeric"
                  />
                </View>
                <Text style={[styles.helpText, {
                  color: colors.placeholderColor,
                  textAlign: isRTL ? 'right' : 'left'
                }]}>
                  {t('form_help_text_low_stock_notification_help')}
                </Text>
              </View>

              <Text style={[styles.formSectionTitle, {
                color: colors.textColor,
                marginTop: 16,
                textAlign: isRTL ? 'right' : 'left'
              }]}>
                {t('inventory.additionalDetails')}
              </Text>

              {/* Dates */}
              <View style={[styles.formRow, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <View style={[styles.formGroup, {
                  flex: 1,
                  marginRight: isRTL ? 0 : 8,
                  marginLeft: isRTL ? 8 : 0
                }]}>
                  <Text style={[styles.label, {
                    color: colors.textColor,
                    textAlign: isRTL ? 'right' : 'left'
                  }]}>
                    {t('inventory.purchaseDate')} *
                  </Text>
                  <TouchableOpacity
                    style={[
                      styles.dateButton,
                      {
                        borderColor: formErrors.purchaseDate ? '#F44336' : colors.borderColor,
                        backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                        flexDirection: isRTL ? 'row-reverse' : 'row'
                      }
                    ]}
                    onPress={() => setShowPurchaseDatePicker(true)}
                  >
                    <Text
                      style={{
                        color: purchaseDate ? colors.textColor : colors.placeholderColor,
                        textAlign: isRTL ? 'right' : 'left',
                        flex: 1
                      }}
                    >
                      {purchaseDate ? formatDate(purchaseDate) : t('inventory.selectExpiryDate')}
                    </Text>
                    <Calendar size={20} color={colors.placeholderColor} />
                  </TouchableOpacity>
                  {showPurchaseDatePicker && (
                    <DateTimePicker
                      value={purchaseDate || new Date()}
                      mode="date"
                      display={Platform.OS === "ios" ? "spinner" : "default"}
                      onChange={onPurchaseDateChange}
                    />
                  )}
                  {formErrors.purchaseDate && (
                    <Text style={styles.errorText}>{formErrors.purchaseDate}</Text>
                  )}
                </View>

                <View style={[styles.formGroup, { flex: 1 }]}>
                  <Text style={[styles.label, {
                    color: colors.textColor,
                    textAlign: isRTL ? 'right' : 'left'
                  }]}>
                    {t('inventory.expiryDate')}
                  </Text>
                  <TouchableOpacity
                    style={[
                      styles.dateButton,
                      {
                        borderColor: colors.borderColor,
                        backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                        flexDirection: isRTL ? 'row-reverse' : 'row'
                      }
                    ]}
                    onPress={() => setShowExpiryDatePicker(true)}
                  >
                    <Text
                      style={{
                        color: expiryDate ? colors.textColor : colors.placeholderColor,
                        textAlign: isRTL ? 'right' : 'left',
                        flex: 1
                      }}
                    >
                      {expiryDate ? formatDate(expiryDate) : t('inventory.selectExpiryDate')}
                    </Text>
                    <Calendar size={20} color={colors.placeholderColor} />
                  </TouchableOpacity>
                  {showExpiryDatePicker && (
                    <DateTimePicker
                      value={expiryDate || new Date()}
                      mode="date"
                      display={Platform.OS === "ios" ? "spinner" : "default"}
                      onChange={onExpiryDateChange}
                    />
                  )}
                </View>
              </View>

              {/* Supplier */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, {
                  color: colors.textColor,
                  textAlign: isRTL ? 'right' : 'left'
                }]}>
                  {t('inventory.supplier')}
                </Text>
                <View style={[styles.inputWithIcon, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                  <Truck size={20} color={colors.placeholderColor} style={[
                    styles.inputIcon,
                    { marginLeft: isRTL ? 8 : 0, marginRight: isRTL ? 0 : 8 }
                  ]} />
                  <TextInput
                    style={[
                      styles.inputWithPadding,
                      {
                        color: colors.textColor,
                        borderColor: colors.borderColor,
                        backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                        textAlign: isRTL ? 'right' : 'left',
                        writingDirection: isRTL ? 'rtl' : 'ltr'
                      }
                    ]}
                    placeholder={t('inventory.enterSupplier')}
                    placeholderTextColor={colors.placeholderColor}
                    value={formData.supplier}
                    onChangeText={(text) => setFormData({ ...formData, supplier: text })}
                  />
                </View>
              </View>

              {/* Unit Price */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, {
                  color: colors.textColor,
                  textAlign: isRTL ? 'right' : 'left'
                }]}>
                  {t('inventory.unitPrice')} *
                </Text>
                <View style={[styles.inputWithIcon, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                  <Package size={20} color={colors.placeholderColor} style={[
                    styles.inputIcon,
                    { marginLeft: isRTL ? 8 : 0, marginRight: isRTL ? 0 : 8 }
                  ]} />
                  <TextInput
                    style={[
                      styles.inputWithPadding,
                      {
                        color: colors.textColor,
                        borderColor: formErrors.price ? '#F44336' : colors.borderColor,
                        backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                        textAlign: isRTL ? 'right' : 'left',
                        writingDirection: isRTL ? 'rtl' : 'ltr'
                      }
                    ]}
                    placeholder="Rs 0"
                    placeholderTextColor={colors.placeholderColor}
                    value={formData.price}
                    onChangeText={(text) => {
                      setFormData({ ...formData, price: text });
                      if (formErrors.price) setFormErrors({ ...formErrors, price: '' });
                    }}
                    keyboardType="numeric"
                  />
                </View>
                {formErrors.price && (
                  <Text style={styles.errorText}>{formErrors.price}</Text>
                )}
              </View>

              {/* Total Amount (Auto-calculated, Disabled) */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, {
                  color: colors.textColor,
                  textAlign: isRTL ? 'right' : 'left'
                }]}>
                  {t('inventory.totalAmount')}
                </Text>
                <View style={[styles.inputWithIcon, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                  <Package size={20} color={colors.placeholderColor} style={[
                    styles.inputIcon,
                    { marginLeft: isRTL ? 8 : 0, marginRight: isRTL ? 0 : 8 }
                  ]} />
                  <TextInput
                    style={[
                      styles.inputWithPadding,
                      styles.disabledInput,
                      {
                        color: colors.placeholderColor,
                        borderColor: colors.borderColor,
                        backgroundColor: isDarkMode ? '#2a2a2a' : '#f0f0f0',
                        textAlign: isRTL ? 'right' : 'left',
                        writingDirection: isRTL ? 'rtl' : 'ltr'
                      }
                    ]}
                    value={formData.totalAmount ? `Rs ${Number(formData.totalAmount).toLocaleString()}` : 'Rs 0'}
                    editable={false}
                    placeholder="Rs 0"
                    placeholderTextColor={colors.placeholderColor}
                  />
                </View>
              </View>

              {/* Consumable Checkbox */}
              <View style={styles.formGroup}>
                <TouchableOpacity
                  style={styles.checkboxContainer}
                  onPress={() => setFormData({ ...formData, isConsumable: !formData.isConsumable })}
                >
                  <View style={[
                    styles.checkbox,
                    {
                      backgroundColor: formData.isConsumable ? colors.primaryColor : 'transparent',
                      borderColor: formData.isConsumable ? colors.primaryColor : colors.borderColor
                    }
                  ]}>
                    {formData.isConsumable && (
                      <Text style={styles.checkmark}>✓</Text>
                    )}
                  </View>
                  <Text style={[styles.checkboxLabel, { color: colors.textColor }]}>
                    {t('inventory.isConsumable')}
                  </Text>
                </TouchableOpacity>
                <Text style={[styles.checkboxDescription, { color: colors.placeholderColor }]}>
                  {t('inventory.consumableDescription')}
                </Text>
              </View>

              {/* Description */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, {
                  color: colors.textColor,
                  textAlign: isRTL ? 'right' : 'left'
                }]}>
                  {t('inventory.description')}
                </Text>
                <TextInput
                  style={[
                    styles.textArea,
                    {
                      color: colors.textColor,
                      borderColor: colors.borderColor,
                      backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                      textAlign: isRTL ? 'right' : 'left',
                      writingDirection: isRTL ? 'rtl' : 'ltr'
                    }
                  ]}
                  placeholder={t('inventory.enterDescription')}
                  placeholderTextColor={colors.placeholderColor}
                  value={formData.description}
                  onChangeText={(text) => setFormData({ ...formData, description: text })}
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                />
              </View>
            </View>

            <View style={[styles.buttonContainer, {
              flexDirection: isRTL ? 'row-reverse' : 'row'
            }]}>
              <TouchableOpacity
                style={[
                  styles.button,
                  styles.cancelButton,
                  {
                    borderColor: colors.borderColor,
                    backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                  }
                ]}
                onPress={() => router.back()}
              >
                <Text style={[styles.buttonText, {
                  color: colors.textColor,
                  textAlign: isRTL ? 'right' : 'left'
                }]}>
                  {t('common.cancel')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.saveButton, {
                  backgroundColor: colors.primaryColor,
                  flexDirection: isRTL ? 'row-reverse' : 'row'
                }]}
                onPress={handleSubmit}
                disabled={submitting}
              >
                {submitting ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <Save size={20} color="#fff" style={{
                      marginLeft: isRTL ? 8 : 0,
                      marginRight: isRTL ? 0 : 8
                    }} />
                    <Text style={[styles.saveButtonText, {
                      textAlign: isRTL ? 'right' : 'left'
                    }]}>
                      {isNewItem ? t('inventory.createItem') : t('common.save')}
                    </Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  accessDeniedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  accessDeniedTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  accessDeniedText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  backButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom: 32,
  },
  headerBanner: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  headerText: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "bold",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 12,
  },
  formCard: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  formSectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  imagePickerContainer: {
    marginBottom: 20,
  },
  imagePlaceholder: {
    height: 200,
    borderRadius: 16,
    borderWidth: 2,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  categoryEmoji: {
    fontSize: 48,
    marginBottom: 12,
  },
  imagePlaceholderText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 22,
  },
  imageButtonsContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  imageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 12,
    gap: 8,
  },
  imageButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  selectedImageContainer: {
    position: 'relative',
    borderRadius: 16,
    overflow: 'hidden',
    height: 200,
  },
  selectedImage: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  imageActions: {
    position: 'absolute',
    bottom: 12,
    left: 12,
    right: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 12,
  },
  imageActionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
  },
  imageActionText: {
    color: '#fff',
    fontWeight: '600',
  },
  formGroup: {
    marginBottom: 20,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  inputWithIcon: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputIcon: {
    position: 'absolute',
    left: 16,
    zIndex: 1,
  },
  inputWithPadding: {
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingLeft: 48,
    fontSize: 16,
    flex: 1,
  },
  textArea: {
    height: 100,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingTop: 12,
    fontSize: 16,
  },
  helpText: {
    fontSize: 12,
    marginTop: 4,
    fontStyle: 'italic',
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  dropdownButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
  },
  dropdownContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  dropdownButtonText: {
    fontSize: 16,
  },
  dropdown: {
    borderWidth: 1,
    borderRadius: 12,
    zIndex: 1000,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    gap: 12,
  },
  selectedDropdownItem: {
    backgroundColor: 'rgba(25, 118, 210, 0.1)',
  },
  dropdownItemText: {
    fontSize: 16,
  },
  dateButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  button: {
    flex: 1,
    height: 52,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  cancelButton: {
    marginRight: 8,
    borderWidth: 1,
  },
  saveButton: {
    marginLeft: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: '600',
    minWidth: 25,
    marginLeft: 8,
    marginRight: 8,
  },
  totalPriceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
    paddingHorizontal: 4,
  },
  totalPriceLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  totalPriceValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  disabledInput: {
    opacity: 0.6,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderRadius: 4,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  checkboxLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  checkboxDescription: {
    fontSize: 14,
    marginTop: 4,
    fontStyle: 'italic',
  },
  dateButtonText: {
    flex: 1,
    fontSize: 16,
  },
  clearDateButton: {
    padding: 4,
  },
});

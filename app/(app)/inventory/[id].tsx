import React, { useState, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  ScrollView, 
  TouchableOpacity, 
  ActivityIndicator, 
  Alert, 
  Image,
  Dimensions,
  Platform,
  RefreshControl,
  TextInput,
  Animated
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { getInventoryItemById, updateInventoryItem, deleteInventoryItem, InventoryItem } from '@/services/inventory-service';
import { useTheme } from '@/context/theme-context';
import { useFarm } from '@/context/farm-context';
import { useAuth } from '@/context/auth-context';
import { useLanguage } from '@/context/language-context';
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  AlertTriangle, 
  Calendar, 
  Package, 
  MapPin, 
  DollarSign, 
  Truck,
  Clock,
  User,
  ChevronRight,

  TrendingDown,
  TrendingUp,
  Info,
  CheckCircle2,
  XCircle,
  Send
} from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

import { AlertBanner } from '@/components/AlertBanner';

export default function InventoryItemDetails() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const { colors } = useTheme();
  const { selectedFarm, farms } = useFarm();
  const { user } = useAuth();
  const { t, isRTL } = useLanguage();
  
  const [item, setItem] = useState<InventoryItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));
  
  const screenWidth = Dimensions.get('window').width;
  const isDarkMode = colors.background === '#121212';
  
  useEffect(() => {
    loadItemDetails();
  }, [id, selectedFarm]);

  useEffect(() => {
    if (item) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [item]);
  
  const loadItemDetails = async () => {
    try {
      if (!id) return;
      
      const farmId = selectedFarm?.id;
      const itemData = await getInventoryItemById(id.toString(), farmId);
      setItem(itemData);
    } catch (error) {
      console.error('Error loading item details:', error);
      Alert.alert(t('common.error'), t('inventory.loadError'));
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  const onRefresh = () => {
    setRefreshing(true);
    loadItemDetails();
  };
  
  const handleEdit = () => {
    if (!item) return;
    
    router.push({
      pathname: '/inventory/edit',
      params: { 
        id: id,
        name: item.name,
        category: item.category || "",
        quantity: item.quantity.toString(),
        unit: item.unit,
        minQuantity: item.minQuantity ? item.minQuantity.toString() : "0",
        description: item.description || "",
        supplier: item.supplier || "",
        price: item.price ? item.price.toString() : "",
        expiryDate: item.expiryDate || "",
        purchaseDate: item.purchaseDate || "",
        imageUrl: item.imageUrl || "",
        location: item.location
      }
    });
  };
  
  const handleDelete = () => {
    Alert.alert(
      t('common.deleteItem'),
      t('common.deleteItemConfirmation'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('common.delete'), style: 'destructive', onPress: confirmDelete }
      ]
    );
  };
  
  const confirmDelete = async () => {
    if (!item) return;
    
    try {
      setDeleting(true);
      await deleteInventoryItem(id as string, item.location);
      Alert.alert(t('common.success'), t('common.itemDeletedSuccessfully'));
      router.back();
    } catch (error) {
      console.error('Error deleting item:', error);
      Alert.alert(t('common.error'), t('common.failedToDeleteItem'));
      setDeleting(false);
    }
  };
  


  const handleRequestTransfer = () => {
    if (!item) return;
    
    router.push({
      pathname: '/request/create-inventory',
      params: { 
        itemId: item.id,
        itemName: item.name,
        currentQuantity: item.quantity.toString(),
        unit: item.unit
      }
    });
  };
  
  const isLowStock = item && item.minQuantity !== undefined && item.quantity <= item.minQuantity;
  const isExpiringSoon = item && item.expiryDate ? new Date(item.expiryDate) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) : false;
  const isExpired = item && item.expiryDate ? new Date(item.expiryDate) < new Date() : false;
  
  const formatDate = (dateString?: string) => {
    if (!dateString) return t('common.notSet');
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
  };
  
  const getFarmName = (farmId?: string) => {
    if (!farmId) return t('common.notSet');
    const farm = farms.find(f => f.id === farmId);
    return farm ? farm.name : t('common.unknownFarm');
  };
  
  const getItemImage = () => {
    if (item?.imageUrl) return item.imageUrl;
    
    const category = item?.category ? item.category.toLowerCase() : '';
    if (category.includes('seed')) {
      return "https://images.unsplash.com/photo-1618812515207-a3824e2c2f81?q=80&w=400&auto=format&fit=crop";
    } else if (category.includes('fertilizer')) {
      return "https://images.unsplash.com/photo-1605000797499-95a51c5269ae?q=80&w=400&auto=format&fit=crop";
    } else if (category.includes('tool')) {
      return "https://images.unsplash.com/photo-1581166397057-235af2b3c6dd?q=80&w=400&auto=format&fit=crop";
    } else if (category.includes('pesticide')) {
      return "https://images.unsplash.com/photo-1612178537253-bccd437b730e?q=80&w=400&auto=format&fit=crop";
    }
    
    return `https://source.unsplash.com/featured/?agriculture,${category}&sig=${item?.id}`;
  };
  
  const getCategoryColor = (category?: string): readonly [string, string] => {
    if (!category) return [colors.primary, colors.primaryDark] as const;
    
    const lowerCategory = category.toLowerCase();
    
    if (lowerCategory.includes('seed')) {
      return ['#4CAF50', '#2E7D32'] as const;
    } else if (lowerCategory.includes('fertilizer')) {
      return ['#9C27B0', '#7B1FA2'] as const;
    } else if (lowerCategory.includes('tool')) {
      return ['#2196F3', '#1976D2'] as const;
    } else if (lowerCategory.includes('pesticide')) {
      return ['#F44336', '#D32F2F'] as const;
    } else if (lowerCategory.includes('equipment')) {
      return ['#FF9800', '#F57C00'] as const;
    } else if (lowerCategory.includes('feed')) {
      return ['#8BC34A', '#689F38'] as const;
    } else if (lowerCategory.includes('medication')) {
      return ['#E91E63', '#C2185B'] as const;
    } else if (lowerCategory.includes('fuel')) {
      return ['#FFC107', '#F57F17'] as const;
    } else {
      return ['#607D8B', '#455A64'] as const;
    }
  };





  const getStatusInfo = () => {
    if (isExpired) {
      return {
        icon: <XCircle size={16} color="#fff" />,
        text: t('common.expired'),
        color: '#F44336',
        bgColor: 'rgba(244, 67, 54, 0.1)'
      };
    }
    if (isLowStock) {
      return {
        icon: <TrendingDown size={16} color="#fff" />,
        text: t('common.lowStock'),
        color: '#FF9800',
        bgColor: 'rgba(255, 152, 0, 0.1)'
      };
    }
    if (isExpiringSoon) {
      return {
        icon: <Clock size={16} color="#fff" />,
        text: t('common.expiringSoon'),
        color: '#FFC107',
        bgColor: 'rgba(255, 193, 7, 0.1)'
      };
    }
    return {
      icon: <CheckCircle2 size={16} color="#fff" />,
      text: t('common.inStock'),
      color: '#4CAF50',
      bgColor: 'rgba(76, 175, 80, 0.1)'
    };
  };

  const getCategoryIcon = (category?: string) => {
    if (!category) return "📦";
    const lowerCategory = category.toLowerCase();
    if (lowerCategory.includes('seed')) return "🌱";
    if (lowerCategory.includes('fertilizer')) return "🧪";
    if (lowerCategory.includes('tool')) return "🔧";
    if (lowerCategory.includes('pesticide')) return "🚫";
    if (lowerCategory.includes('equipment')) return "⚙️";
    if (lowerCategory.includes('feed')) return "🌾";
    if (lowerCategory.includes('medication')) return "💊";
    if (lowerCategory.includes('fuel')) return "⛽";
    return "📦";
  };
  
  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <Stack.Screen
          options={{
            title: t('common.itemDetails'),
            headerStyle: { backgroundColor: colors.primary },
            headerTintColor: '#fff',
            headerLeft: () => (
              <TouchableOpacity onPress={() => router.back()} style={{ marginLeft: 16 }}>
                <ArrowLeft size={24} color="#fff" />
              </TouchableOpacity>
            ),
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text, marginTop: 16 }]}>
            {t('common.loadingItemDetails')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }
  
  if (!item) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <Stack.Screen
          options={{
            title: t('common.itemDetails'),
            headerStyle: { backgroundColor: colors.primary },
            headerTintColor: '#fff',
            headerLeft: () => (
              <TouchableOpacity onPress={() => router.back()} style={{ marginLeft: 16 }}>
                <ArrowLeft size={24} color="#fff" />
              </TouchableOpacity>
            ),
          }}
        />
        <View style={styles.emptyContainer}>
          <XCircle size={64} color={colors.textSecondary} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            {t('common.itemNotFound')}
          </Text>
          <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
            {t('common.itemNotFoundMessage')}
          </Text>
          <TouchableOpacity 
            style={[styles.backButton, { backgroundColor: colors.primary }]}
            onPress={() => router.back()}
          >
            <Text style={styles.backButtonText}>{t('common.backToInventory')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }
  
  const categoryColors = getCategoryColor(item.category);
  const statusInfo = getStatusInfo();



  // Check user permissions - caretakers cannot edit or delete
  const canEdit = user?.role === "owner" || user?.role === "admin";
  const isCaretaker = user?.role === "caretaker";
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t('common.itemDetails'),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: '#fff',
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} style={{ marginLeft: 16 }}>
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),

        }}
      />
      

      
      <ScrollView 
        style={styles.scrollView} 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl 
            refreshing={refreshing} 
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
          {/* Hero Section */}
          <View style={styles.heroContainer}>
            <Image
              source={{ uri: getItemImage() }}
              style={styles.heroImage}
              resizeMode="cover"
            />
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.8)']}
              style={styles.heroOverlay}
            />

            <View style={styles.heroContent}>
              <View style={styles.categoryBadge}>
                <Text style={styles.categoryEmoji}>{getCategoryIcon(item.category)}</Text>
                <Text style={styles.categoryText}>
                  {item.category || t('inventory.other')}
                </Text>
              </View>

              <Text style={styles.heroTitle}>
                {item.name}
              </Text>
                
                <View style={styles.heroStats}>
                  <View style={styles.quantityDisplay}>
                    <Text style={styles.quantityNumber}>{item.quantity}</Text>
                    <Text style={styles.quantityUnit}>{item.unit}</Text>
                  </View>
                  
                  <View style={[styles.statusBadge, { backgroundColor: statusInfo.color }]}>
                    {statusInfo.icon}
                    <Text style={styles.statusText}>{statusInfo.text}</Text>
                  </View>
                </View>
              </View>
            </View>

            {/* Alert Banners */}
            {(isLowStock || isExpiringSoon || isExpired) && (
              <View style={styles.alertsSection}>
                {isExpired && (
                  <AlertBanner
                    type="error"
                    title={t('common.itemExpired')}
                    message={t('common.itemExpiredMessage', { date: formatDate(item.expiryDate) })}
                    isDarkMode={isDarkMode}
                  />
                )}
                
                {!isExpired && isLowStock && (
                  <AlertBanner
                    type="warning"
                    title={t('common.lowStockAlert')}
                    message={t('common.lowStockMessage', { 
                      quantity: item.quantity, 
                      unit: item.unit, 
                      minQuantity: item.minQuantity 
                    })}
                    isDarkMode={isDarkMode}
                  />
                )}
                
                {!isExpired && !isLowStock && isExpiringSoon && (
                  <AlertBanner
                    type="warning"
                    title={t('common.expiringSoonAlert')}
                    message={t('common.expiringSoonMessage', { date: formatDate(item.expiryDate) })}
                    isDarkMode={isDarkMode}
                  />
                )}
              </View>
            )}
            
            {/* Action Buttons */}
            <View style={styles.actionSection}>
              {canEdit ? (
                <>
                  <TouchableOpacity 
                    style={[styles.primaryAction, { backgroundColor: colors.primary }]} 
                    onPress={handleEdit}
                  >
                    <Edit size={20} color="#fff" />
                    <Text style={styles.primaryActionText}>{t('common.editItem')}</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={[styles.secondaryAction, { borderColor: colors.error }]}
                    onPress={handleDelete}
                    disabled={deleting}
                  >
                    {deleting ? (
                      <ActivityIndicator size="small" color={colors.error} />
                    ) : (
                      <>
                        <Trash2 size={20} color={colors.error} />
                        <Text style={[styles.secondaryActionText, { color: colors.error }]}>
                          {t('common.delete')}
                        </Text>
                      </>
                    )}
                  </TouchableOpacity>
                </>
              ) : isCaretaker ? (
                <TouchableOpacity 
                  style={[styles.primaryAction, { backgroundColor: colors.primary }]} 
                  onPress={handleRequestTransfer}
                >
                  <Send size={20} color="#fff" />
                  <Text style={styles.primaryActionText}>{t('common.requestTransfer')}</Text>
                </TouchableOpacity>
              ) : null}
            </View>
            
            {/* Details Card */}
            <View style={[styles.detailsCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                {t('common.itemInformation')}
              </Text>
              
              <View style={styles.detailsGrid}>
                <View style={styles.detailRow}>
                  <View style={[styles.detailIcon, { backgroundColor: 'rgba(76, 175, 80, 0.1)' }]}>
                    <Package size={20} color="#4CAF50" />
                  </View>
                  <View style={styles.detailContent}>
                    <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                      {t('common.currentStock')}
                    </Text>
                    <Text style={[styles.detailValue, { color: colors.text }]}>
                      {item.quantity} {item.unit}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.detailRow}>
                  <View style={[styles.detailIcon, { backgroundColor: 'rgba(255, 152, 0, 0.1)' }]}>
                    <TrendingDown size={20} color="#FF9800" />
                  </View>
                  <View style={styles.detailContent}>
                    <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                      {t('common.minimumStock')}
                    </Text>
                    <Text style={[styles.detailValue, { color: colors.text }]}>
                      {item.minQuantity || 0} {item.unit}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.detailRow}>
                  <View style={[styles.detailIcon, { backgroundColor: 'rgba(33, 150, 243, 0.1)' }]}>
                    <MapPin size={20} color="#2196F3" />
                  </View>
                  <View style={styles.detailContent}>
                    <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                      {t('common.farmLocation')}
                    </Text>
                    <Text style={[styles.detailValue, { color: colors.text }]}>
                      {getFarmName(item.location)}
                    </Text>
                  </View>
                </View>
                
                {item.purchaseDate && (
                  <View style={styles.detailRow}>
                    <View style={[styles.detailIcon, { backgroundColor: 'rgba(156, 39, 176, 0.1)' }]}>
                      <Calendar size={20} color="#9C27B0" />
                    </View>
                    <View style={styles.detailContent}>
                      <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                        {t('common.purchaseDate')}
                      </Text>
                      <Text style={[styles.detailValue, { color: colors.text }]}>
                        {formatDate(item.purchaseDate)}
                      </Text>
                    </View>
                  </View>
                )}
                
                {item.expiryDate && (
                  <View style={styles.detailRow}>
                    <View style={[styles.detailIcon, { backgroundColor: 'rgba(244, 67, 54, 0.1)' }]}>
                      <Calendar size={20} color="#F44336" />
                    </View>
                    <View style={styles.detailContent}>
                      <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                        {t('common.expiryDate')}
                      </Text>
                      <Text style={[
                        styles.detailValue, 
                        { 
                          color: isExpiringSoon || isExpired ? colors.error : colors.text
                        }
                      ]}>
                        {formatDate(item.expiryDate)}
                      </Text>
                    </View>
                  </View>
                )}
                
                {item.price && (
                  <View style={styles.detailRow}>
                    <View style={[styles.detailIcon, { backgroundColor: 'rgba(76, 175, 80, 0.1)' }]}>
                      <DollarSign size={20} color="#4CAF50" />
                    </View>
                    <View style={styles.detailContent}>
                      <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                        {t('common.price')}
                      </Text>
                      <Text style={[styles.detailValue, { color: colors.text }]}>
                        ${item.price.toFixed(2)}
                      </Text>
                    </View>
                  </View>
                )}
                
                {item.supplier && (
                  <View style={styles.detailRow}>
                    <View style={[styles.detailIcon, { backgroundColor: 'rgba(255, 152, 0, 0.1)' }]}>
                      <Truck size={20} color="#FF9800" />
                    </View>
                    <View style={styles.detailContent}>
                      <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                        {t('common.supplier')}
                      </Text>
                      <Text style={[styles.detailValue, { color: colors.text }]}>
                        {item.supplier}
                      </Text>
                    </View>
                  </View>
                )}
              </View>
            </View>
            
            {/* Description Card */}
            {item.description && (
              <View style={[styles.descriptionCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  {t('common.notesAndDescription')}
                </Text>
                <Text style={[styles.descriptionText, { color: colors.textSecondary }]}>
                  {item.description}
                </Text>
              </View>
            )}
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  backButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  clearButton: {
    padding: 8,
    marginLeft: 8,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingBottom: 32,
  },
  heroContainer: {
    height: 300,
    position: 'relative',
    overflow: 'hidden',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 160,
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  categoryBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 12,
    gap: 6,
  },
  categoryEmoji: {
    fontSize: 16,
  },
  categoryText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '700',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  heroTitle: {
    fontSize: 28,
    fontWeight: '800',
    color: '#fff',
    marginBottom: 16,
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  heroStats: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  quantityDisplay: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  quantityNumber: {
    fontSize: 32,
    fontWeight: '900',
    color: '#fff',
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  quantityUnit: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
    marginLeft: 4,
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '700',
  },
  alertsSection: {
    paddingTop: 16,
  },
  actionSection: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 20,
    gap: 12,
  },
  primaryAction: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  primaryActionText: {
    color: '#fff',
    fontWeight: '700',
    fontSize: 16,
  },
  secondaryAction: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 2,
    gap: 8,
  },
  secondaryActionText: {
    fontWeight: '700',
    fontSize: 16,
  },
  detailsCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '800',
    marginBottom: 20,
  },
  detailsGrid: {
    gap: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  detailContent: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  descriptionCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  descriptionText: {
    fontSize: 16,
    lineHeight: 24,
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    minHeight: 400,
  },
  noResultsTitle: {
    fontSize: 20,
    fontWeight: '700',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  noResultsSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  clearSearchButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  clearSearchText: {
    color: '#fff',
    fontWeight: '600',
  },
});
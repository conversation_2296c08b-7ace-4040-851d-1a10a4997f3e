import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, TextInput, TouchableOpacity, Alert, Image, Platform, Dimensions, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter, useLocalSearchParams } from "expo-router";
import { useTheme } from "@/context/theme-context";
import { useFarm } from "@/context/farm-context";
import { useAuth } from "@/context/auth-context";
import { useLanguage } from "@/context/language-context";
import { getMachineryById, updateMachinery, Machinery } from "@/services/machinery-service";
import { uploadMachineryImage, deleteImageFromStorage } from "@/services/storage-service";
import { Camera, Upload, X } from "lucide-react-native";
import * as ImagePicker from "expo-image-picker";
import { LinearGradient } from "expo-linear-gradient";
import ModalDropdown from '@/components/ModalDropdown';
import { useLookupStore } from '@/services/lookup_service';

export default function EditMachineryScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const { colors } = useTheme();
  const { selectedFarm } = useFarm();
  const { user } = useAuth();
  const { t, isRTL } = useLanguage();
  const screenWidth = Dimensions.get('window').width;
  const isTablet = screenWidth > 768;
  
  const [machinery, setMachinery] = useState<Machinery | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    type: "tractor",
    model: "",
    year: "",
    serialNumber: "",
    status: "working",
    fuelType: "diesel",
    fuelCapacity: "",
    currentFuelLevel: "",
    odometerReading: "",
    odometerUnit: "hours",
    maintenanceIntervalHours: "",
    price: "",
    notes: "",
  });
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [imageUri, setImageUri] = useState<string | null>(null);

  // Lookup store for dynamic dropdowns
  const [machineryTypes, setMachineryTypes] = useState<any[]>([]);
  const [fuelTypes, setFuelTypes] = useState<any[]>([]);
  const [statusTypes, setStatusTypes] = useState<any[]>([]);
  const { getLookupsByCategory } = useLookupStore();

  // Helper function to get correct status translations
  const getStatusTranslation = (status: string) => {
    switch (status.toLowerCase()) {
      case 'working':
        return t('machinery.working');
      case 'maintenance':
      case 'under maintenance':
        return t('machinery.maintenance');
      case 'malfunction':
        return t('machinery.malfunction');
      case 'in use':
      case 'in_use':
        return t('machinery.inUse');
      case 'in use other farm':
      case 'in_use_other_farm':
        return t('machinery.inUseOtherFarm');
      default:
        return t(`machinery.${status}`) || status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  // Odometer units options
  const odometerUnits = [
    { label: t('common.hours'), value: 'hours' },
    { label: t('common.kilometers'), value: 'kilometers' }
  ];

  // Load lookup data
  useEffect(() => {
    const machineryTypesData = getLookupsByCategory('machineryType');
    const fuelTypesData = getLookupsByCategory('fuelType');
    const statusTypesData = getLookupsByCategory('machineryStatus');

    const machineryTypesOptions = machineryTypesData.map(item => ({
      label: t(`machinery.${item.title.toLowerCase().replace(/[^a-z0-9]/g, '_')}`) ||
             t(`machinery.types.${item.title.toLowerCase()}`) ||
             t(`machinery.${item.title.toLowerCase()}`) ||
             item.title,
      value: item.id,
    }));

    const fuelTypesOptions = fuelTypesData.map(item => ({
      label: t(`machinery.${item.title.toLowerCase()}`) || t(`common.${item.title.toLowerCase()}`) || item.title,
      value: item.id,
    }));

    const statusTypesOptions = statusTypesData.map(item => ({
      label: getStatusTranslation(item.title.toLowerCase()) || item.title,
      value: item.id,
    }));

    setMachineryTypes(machineryTypesOptions);
    setFuelTypes(fuelTypesOptions);
    setStatusTypes(statusTypesOptions);
  }, [t]);

  useEffect(() => {
    loadMachinery();
  }, [id]);
  
  const loadMachinery = async () => {
    if (!id) {
      Alert.alert(t('common.error'), t('machinery.noIdProvided'));
      router.back();
      return;
    }
    
    try {
      setInitialLoading(true);
      const machineryData = await getMachineryById(id as string, selectedFarm?.id);
      
      if (!machineryData) {
        Alert.alert(t('common.error'), t('machinery.notFound'));
        router.back();
        return;
      }
      
      setMachinery(machineryData);
      setFormData({
        name: machineryData.name,
        type: machineryData.type,
        model: machineryData.model,
        year: machineryData.year.toString(),
        serialNumber: machineryData.serialNumber,
        status: machineryData.status,
        fuelType: machineryData.fuelType,
        fuelCapacity: machineryData.fuelCapacity.toString(),
        currentFuelLevel: machineryData.currentFuelLevel.toString(),
        odometerReading: machineryData.odometerReading.toString(),
        odometerUnit: "hours", // Default to hours
        maintenanceIntervalHours: machineryData.maintenanceIntervalHours.toString(),
        price: machineryData.price ? machineryData.price.toString() : "",
        notes: machineryData.notes || "",
      });
      
      // Set existing image if available
      if (machineryData.imageUrl) {
        setImageUri(machineryData.imageUrl);
      }
    } catch (error) {
      console.error("Error loading machinery:", error);
      Alert.alert(t('common.error'), t('machinery.loadError'));
      router.back();
    } finally {
      setInitialLoading(false);
    }
  };
  
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };
  
  const validateForm = () => {
    if (!formData.name.trim()) {
      Alert.alert(t('common.error'), t('machinery.enterMachineryName'));
      return false;
    }
    if (!formData.model.trim()) {
      Alert.alert(t('common.error'), t('machinery.enterModel'));
      return false;
    }
    if (!formData.year.trim() || isNaN(Number(formData.year)) || Number(formData.year) < 1900 || Number(formData.year) > new Date().getFullYear() + 1) {
      Alert.alert(t('common.error'), t('machinery.enterYear'));
      return false;
    }
    if (!formData.serialNumber.trim()) {
      Alert.alert(t('common.error'), t('machinery.enterSerialNumber'));
      return false;
    }
    if (!formData.fuelCapacity.trim() || isNaN(Number(formData.fuelCapacity)) || Number(formData.fuelCapacity) <= 0) {
      Alert.alert(t('common.error'), t('machinery.validFuelCapacityRequired'));
      return false;
    }
    if (!formData.currentFuelLevel.trim() || isNaN(Number(formData.currentFuelLevel)) || Number(formData.currentFuelLevel) < 0) {
      Alert.alert(t('common.error'), t('machinery.validCurrentFuelRequired'));
      return false;
    }
    if (Number(formData.currentFuelLevel) > Number(formData.fuelCapacity)) {
      Alert.alert(t('common.error'), t('common.currentFuelLevelExceedsCapacity'));
      return false;
    }
    if (!formData.odometerReading.trim() || isNaN(Number(formData.odometerReading)) || Number(formData.odometerReading) < 0) {
      Alert.alert(t('common.error'), t('machinery.validOdometerRequired'));
      return false;
    }
    if (!formData.maintenanceIntervalHours.trim() || isNaN(Number(formData.maintenanceIntervalHours)) || Number(formData.maintenanceIntervalHours) <= 0) {
      Alert.alert(t('common.error'), t('machinery.validMaintenanceIntervalRequired'));
      return false;
    }
    return true;
  };
  
  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });

      if (!result.canceled) {
        setImageUri(result.assets[0].uri);
      }
    } catch (error) {
      console.error("Error picking image:", error);
      Alert.alert(t('common.error'), t('inventory.imagePickError'));
    }
  };

  const takePhoto = async () => {
    if (Platform.OS === "web") {
      Alert.alert(t('common.notAvailable'), t('inventory.cameraNotAvailableWeb'));
      return;
    }

    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== "granted") {
        Alert.alert(t('common.permissionRequired'), t('inventory.cameraPermissionRequired'));
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });

      if (!result.canceled) {
        setImageUri(result.assets[0].uri);
      }
    } catch (error) {
      console.error("Error using camera:", error);
      Alert.alert(t('common.error'), t('inventory.cameraError'));
    }
  };
  
  const handleSubmit = async () => {
    if (!machinery || !user) {
      Alert.alert(t('common.error'), t('common.missingRequiredInformation'));
      return;
    }
    
    if (!validateForm()) return;
    
    setLoading(true);
    try {
      let finalImageUrl = imageUri;

      // Upload image to Firebase Storage if a new local image is selected
      if (imageUri && imageUri.startsWith('file://')) {
        console.log("Uploading new machinery image to Storage...");

        // Delete old image if it exists and is from Firebase Storage
        if (machinery.imageUrl) {
          await deleteImageFromStorage(machinery.imageUrl);
        }

        // Upload new image
        finalImageUrl = await uploadMachineryImage(imageUri, machinery.id);
        console.log("Machinery image uploaded:", finalImageUrl);
      }

      const updates = {
        farmId: machinery.farmId,
        name: formData.name.trim(),
        type: formData.type as any,
        model: formData.model.trim(),
        year: Number(formData.year),
        serialNumber: formData.serialNumber.trim(),
        status: formData.status as any,
        fuelType: formData.fuelType as any,
        fuelCapacity: Number(formData.fuelCapacity),
        currentFuelLevel: Number(formData.currentFuelLevel),
        odometerReading: Number(formData.odometerReading),
        maintenanceIntervalHours: Number(formData.maintenanceIntervalHours),
        price: formData.price ? Number(formData.price) : undefined,
        notes: formData.notes.trim() || undefined,
        imageUrl: finalImageUrl || undefined,
      };
      
      await updateMachinery(machinery.id, updates);
      
      Alert.alert(t('common.success'), t('machinery.machineryUpdated'), [
        { 
          text: t('common.ok'), 
          onPress: () => router.back()
        }
      ]);
    } catch (error) {
      console.error("Error updating machinery:", error);
      Alert.alert(t('common.error'), t('machinery.updateError'));
    } finally {
      setLoading(false);
    }
  };
  
  if (initialLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
        <Stack.Screen
          options={{
            title: t('machinery.editMachinery'),
            headerStyle: { backgroundColor: colors.primary },
            headerTintColor: 'white',
            headerTitleStyle: { color: 'white', fontWeight: '600' },
          }}
        />
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            {t('machinery.loadingDetails')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t('machinery.editMachinery'),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { color: 'white', fontWeight: '600' },
        }}
      />
      
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Image Picker Section */}
        <View style={styles.imagePickerContainer}>
          {imageUri ? (
            <View style={styles.selectedImageContainer}>
              <Image source={{ uri: imageUri }} style={styles.selectedImage} />
              <LinearGradient
                colors={["transparent", "rgba(0,0,0,0.7)"]}
                style={styles.imageOverlay}
              />
              <View style={styles.imageActions}>
                <TouchableOpacity
                  style={[styles.imageActionButton, { backgroundColor: colors.primary }]}
                  onPress={pickImage}
                >
                  <Text style={styles.imageActionText}>{t('inventory.changeImage')}</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.imageActionButton, { backgroundColor: colors.error }]}
                  onPress={() => setImageUri(null)}
                >
                  <Text style={styles.imageActionText}>{t('inventory.removeImage')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <View style={[styles.imagePlaceholder, { borderColor: colors.border }]}>
              <View style={styles.imageButtonsContainer}>
                <TouchableOpacity
                  style={[styles.imageButton, { backgroundColor: colors.primary }]}
                  onPress={pickImage}
                >
                  <Upload size={20} color="#fff" />
                  <Text style={styles.imageButtonText}>{t('common.gallery')}</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.imageButton, { backgroundColor: colors.primary }]}
                  onPress={takePhoto}
                >
                  <Camera size={20} color="#fff" />
                  <Text style={styles.imageButtonText}>{t('common.camera')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>

        <View style={[styles.formContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('machinery.basicInformation')}</Text>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>{t('machinery.machineryName')} *</Text>
            <TextInput
              style={[styles.input, { backgroundColor: colors.inputBackground, color: colors.text, borderColor: colors.border }]}
              value={formData.name}
              onChangeText={(value) => handleInputChange("name", value)}
              placeholder={t('machinery.enterMachineryName')}
              placeholderTextColor={colors.textSecondary}
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>{t('common.type')} *</Text>
            <ModalDropdown
              items={machineryTypes}
              selectedValue={formData.type}
              onValueChange={(value) => handleInputChange("type", value)}
              placeholder={t('machinery.selectType')}
              error={false}
            />
          </View>
          
          <View style={styles.row}>
            <View style={[styles.inputGroup, { flex: 1, marginRight: 8 }]}>
              <Text style={[styles.label, { color: colors.text }]}>{t('common.model')} *</Text>
              <TextInput
                style={[styles.input, { backgroundColor: colors.inputBackground, color: colors.text, borderColor: colors.border }]}
                value={formData.model}
                onChangeText={(value) => handleInputChange("model", value)}
                placeholder={t('machinery.enterModel')}
                placeholderTextColor={colors.textSecondary}
              />
            </View>

            <View style={[styles.inputGroup, { flex: 1, marginLeft: 8 }]}>
              <Text style={[styles.label, { color: colors.text }]}>{t('common.year')} *</Text>
              <TextInput
                style={[styles.input, { backgroundColor: colors.inputBackground, color: colors.text, borderColor: colors.border }]}
                value={formData.year}
                onChangeText={(value) => handleInputChange("year", value)}
                placeholder="2024"
                placeholderTextColor={colors.textSecondary}
                keyboardType="numeric"
              />
            </View>
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>{t('machinery.serialNumber')} *</Text>
            <TextInput
              style={[styles.input, { backgroundColor: colors.inputBackground, color: colors.text, borderColor: colors.border }]}
              value={formData.serialNumber}
              onChangeText={(value) => handleInputChange("serialNumber", value)}
              placeholder={t('machinery.enterSerialNumber')}
              placeholderTextColor={colors.textSecondary}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>{t('common.status')} *</Text>
            <ModalDropdown
              items={statusTypes}
              selectedValue={formData.status}
              onValueChange={(value) => handleInputChange("status", value)}
              placeholder={t('machinery.selectStatus')}
              error={false}
            />
          </View>
        </View>
        
        <View style={[styles.formContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('machinery.fuelInformation')}</Text>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>{t('machinery.fuelType')} *</Text>
            <ModalDropdown
              items={fuelTypes}
              selectedValue={formData.fuelType}
              onValueChange={(value) => handleInputChange("fuelType", value)}
              placeholder={t('machinery.selectFuelType')}
              error={false}
            />
          </View>
          
          <View style={styles.row}>
            <View style={[styles.inputGroup, { flex: 1, marginRight: 8 }]}>
              <Text style={[styles.label, { color: colors.text }]}>{t('machinery.fuelCapacity')} (L) *</Text>
              <TextInput
                style={[styles.input, { backgroundColor: colors.inputBackground, color: colors.text, borderColor: colors.border }]}
                value={formData.fuelCapacity}
                onChangeText={(value) => handleInputChange("fuelCapacity", value)}
                placeholder="200"
                placeholderTextColor={colors.textSecondary}
                keyboardType="numeric"
              />
            </View>

            <View style={[styles.inputGroup, { flex: 1, marginLeft: 8 }]}>
              <Text style={[styles.label, { color: colors.text }]}>{t('machinery.currentFuelLevel')} (L) *</Text>
              <TextInput
                style={[styles.input, { backgroundColor: colors.inputBackground, color: colors.text, borderColor: colors.border }]}
                value={formData.currentFuelLevel}
                onChangeText={(value) => handleInputChange("currentFuelLevel", value)}
                placeholder="150"
                placeholderTextColor={colors.textSecondary}
                keyboardType="numeric"
              />
            </View>
          </View>

          {/* Price */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              {t('machinery.priceRs')}
            </Text>
            <TextInput
              style={[styles.input, { backgroundColor: colors.inputBackground, color: colors.text, borderColor: colors.border }]}
              placeholder={t('machinery.enterPrice')}
              placeholderTextColor={colors.textSecondary}
              value={formData.price}
              onChangeText={(text) => handleInputChange("price", text)}
              keyboardType="numeric"
            />
          </View>
        </View>

        <View style={[styles.formContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('machinery.maintenanceInformation')}</Text>

          {/* Odometer Reading */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              {t('machinery.odometerReading')} *
            </Text>

            {/* Combined Unit and Reading Input */}
            <View style={[styles.row, isRTL && styles.rowRTL]}>
              {/* Unit Selection - Shorter */}
              <View style={[styles.inputGroup, { flex: 0.35, marginRight: isRTL ? 0 : 8, marginLeft: isRTL ? 8 : 0 }]}>
                <ModalDropdown
                  items={odometerUnits}
                  selectedValue={formData.odometerUnit}
                  onValueChange={(value) => {
                    handleInputChange('odometerUnit', value);
                  }}
                  placeholder={t('machinery.selectUnit')}
                  error={false}
                />
              </View>

              {/* Reading Input - Longer */}
              <View style={[styles.inputGroup, { flex: 0.65, marginLeft: isRTL ? 0 : 8, marginRight: isRTL ? 8 : 0 }]}>
                <TextInput
                  style={[styles.input, { backgroundColor: colors.inputBackground, color: colors.text, borderColor: colors.border }]}
                  placeholder={formData.odometerUnit === 'hours' ? "e.g., 1250" : "e.g., 15000"}
                  placeholderTextColor={colors.textSecondary}
                  value={formData.odometerReading}
                  onChangeText={(value) => handleInputChange("odometerReading", value)}
                  keyboardType="numeric"
                />
              </View>
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>{t('machinery.maintenanceIntervalHours')} ({t('common.hours')}) *</Text>
            <TextInput
              style={[styles.input, { backgroundColor: colors.inputBackground, color: colors.text, borderColor: colors.border }]}
              value={formData.maintenanceIntervalHours}
              onChangeText={(value) => handleInputChange("maintenanceIntervalHours", value)}
              placeholder="250"
              placeholderTextColor={colors.textSecondary}
              keyboardType="numeric"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>{t('common.notes')}</Text>
            <TextInput
              style={[styles.textArea, { backgroundColor: colors.inputBackground, color: colors.text, borderColor: colors.border }]}
              value={formData.notes}
              onChangeText={(value) => handleInputChange("notes", value)}
              placeholder={t('machinery.enterNotes')}
              placeholderTextColor={colors.textSecondary}
              multiline
              numberOfLines={4}
            />
          </View>
        </View>
        
        <TouchableOpacity
          style={[styles.submitButton, { backgroundColor: colors.primary, opacity: loading ? 0.7 : 1 }]}
          onPress={handleSubmit}
          disabled={loading}
        >
          <Text style={styles.submitButtonText}>
            {loading ? t('common.submitting') : t('machinery.updateMachinery')}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 16,
  },
  imagePickerContainer: {
    marginBottom: 16,
  },
  imagePlaceholder: {
    height: 180,
    borderRadius: 16,
    borderWidth: 1,
    borderStyle: "dashed",
    justifyContent: "center",
    alignItems: "center",
  },
  imageButtonsContainer: {
    flexDirection: "row",
    gap: 16,
  },
  imageButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
    gap: 8,
  },
  imageButtonText: {
    color: "#fff",
    fontWeight: "500",
  },
  selectedImageContainer: {
    position: "relative",
    borderRadius: 16,
    overflow: "hidden",
    height: 180,
  },
  selectedImage: {
    width: "100%",
    height: "100%",
  },
  imageOverlay: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  imageActions: {
    position: "absolute",
    bottom: 12,
    left: 12,
    right: 12,
    flexDirection: "row",
    justifyContent: "center",
    gap: 12,
  },
  imageActionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
  },
  imageActionText: {
    color: "#fff",
    fontWeight: "600",
  },
  formContainer: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: "top",
  },
  row: {
    flexDirection: "row",
  },

  submitButton: {
    borderRadius: 8,
    padding: 16,
    alignItems: "center",
    marginTop: 8,
  },
  submitButtonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },

  row: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  rowRTL: {
    flexDirection: 'row-reverse',
  },
});
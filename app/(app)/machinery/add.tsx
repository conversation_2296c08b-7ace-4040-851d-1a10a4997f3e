import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Image,
  ActivityIndicator,
  Alert,
  Platform,
  KeyboardAvoidingView
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, Stack, useLocalSearchParams } from 'expo-router';
import { useTheme } from '@/context/theme-context';
import { useLanguage } from '@/context/language-context';
import { useFarm } from '@/context/farm-context';
import { useAuth } from '@/context/auth-context';
import { Dimensions } from 'react-native';
import ModalDropdown from '@/components/ModalDropdown';
import {
  ArrowLeft,
  Camera,
  Upload,
  Calendar,
  Truck,
  Fuel,
  Wrench,
  Save
} from 'lucide-react-native';
import { addMachinery, Machinery } from '@/services/machinery-service';
import * as ImagePicker from 'expo-image-picker';
import { uploadMachineryImage } from '@/services/storage-service';
import DateTimePicker from '@react-native-community/datetimepicker';
import { LinearGradient } from 'expo-linear-gradient';
import { useLookupStore } from '@/services/lookup_service';

// Predefined machinery types and fuel types
const MACHINERY_TYPES = [
  { key: 'machinery.tractor', stored: 'tractor' },
  { key: 'machinery.harvester', stored: 'harvester' },
  { key: 'machinery.planter', stored: 'planter' },
  { key: 'machinery.sprayer', stored: 'sprayer' },
  { key: 'machinery.cultivator', stored: 'cultivator' },
  { key: 'machinery.car', stored: 'car' },
  { key: 'machinery.jeep', stored: 'jeep' },
  { key: 'machinery.motorbike', stored: 'motorbike' },
  { key: 'machinery.bicycle', stored: 'bicycle' },
  { key: 'machinery.other_custom', stored: 'Other (Custom)' },
];

const FUEL_TYPES = [
  { key: 'machinery.diesel', stored: 'diesel' },
  { key: 'machinery.gasoline', stored: 'gasoline' },
  { key: 'machinery.petrol', stored: 'petrol' },
  { key: 'machinery.electric', stored: 'electric' },
  { key: 'machinery.hybrid', stored: 'hybrid' },
];

const STATUS_TYPES = [
  { key: 'machinery.working', stored: 'working' },
  { key: 'machinery.maintenance', stored: 'maintenance' },
  { key: 'machinery.malfunction', stored: 'malfunction' },
  { key: 'machinery.in_use', stored: 'in_use' },
];

export default function AddMachineryScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const { selectedFarm } = useFarm();
  const { user } = useAuth();

  const [submitting, setSubmitting] = useState(false);
  const screenWidth = Dimensions.get('window').width;
  const isTablet = screenWidth > 768;

  // AI Analysis info
  const analysisConfidence = params.analysisConfidence ? parseFloat(params.analysisConfidence as string) : null;
  const analysisDescription = params.analysisDescription as string;

  // Form state with auto-fill from AI analysis
  const [formData, setFormData] = useState({
    name: (params.name as string) || '',
    type: (params.type as string) || '',
    model: (params.model as string) || '',
    year: (params.year as string) || new Date().getFullYear().toString(),
    registrationNumber: (params.registrationNumber as string) || '',
    status: (params.status as string) || t('machinery.working'),
    fuelType: (params.fuelType as string) || '',
    fuelCapacity: '',
    currentFuelLevel: '',
    odometerReading: '',
    odometerUnit: 'hours', // Add odometer unit
    maintenanceIntervalHours: '100',
    price: (params.price as string) || '',
    notes: (params.notes as string) || '',
  });

  // Custom input states
  const [customType, setCustomType] = useState('');
  const [customFuelType, setCustomFuelType] = useState('');

  // Image state with auto-fill from AI analysis
  const [imageUri, setImageUri] = useState<string | null>((params.imageUri as string) || null);

  // Date states
  const [lastMaintenanceDate, setLastMaintenanceDate] = useState<Date>(new Date());
  const [nextMaintenanceDate, setNextMaintenanceDate] = useState<Date>(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)); // 30 days from now
  const [showLastMaintenancePicker, setShowLastMaintenancePicker] = useState(false);
  const [showNextMaintenancePicker, setShowNextMaintenancePicker] = useState(false);
  const [machineryTypes, setMachineryTypes] = useState<any[]>([]);
  const [fuelTypes,setFuelTypes ] = useState<any[]>([]);
  const [status,setStatus ] = useState<any[]>([]);
  const { getLookupsByCategory } = useLookupStore();

  // Helper function to get correct status translations
  const getStatusTranslation = (status: string) => {
    switch (status.toLowerCase()) {
      case 'working':
        return t('machinery.working');
      case 'maintenance':
      case 'under maintenance':
        return t('machinery.maintenance');
      case 'malfunction':
        return t('machinery.malfunction');
      case 'in use':
      case 'in_use':
        return t('machinery.inUse');
      case 'in use other farm':
      case 'in_use_other_farm':
        return t('machinery.inUseOtherFarm');
      default:
        return t(`machinery.${status}`) || status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  // Odometer units options
  const odometerUnits = [
    { label: t('common.hours'), value: 'hours' },
    { label: t('common.kilometers'), value: 'kilometers' }
  ];

  // Validation state
  const [formErrors, setFormErrors] = useState({
    name: '',
    type: '',
    model: '',
    year: '',
    fuelType: '',
    fuelCapacity: '',
    currentFuelLevel: '',
    odometerReading: '',
    price: '',
  });

  // Handle form submission
  const handleSubmit = async () => {
    // Validate form
    if (!validateForm()) {
      return;
    }

    if (!user) {
      Alert.alert(t('common.error'), t('auth.loginRequired'));
      return;
    }

    if (!selectedFarm) {
      Alert.alert(t('common.error'), t('common.farmIdRequired'));
      return;
    }

    setSubmitting(true);

    try {
      // Determine final type, fuel type, and status values
      let finalType: string;
      let finalFuelType: string;
      let finalStatus: string;

      // Handle type: use custom if provided, otherwise use stored value
      if (formData.type === t('machinery.other_custom') && customType) {
        finalType = customType;
      } else {
        const typeOption = MACHINERY_TYPES.find(opt => t(opt.key) === formData.type);
        finalType = typeOption ? typeOption.stored : formData.type;
      }

      // Handle fuel type: use custom if provided, otherwise use stored value
      if (formData.fuelType === t('machinery.other_custom') && customFuelType) {
        finalFuelType = customFuelType;
      } else {
        const fuelOption = FUEL_TYPES.find(opt => t(opt.key) === formData.fuelType);
        finalFuelType = fuelOption ? fuelOption.stored : formData.fuelType;
      }

      // Handle status: convert translated value to stored value
      const statusOption = STATUS_TYPES.find(opt => t(opt.key) === formData.status);
      finalStatus = statusOption ? statusOption.stored : formData.status;

      let finalImageUrl = imageUri;

      // Upload image to Firebase Storage if a local image is selected
      if (imageUri && imageUri.startsWith('file://')) {
        console.log("Uploading machinery image to Storage...");
        const tempMachineryId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
        finalImageUrl = await uploadMachineryImage(imageUri, tempMachineryId);
        console.log("Machinery image uploaded:", finalImageUrl);
      }

      // Create machinery data
      const machineryData: Omit<Machinery, 'id' | 'createdAt' | 'updatedAt'> = {
        farmId: selectedFarm.id,
        name: formData.name,
        type: finalType as any,
        model: formData.model,
        year: parseInt(formData.year),
        serialNumber: formData.registrationNumber,
        status: finalStatus as any,
        fuelType: finalFuelType as any,
        fuelCapacity: parseFloat(formData.fuelCapacity) || 0,
        currentFuelLevel: parseFloat(formData.currentFuelLevel) || 0,
        odometerReading: parseFloat(formData.odometerReading) || 0,
        lastMaintenanceDate: lastMaintenanceDate.toISOString(),
        nextMaintenanceDate: nextMaintenanceDate.toISOString(),
        maintenanceIntervalHours: parseInt(formData.maintenanceIntervalHours) || 100,
        price: formData.price ? parseFloat(formData.price) : undefined,
        notes: formData.notes,
        createdBy: user.uid,
      };

      // Add image if selected
      if (finalImageUrl) {
        machineryData.imageUrl = finalImageUrl;
      }

      // Add machinery
      await addMachinery(machineryData);
      Alert.alert(t('common.success'), t('machinery.machineryAdded'), [
        { text: t('common.ok'), onPress: () => router.back() }
      ]);
    } catch (error) {
      console.error('Error saving machinery:', error);
      Alert.alert(t('common.error'), t('machinery.addError'));
    } finally {
      setSubmitting(false);
    }
  };

//Lookups 
useEffect(() => {
    const res =getLookupsByCategory('machineryType');
    const res1 = getLookupsByCategory('fuelType');
    const res2 = getLookupsByCategory('machineryStatus');
    const response = res.map(item => ({
      label: t(`machinery.${item.title.toLowerCase().replace(/[^a-z0-9]/g, '_')}`) ||
             t(`machinery.types.${item.title.toLowerCase()}`) ||
             t(`machinery.${item.title.toLowerCase()}`) ||
             item.title,
      value: item.id,
      //icon: getCategoryIcon(item.value)
    }));
   const response1 = res1.map(item => ({
    label: t(`machinery.${item.title.toLowerCase()}`) || t(`common.${item.title.toLowerCase()}`) || item.title,
      value: item.id,
    }));
    const response2 = res2.map(item => ({
      label: getStatusTranslation(item.title.toLowerCase()) || item.title,
      value: item.id,
    }));
    
    setMachineryTypes(response);
    setFuelTypes(response1)
    setStatus(response2)
   // console.log({ res });
  }, []);

  // Validate form
  const validateForm = () => {
    let valid = true;
    const errors = {
      name: '',
      type: '',
      model: '',
      year: '',
      fuelType: '',
      fuelCapacity: '',
      currentFuelLevel: '',
      odometerReading: '',
      price: '',
    };

    if (!formData.name || !formData.name.trim()) {
      errors.name = t('machinery.machineryName') + ' ' + t('common.isRequired');
      valid = false;
    }

    // Type validation: check if type is selected or custom type is provided
    const hasType = formData.type && formData.type.trim();
    const hasCustomType = customType && customType.trim();
    const isCustomTypeSelected = formData.type === t('machinery.other_custom');

    if (!hasType) {
      errors.type = t('machinery.machineryType') + ' ' + t('common.isRequired');
      valid = false;
    } else if (isCustomTypeSelected && !hasCustomType) {
      errors.type = t('machinery.enterCustomType');
      valid = false;
    }

    if (!formData.model || !formData.model.trim()) {
      errors.model = t('machinery.model') + ' ' + t('common.isRequired');
      valid = false;
    }

    if (!formData.year || isNaN(Number(formData.year)) || Number(formData.year) < 1900 || Number(formData.year) > new Date().getFullYear()) {
      errors.year = t('machinery.year') + ' ' + t('common.isInvalid');
      valid = false;
    }

    // Fuel type validation: check if fuel type is selected or custom fuel type is provided
    const hasFuelType = formData.fuelType && formData.fuelType.trim();
    const hasCustomFuelType = customFuelType && customFuelType.trim();
    const isCustomFuelTypeSelected = formData.fuelType === t('machinery.other_custom');

    if (!hasFuelType) {
      errors.fuelType = t('machinery.fuelType') + ' ' + t('common.isRequired');
      valid = false;
    } else if (isCustomFuelTypeSelected && !hasCustomFuelType) {
      errors.fuelType = t('machinery.enterCustomFuelType');
      valid = false;
    }

    if (!formData.fuelCapacity || isNaN(Number(formData.fuelCapacity)) || Number(formData.fuelCapacity) <= 0) {
      errors.fuelCapacity = t('machinery.fuelCapacity') + ' ' + t('common.isInvalid');
      valid = false;
    }

    if (!formData.currentFuelLevel || isNaN(Number(formData.currentFuelLevel)) || Number(formData.currentFuelLevel) < 0) {
      errors.currentFuelLevel = t('machinery.currentFuelLevel') + ' ' + t('common.isInvalid');
      valid = false;
    }

    if (Number(formData.currentFuelLevel) > Number(formData.fuelCapacity)) {
      errors.currentFuelLevel = t('machinery.currentFuelLevelExceedsCapacity');
      valid = false;
    }

    if (!formData.odometerReading || isNaN(Number(formData.odometerReading)) || Number(formData.odometerReading) < 0) {
      errors.odometerReading = t('machinery.odometerReading') + ' ' + t('common.isInvalid');
      valid = false;
    }

    // Price validation (optional)
    if (formData.price && (isNaN(Number(formData.price)) || Number(formData.price) < 0)) {
      errors.price = t('machinery.priceRs') + ' ' + t('common.isInvalid');
      valid = false;
    }

    setFormErrors(errors);
    return valid;
  };



  // Pick image from gallery
  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });

      if (!result.canceled) {
        setImageUri(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(t('common.error'), t('inventory.imagePickError'));
    }
  };

  // Take photo with camera
  const takePhoto = async () => {
    if (Platform.OS === 'web') {
      Alert.alert(t('common.notAvailable'), t('inventory.cameraNotAvailableWeb'));
      return;
    }

    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(t('common.permissionRequired'), t('inventory.cameraPermissionRequired'));
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });

      if (!result.canceled) {
        setImageUri(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert(t('common.error'), t('inventory.cameraError'));
    }
  };

  // Date picker handlers
  const onLastMaintenanceDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || lastMaintenanceDate;
    setShowLastMaintenancePicker(Platform.OS === 'ios');
    if (currentDate) {
      setLastMaintenanceDate(currentDate);
    }
  };

  const onNextMaintenanceDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || nextMaintenanceDate;
    setShowNextMaintenancePicker(Platform.OS === 'ios');
    if (currentDate) {
      setNextMaintenanceDate(currentDate);
    }
  };

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString();
  };

  // Handle type selection
  const handleTypeSelect = (type: string) => {
    if (type === t('machinery.other_custom')) {
      setFormData({ ...formData, type: t('machinery.other_custom') });
      setCustomType('');
    } else {
      setFormData({ ...formData, type });
      setCustomType('');
    }
  };

  // Handle fuel type selection
  const handleFuelTypeSelect = (fuelType: string) => {
    if (fuelType === t('machinery.other_custom')) {
      setFormData({ ...formData, fuelType: t('machinery.other_custom') });
      setCustomFuelType('');
    } else {
      setFormData({ ...formData, fuelType });
      setCustomFuelType('');
    }
  };

  // Handle custom type input
  const handleCustomTypeChange = (text: string) => {
    setCustomType(text);
  };

  // Handle custom fuel type input
  const handleCustomFuelTypeChange = (text: string) => {
    setCustomFuelType(text);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t('machinery.addMachinery'),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { color: 'white', fontWeight: '600' },
        }}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.content}>
            {/* Header Banner */}
            <LinearGradient
              colors={[`${colors.primary}20`, `${colors.primary}10`]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.headerBanner}
            >
              <Text style={[styles.headerTitle, { color: colors.primary }]}>
                {t('machinery.addMachinery')}
              </Text>
              <Text style={[styles.headerSubtitle, { color: `${colors.primary}CC` }]}>
                {t('machinery.addMachineryDescription')}
              </Text>
            </LinearGradient>

            {/* AI Analysis Banner */}
            {analysisConfidence && analysisDescription && (
              <View style={[styles.aiAnalysisBanner, { backgroundColor: colors.primary + '15', borderColor: colors.primary + '30' }]}>
                <View style={styles.aiAnalysisHeader}>
                  <View style={[styles.aiAnalysisIcon, { backgroundColor: colors.primary }]}>
                    <Text style={styles.aiAnalysisIconText}>AI</Text>
                  </View>
                  <View style={styles.aiAnalysisInfo}>
                    <Text style={[styles.aiAnalysisTitle, { color: colors.primary }]}>
                      {t('chat.aiAnalysisResults')}
                    </Text>
                    <Text style={[styles.aiAnalysisConfidence, { color: colors.textSecondary }]}>
                      {t('chat.confidence')}: {Math.round(analysisConfidence * 100)}%
                    </Text>
                  </View>
                </View>
                <Text style={[styles.aiAnalysisDescription, { color: colors.text }]}>
                  {analysisDescription}
                </Text>
              </View>
            )}

            {/* Image Picker */}
            <View style={styles.imagePickerContainer}>
              {imageUri ? (
                <View style={styles.selectedImageContainer}>
                  <Image source={{ uri: imageUri }} style={styles.selectedImage} />
                  <LinearGradient
                    colors={['transparent', 'rgba(0,0,0,0.7)']}
                    style={styles.imageOverlay}
                  />
                  <View style={styles.imageActions}>
                    <TouchableOpacity
                      style={[styles.imageActionButton, { backgroundColor: colors.primary }]}
                      onPress={pickImage}
                    >
                      <Text style={styles.imageActionText}>{t('inventory.changeImage')}</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.imageActionButton, { backgroundColor: colors.error }]}
                      onPress={() => setImageUri(null)}
                    >
                      <Text style={styles.imageActionText}>{t('inventory.removeImage')}</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : (
                <View style={[styles.imagePlaceholder, { borderColor: colors.border }]}>
                  <View style={styles.imageButtonsContainer}>
                    <TouchableOpacity
                      style={[styles.imageButton, { backgroundColor: colors.primary }]}
                      onPress={pickImage}
                    >
                      <Upload size={20} color="#fff" />
                      <Text style={styles.imageButtonText}>{t('common.gallery')}</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[styles.imageButton, { backgroundColor: colors.primary }]}
                      onPress={takePhoto}
                    >
                      <Camera size={20} color="#fff" />
                      <Text style={styles.imageButtonText}>{t('common.camera')}</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            </View>

            {/* Form Card - Basic Information */}
            <View style={[styles.formCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
              <Text style={[styles.formSectionTitle, { color: colors.text }]}>
                {t('machinery.basicInformation')}
              </Text>

              {/* Name */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {t('machinery.machineryName')} *
                </Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: colors.text,
                      borderColor: formErrors.name ? colors.error : colors.border,
                      backgroundColor: colors.background === '#121212' ? '#252525' : '#f5f5f5',
                    }
                  ]}
                  placeholder={t('machinery.enterMachineryName')}
                  placeholderTextColor={colors.textSecondary}
                  value={formData.name}
                  onChangeText={(text) => setFormData({ ...formData, name: text })}
                />
                {formErrors.name ? (
                  <Text style={[styles.errorText, { color: colors.error }]}>
                    {formErrors.name}
                  </Text>
                ) : null}
              </View>

              {/* Type */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {t('machinery.machineryType')} *
                </Text>
                <ModalDropdown
                  // items={MACHINERY_TYPES.map(type => ({
                  //   label: t(type.key),
                  //   value: t(type.key),
                  // }))}
                  items={machineryTypes}
                  selectedValue={formData.type}
                  onValueChange={(value) => {
                    handleTypeSelect(value);
                    if (formErrors.type) {
                      setFormErrors(prev => ({ ...prev, type: '' }));
                    }
                  }}
                  placeholder={t('machinery.selectType')}
                  error={!!formErrors.type}
                />

                {(formData.type === t('machinery.other_custom') || customType) && (
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: colors.text,
                        borderColor: formErrors.type ? colors.error : colors.border,
                        backgroundColor: colors.background === '#121212' ? '#252525' : '#f5f5f5',
                        marginTop: 8,
                      }
                    ]}
                    placeholder={t('machinery.enterCustomType')}
                    placeholderTextColor={colors.textSecondary}
                    value={customType}
                    onChangeText={handleCustomTypeChange}
                  />
                )}

                {formErrors.type ? (
                  <Text style={[styles.errorText, { color: colors.error }]}>
                    {formErrors.type}
                  </Text>
                ) : null}
              </View>

              {/* Model and Year */}
              <View style={styles.formRow}>
                <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    {t('machinery.model')} *
                  </Text>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: colors.text,
                        borderColor: formErrors.model ? colors.error : colors.border,
                        backgroundColor: colors.background === '#121212' ? '#252525' : '#f5f5f5',
                      }
                    ]}
                    placeholder={t('machinery.enterModel')}
                    placeholderTextColor={colors.textSecondary}
                    value={formData.model}
                    onChangeText={(text) => setFormData({ ...formData, model: text })}
                  />
                  {formErrors.model ? (
                    <Text style={[styles.errorText, { color: colors.error }]}>
                      {formErrors.model}
                    </Text>
                  ) : null}
                </View>

                <View style={[styles.formGroup, { flex: 1 }]}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    {t('machinery.year')} *
                  </Text>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: colors.text,
                        borderColor: formErrors.year ? colors.error : colors.border,
                        backgroundColor: colors.background === '#121212' ? '#252525' : '#f5f5f5',
                      }
                    ]}
                    placeholder={t('machinery.enterYear')}
                    placeholderTextColor={colors.textSecondary}
                    value={formData.year}
                    onChangeText={(text) => setFormData({ ...formData, year: text })}
                    keyboardType="numeric"
                  />
                  {formErrors.year ? (
                    <Text style={[styles.errorText, { color: colors.error }]}>
                      {formErrors.year}
                    </Text>
                  ) : null}
                </View>
              </View>

              {/* Registration Number */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {t('machinery.registrationNumber')}
                </Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: colors.text,
                      borderColor: colors.border,
                      backgroundColor: colors.background === '#121212' ? '#252525' : '#f5f5f5',
                    }
                  ]}
                  placeholder={t('machinery.enterRegistrationNumber')}
                  placeholderTextColor={colors.textSecondary}
                  value={formData.registrationNumber}
                  onChangeText={(text) => setFormData({ ...formData, registrationNumber: text })}
                />
              </View>

              {/* Status */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {t('machinery.status')}
                </Text>
                <ModalDropdown
                  // items={STATUS_TYPES.map(status => ({
                  //   label: t(status.key),
                  //   value: t(status.key),
                  // }))}
                  items={status}
                  selectedValue={formData.status}
                  onValueChange={(value) => {
                    setFormData(prev => ({ ...prev, status: value }));
                  }}
                  placeholder={t('machinery.selectStatus')}
                />
              </View>
            </View>

            {/* Form Card - Fuel Information */}
            <View style={[styles.formCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
              <Text style={[styles.formSectionTitle, { color: colors.text }]}>
                {t('machinery.fuelInformation')}
              </Text>

              {/* Fuel Type */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {t('machinery.fuelType')} *
                </Text>
                <ModalDropdown
                  // items={FUEL_TYPES.map(fuelType => ({
                  //   label: t(fuelType.key),
                  //   value: t(fuelType.key),
                  // }))}
                  items={fuelTypes}
                  selectedValue={formData.fuelType}
                  onValueChange={(value) => {
                    handleFuelTypeSelect(value);
                    if (formErrors.fuelType) {
                      setFormErrors(prev => ({ ...prev, fuelType: '' }));
                    }
                  }}
                  placeholder={t('machinery.selectFuelType')}
                  error={!!formErrors.fuelType}
                />

                {(formData.fuelType === t('machinery.other_custom') || customFuelType) && (
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: colors.text,
                        borderColor: formErrors.fuelType ? colors.error : colors.border,
                        backgroundColor: colors.background === '#121212' ? '#252525' : '#f5f5f5',
                        marginTop: 8,
                      }
                    ]}
                    placeholder={t('machinery.enterCustomFuelType')}
                    placeholderTextColor={colors.textSecondary}
                    value={customFuelType}
                    onChangeText={handleCustomFuelTypeChange}
                  />
                )}

                {formErrors.fuelType ? (
                  <Text style={[styles.errorText, { color: colors.error }]}>
                    {formErrors.fuelType}
                  </Text>
                ) : null}
              </View>

              {/* Fuel Capacity and Current Level */}
              <View style={styles.formRow}>
                <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    {t('machinery.fuelCapacity')} (L) *
                  </Text>
                  <View style={styles.inputWithIcon}>
                    <Fuel size={20} color={colors.textSecondary} style={styles.inputIcon} />
                    <TextInput
                      style={[
                        styles.inputWithPadding,
                        {
                          color: colors.text,
                          borderColor: formErrors.fuelCapacity ? colors.error : colors.border,
                          backgroundColor: colors.background === '#121212' ? '#252525' : '#f5f5f5',
                        }
                      ]}
                      placeholder="0"
                      placeholderTextColor={colors.textSecondary}
                      value={formData.fuelCapacity}
                      onChangeText={(text) => setFormData({ ...formData, fuelCapacity: text })}
                      keyboardType="numeric"
                    />
                  </View>
                  {formErrors.fuelCapacity ? (
                    <Text style={[styles.errorText, { color: colors.error }]}>
                      {formErrors.fuelCapacity}
                    </Text>
                  ) : null}
                </View>

                <View style={[styles.formGroup, { flex: 1 }]}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    {t('machinery.currentFuelLevel')} (L) *
                  </Text>
                  <View style={styles.inputWithIcon}>
                    <Fuel size={20} color={colors.textSecondary} style={styles.inputIcon} />
                    <TextInput
                      style={[
                        styles.inputWithPadding,
                        {
                          color: colors.text,
                          borderColor: formErrors.currentFuelLevel ? colors.error : colors.border,
                          backgroundColor: colors.background === '#121212' ? '#252525' : '#f5f5f5',
                        }
                      ]}
                      placeholder="0"
                      placeholderTextColor={colors.textSecondary}
                      value={formData.currentFuelLevel}
                      onChangeText={(text) => setFormData({ ...formData, currentFuelLevel: text })}
                      keyboardType="numeric"
                    />
                  </View>
                  {formErrors.currentFuelLevel ? (
                    <Text style={[styles.errorText, { color: colors.error }]}>
                      {formErrors.currentFuelLevel}
                    </Text>
                  ) : null}
                </View>
              </View>
            </View>

            {/* Form Card - Maintenance Information */}
            <View style={[styles.formCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
              <Text style={[styles.formSectionTitle, { color: colors.text }]}>
                {t('machinery.maintenanceInformation')}
              </Text>

              {/* Odometer Reading */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {t('machinery.odometerReading')} *
                </Text>

                {/* Combined Unit and Reading Input */}
                <View style={[styles.row, isRTL && styles.rowRTL]}>
                  {/* Unit Selection - Shorter */}
                  <View style={[styles.inputGroup, { flex: 0.35, marginRight: isRTL ? 0 : 8, marginLeft: isRTL ? 8 : 0 }]}>
                    <ModalDropdown
                      items={odometerUnits}
                      selectedValue={formData.odometerUnit}
                      onValueChange={(value) => {
                        setFormData({ ...formData, odometerUnit: value });
                      }}
                      placeholder={t('machinery.selectUnit')}
                      error={false}
                    />
                  </View>

                  {/* Reading Input - Longer */}
                  <View style={[styles.inputGroup, { flex: 0.65, marginLeft: isRTL ? 0 : 8, marginRight: isRTL ? 8 : 0 }]}>
                    <View style={styles.inputWithIcon}>
                      <Truck size={20} color={colors.textSecondary} style={styles.inputIcon} />
                      <TextInput
                        style={[
                          styles.inputWithPadding,
                          {
                            color: colors.text,
                            borderColor: formErrors.odometerReading ? colors.error : colors.border,
                            backgroundColor: colors.background === '#121212' ? '#252525' : '#f5f5f5',
                          }
                        ]}
                        placeholder={formData.odometerUnit === 'hours' ? "e.g., 1250" : "e.g., 15000"}
                        placeholderTextColor={colors.textSecondary}
                        value={formData.odometerReading}
                        onChangeText={(text) => setFormData({ ...formData, odometerReading: text })}
                        keyboardType="numeric"
                      />
                    </View>
                  </View>
                </View>

                {formErrors.odometerReading ? (
                  <Text style={[styles.errorText, { color: colors.error }]}>
                    {formErrors.odometerReading}
                  </Text>
                ) : null}
              </View>

              {/* Maintenance Interval */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {t('machinery.maintenanceIntervalHours')} ({t('common.hours')})
                </Text>
                <View style={styles.inputWithIcon}>
                  <Wrench size={20} color={colors.textSecondary} style={styles.inputIcon} />
                  <TextInput
                    style={[
                      styles.inputWithPadding,
                      {
                        color: colors.text,
                        borderColor: colors.border,
                        backgroundColor: colors.background === '#121212' ? '#252525' : '#f5f5f5',
                      }
                    ]}
                    placeholder="100"
                    placeholderTextColor={colors.textSecondary}
                    value={formData.maintenanceIntervalHours}
                    onChangeText={(text) => setFormData({ ...formData, maintenanceIntervalHours: text })}
                    keyboardType="numeric"
                  />
                </View>
              </View>

              {/* Maintenance Dates */}
              <View style={styles.formRow}>
                <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    {t('machinery.lastMaintenanceDate')}
                  </Text>
                  <TouchableOpacity
                    style={[
                      styles.dateButton,
                      {
                        borderColor: colors.border,
                        backgroundColor: colors.background === '#121212' ? '#252525' : '#f5f5f5',
                      }
                    ]}
                    onPress={() => setShowLastMaintenancePicker(true)}
                  >
                    <Text
                      style={{
                        color: colors.text,
                      }}
                    >
                      {formatDate(lastMaintenanceDate)}
                    </Text>
                    <Calendar size={20} color={colors.textSecondary} />
                  </TouchableOpacity>
                  {showLastMaintenancePicker && (
                    <DateTimePicker
                      value={lastMaintenanceDate}
                      mode="date"
                      display={Platform.OS === "ios" ? "spinner" : "default"}
                      onChange={onLastMaintenanceDateChange}
                      maximumDate={new Date()}
                    />
                  )}
                </View>

                <View style={[styles.formGroup, { flex: 1 }]}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    {t('machinery.nextMaintenanceDate')}
                  </Text>
                  <TouchableOpacity
                    style={[
                      styles.dateButton,
                      {
                        borderColor: colors.border,
                        backgroundColor: colors.background === '#121212' ? '#252525' : '#f5f5f5',
                      }
                    ]}
                    onPress={() => setShowNextMaintenancePicker(true)}
                  >
                    <Text
                      style={{
                        color: colors.text,
                      }}
                    >
                      {formatDate(nextMaintenanceDate)}
                    </Text>
                    <Calendar size={20} color={colors.textSecondary} />
                  </TouchableOpacity>
                  {showNextMaintenancePicker && (
                    <DateTimePicker
                      value={nextMaintenanceDate}
                      mode="date"
                      display={Platform.OS === "ios" ? "spinner" : "default"}
                      onChange={onNextMaintenanceDateChange}
                      minimumDate={new Date()}
                    />
                  )}
                </View>
              </View>

              {/* Price */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {t('machinery.priceRs')}
                </Text>
                <View style={styles.inputWithIcon}>
                  <Text style={[styles.currencySymbol, { color: colors.textSecondary }]}>Rs</Text>
                  <TextInput
                    style={[
                      styles.inputWithPadding,
                      {
                        color: colors.text,
                        borderColor: formErrors.price ? colors.error : colors.border,
                        backgroundColor: colors.background === '#121212' ? '#252525' : '#f5f5f5',
                      }
                    ]}
                    placeholder={t('machinery.enterPrice')}
                    placeholderTextColor={colors.textSecondary}
                    value={formData.price}
                    onChangeText={(text) => setFormData({ ...formData, price: text })}
                    keyboardType="numeric"
                  />
                </View>
                {formErrors.price && (
                  <Text style={[styles.errorText, { color: colors.error }]}>
                    {formErrors.price}
                  </Text>
                )}
              </View>

              {/* Notes */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {t('common.notes')}
                </Text>
                <TextInput
                  style={[
                    styles.textArea,
                    {
                      color: colors.text,
                      borderColor: colors.border,
                      backgroundColor: colors.background === '#121212' ? '#252525' : '#f5f5f5',
                    }
                  ]}
                  placeholder={t('machinery.enterNotes')}
                  placeholderTextColor={colors.textSecondary}
                  value={formData.notes}
                  onChangeText={(text) => setFormData({ ...formData, notes: text })}
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                />
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[
                  styles.button,
                  styles.cancelButton,
                  {
                    borderColor: colors.border,
                    backgroundColor: colors.background === '#121212' ? '#252525' : '#f5f5f5',
                  }
                ]}
                onPress={() => router.back()}
              >
                <Text style={[styles.buttonText, { color: colors.text }]}>
                  {t('common.cancel')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.saveButton, { backgroundColor: colors.primary }]}
                onPress={handleSubmit}
                disabled={submitting}
              >
                {submitting ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <Save size={20} color="#fff" />
                    <Text style={styles.saveButtonText}>
                      {t('machinery.addMachinery')}
                    </Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom: 32,
  },
  headerBanner: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "bold",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
  },
  formCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  formSectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 12,
  },
  imagePickerContainer: {
    marginBottom: 16,
  },
  imagePlaceholder: {
    height: 180,
    borderRadius: 16,
    borderWidth: 1,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageButtonsContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  imageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 12,
    gap: 8,
  },
  imageButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  selectedImageContainer: {
    position: 'relative',
    borderRadius: 16,
    overflow: 'hidden',
    height: 180,
  },
  selectedImage: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  imageActions: {
    position: 'absolute',
    bottom: 12,
    left: 12,
    right: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 12,
  },
  imageActionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
  },
  imageActionText: {
    color: '#fff',
    fontWeight: '600',
  },
  formGroup: {
    marginBottom: 16,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  rowRTL: {
    flexDirection: 'row-reverse',
  },
  inputGroup: {
    flex: 1,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  inputWithIcon: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputIcon: {
    position: 'absolute',
    left: 16,
    zIndex: 1,
  },
  inputWithPadding: {
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingLeft: 48,
    fontSize: 16,
    flex: 1,
  },
  currencySymbol: {
    position: 'absolute',
    left: 16,
    zIndex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
  textArea: {
    height: 100,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingTop: 12,
    fontSize: 16,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  dropdownButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
  },
  dropdownButtonText: {
    fontSize: 16,
  },
  dropdown: {
    borderWidth: 1,
    borderRadius: 12,
    zIndex: 1000,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  dropdownItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  selectedDropdownItem: {
    backgroundColor: 'rgba(25, 118, 210, 0.1)',
  },
  dropdownItemText: {
    fontSize: 16,
  },
  dateButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  button: {
    flex: 1,
    height: 52,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  cancelButton: {
    marginRight: 8,
    borderWidth: 1,
  },
  saveButton: {
    marginLeft: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  // AI Analysis Banner Styles
  aiAnalysisBanner: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  aiAnalysisHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  aiAnalysisIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  aiAnalysisIconText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  aiAnalysisInfo: {
    flex: 1,
  },
  aiAnalysisTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  aiAnalysisConfidence: {
    fontSize: 12,
    fontWeight: '500',
  },
  aiAnalysisDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
});
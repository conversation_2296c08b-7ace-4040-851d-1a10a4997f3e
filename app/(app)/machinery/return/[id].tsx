import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, ActivityIndicator, TextInput, Modal, Alert } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter, useLocalSearchParams } from "expo-router";
import { useAuth } from "@/context/auth-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { 
  MachineryReturn, 
  getMachineryReturnById, 
  approveMachineryReturn, 
  rejectMachineryReturn
} from "@/services/machinery-service";
import { getRequestById } from "@/services/request-service";
import { 
  Truck, 
  User, 
  Calendar, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertCircle,
  FileText,
  Info,
  X,
  Wrench,
  Fuel
} from "lucide-react-native";

export default function MachineryReturnDetailScreen() {
  const { id } = useLocalSearchParams();
  const { user } = useAuth();
  const { colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const { selectedFarm } = useFarm();
  const router = useRouter();
  
  const [returnItem, setReturnItem] = useState<MachineryReturn | null>(null);
  const [originalRequest, setOriginalRequest] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  
  // Modal states
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [adminDescription, setAdminDescription] = useState("");
  const [rejectionReason, setRejectionReason] = useState("");
  
  useEffect(() => {
    if (selectedFarm && id) {
      loadReturnDetails();
    }
  }, [selectedFarm, id]);
  
  const loadReturnDetails = async () => {
    try {
      setLoading(true);
      if (!selectedFarm || !id) return;
      
      const returnData = await getMachineryReturnById(id as string, selectedFarm.id);
      if (returnData) {
        setReturnItem(returnData);
        
        // Load original request details
        const requestData = await getRequestById(returnData.requestId, selectedFarm.id);
        setOriginalRequest(requestData);
      }
    } catch (error) {
      console.error("Error loading return details:", error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleApprove = async () => {
    if (!user || !selectedFarm || !returnItem) return;
    
    try {
      setProcessing(true);
      await approveMachineryReturn(
        returnItem.id,
        selectedFarm.id,
        user.uid,
        user.displayName || user.name || "Admin",
        adminDescription.trim() || undefined
      );
      
      // Show success message
      Alert.alert(
        "Return Approved",
        "The machinery return has been approved successfully. The machinery is now available for new requests.",
        [
          {
            text: "OK",
            onPress: () => {
              // Navigate back to returns list
              router.back();
            }
          }
        ]
      );
      
      // Refresh data
      await loadReturnDetails();
      setShowApproveModal(false);
      setAdminDescription("");
    } catch (error) {
      console.error("Error approving return:", error);
      Alert.alert("Error", "Failed to approve the return. Please try again.");
    } finally {
      setProcessing(false);
    }
  };
  
  const handleReject = async () => {
    if (!user || !selectedFarm || !returnItem || !rejectionReason.trim()) return;
    
    try {
      setProcessing(true);
      await rejectMachineryReturn(
        returnItem.id,
        selectedFarm.id,
        user.uid,
        user.displayName || user.name || "Admin",
        rejectionReason.trim(),
        adminDescription.trim() || undefined
      );
      
      // Show success message
      Alert.alert(
        "Return Rejected",
        "The machinery return has been rejected. The machinery remains in use.",
        [
          {
            text: "OK",
            onPress: () => {
              // Navigate back to returns list
              router.back();
            }
          }
        ]
      );
      
      // Refresh data
      await loadReturnDetails();
      setShowRejectModal(false);
      setRejectionReason("");
      setAdminDescription("");
    } catch (error) {
      console.error("Error rejecting return:", error);
      Alert.alert("Error", "Failed to reject the return. Please try again.");
    } finally {
      setProcessing(false);
    }
  };
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#FF9800';
      case 'approved':
        return '#4CAF50';
      case 'rejected':
        return '#F44336';
      default:
        return '#757575';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock size={20} color={getStatusColor(status)} />;
      case 'approved':
        return <CheckCircle size={20} color={getStatusColor(status)} />;
      case 'rejected':
        return <XCircle size={20} color={getStatusColor(status)} />;
      default:
        return null;
    }
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case 'working':
        return '#4CAF50';
      case 'minor_issue':
        return '#FF9800';
      case 'needs_repair':
        return '#F44336';
      default:
        return '#757575';
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
        <Stack.Screen
          options={{
            title: "Machinery Return Details",
            headerStyle: {
              backgroundColor: colors.primary,
            },
            headerTintColor: "#fff",
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </SafeAreaView>
    );
  }

  if (!returnItem) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
        <Stack.Screen
          options={{
            title: "Machinery Return Details",
            headerStyle: {
              backgroundColor: colors.primary,
            },
            headerTintColor: "#fff",
          }}
        />
        <View style={styles.emptyContainer}>
          <AlertCircle size={64} color={colors.textSecondary} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            Return Not Found
          </Text>
          <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
            The requested machinery return could not be found
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: "Machinery Return Details",
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: "#fff",
        }}
      />
      
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Status Header */}
        <View style={[styles.statusHeader, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <View style={styles.statusIconContainer}>
            {getStatusIcon(returnItem.returnStatus)}
          </View>
          <View style={styles.statusContent}>
            <Text style={[styles.statusTitle, { color: colors.text }]}>
              Return {returnItem.returnStatus.charAt(0).toUpperCase() + returnItem.returnStatus.slice(1)}
            </Text>
            <Text style={[styles.statusSubtitle, { color: colors.textSecondary }]}>
              Returned on {formatDate(returnItem.returnedAt)}
            </Text>
          </View>
        </View>

        {/* Machinery Information */}
        <View style={[styles.section, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <View style={styles.sectionHeader}>
            <Truck size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Machinery Information
            </Text>
          </View>
          
          <View style={styles.infoGrid}>
            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                Machinery Name:
              </Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {returnItem.machineryName}
              </Text>
            </View>
            
            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                Condition:
              </Text>
              <View style={styles.conditionContainer}>
                <View
                  style={[
                    styles.conditionIndicator,
                    { backgroundColor: getConditionColor(returnItem.returnCondition) },
                  ]}
                />
                <Text style={[styles.infoValue, { color: getConditionColor(returnItem.returnCondition) }]}>
                  {returnItem.returnCondition.replace('_', ' ').charAt(0).toUpperCase() + 
                   returnItem.returnCondition.replace('_', ' ').slice(1)}
                </Text>
              </View>
            </View>
            
            {returnItem.hoursUsed && (
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  Hours Used:
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {returnItem.hoursUsed}h
                </Text>
              </View>
            )}
            
            {returnItem.fuelUsed && (
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  Fuel Used:
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {returnItem.fuelUsed}L
                </Text>
              </View>
            )}
            
            {returnItem.odometerReading && (
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  Odometer Reading:
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {returnItem.odometerReading}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Caretaker Information */}
        <View style={[styles.section, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <View style={styles.sectionHeader}>
            <User size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Caretaker Information
            </Text>
          </View>
          
          <View style={styles.infoGrid}>
            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                Name:
              </Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {returnItem.caretakerName}
              </Text>
            </View>
            
            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                Returned At:
              </Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {formatDate(returnItem.returnedAt)}
              </Text>
            </View>
          </View>
        </View>

        {/* Remarks */}
        {returnItem.remarks && (
          <View style={[styles.section, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <View style={styles.sectionHeader}>
              <FileText size={20} color={colors.primary} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Remarks
              </Text>
            </View>
            
            <Text style={[styles.remarksText, { color: colors.text }]}>
              {returnItem.remarks}
            </Text>
          </View>
        )}

        {/* Review Information */}
        {returnItem.returnStatus !== 'pending' && (
          <View style={[styles.section, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <View style={styles.sectionHeader}>
              <Info size={20} color={colors.primary} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Review Information
              </Text>
            </View>
            
            <View style={styles.infoGrid}>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  Reviewed By:
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {returnItem.reviewedByName || 'Unknown'}
                </Text>
              </View>
              
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  Reviewed At:
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {returnItem.reviewedAt ? formatDate(returnItem.reviewedAt) : 'Unknown'}
                </Text>
              </View>
              
              {returnItem.adminDescription && (
                <View style={styles.descriptionContainer}>
                  <Text style={[styles.descriptionLabel, { color: colors.textSecondary }]}>
                    Admin Description:
                  </Text>
                  <Text style={[styles.descriptionText, { color: colors.text }]}>
                    {returnItem.adminDescription}
                  </Text>
                </View>
              )}
              
              {returnItem.rejectionReason && (
                <View style={styles.rejectionContainer}>
                  <Text style={[styles.rejectionLabel, { color: '#F44336' }]}>
                    Rejection Reason:
                  </Text>
                  <Text style={[styles.rejectionText, { color: colors.text }]}>
                    {returnItem.rejectionReason}
                  </Text>
                </View>
              )}
            </View>
          </View>
        )}

        {/* Original Request Information */}
        {originalRequest && (
          <View style={[styles.section, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <View style={styles.sectionHeader}>
              <Calendar size={20} color={colors.primary} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Original Request
              </Text>
            </View>
            
            <View style={styles.infoGrid}>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  Request Date:
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {formatDate(originalRequest.createdAt)}
                </Text>
              </View>
              
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  Approved By:
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {originalRequest.approvedByName || 'Unknown'}
                </Text>
              </View>
              
              {originalRequest.reason && (
                <View style={styles.infoRow}>
                  <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                    Request Reason:
                  </Text>
                  <Text style={[styles.infoValue, { color: colors.text }]}>
                    {originalRequest.reason}
                  </Text>
                </View>
              )}
            </View>
          </View>
        )}
      </ScrollView>

      {/* Action Buttons */}
      {returnItem.returnStatus === 'pending' && user?.role === 'admin' && (
        <View style={[styles.actionContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <TouchableOpacity
            style={[styles.actionButton, styles.rejectButton]}
            onPress={() => setShowRejectModal(true)}
            disabled={processing}
          >
            <XCircle size={16} color="#fff" />
            <Text style={styles.actionButtonText}>Reject</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, styles.approveButton]}
            onPress={() => setShowApproveModal(true)}
            disabled={processing}
          >
            <CheckCircle size={16} color="#fff" />
            <Text style={styles.actionButtonText}>Approve</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Approve Modal */}
      <Modal
        visible={showApproveModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowApproveModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Approve Machinery Return
              </Text>
              <TouchableOpacity
                onPress={() => setShowApproveModal(false)}
                style={styles.closeButton}
              >
                <X size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>
            
            <Text style={[styles.modalDescription, { color: colors.textSecondary }]}>
              Approving this return will make the machinery available for new requests.
            </Text>
            
            <Text style={[styles.modalDescription, { color: colors.textSecondary, marginTop: 8 }]}>
              Add an optional description for this approval:
            </Text>
            
            <TextInput
              style={[
                styles.textInput,
                {
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text,
                }
              ]}
              placeholder="Optional description..."
              placeholderTextColor={colors.textSecondary}
              value={adminDescription}
              onChangeText={setAdminDescription}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
            
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowApproveModal(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, styles.approveButton]}
                onPress={handleApprove}
                disabled={processing}
              >
                {processing ? (
                  <ActivityIndicator size={16} color="#fff" />
                ) : (
                  <Text style={styles.actionButtonText}>Approve</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Reject Modal */}
      <Modal
        visible={showRejectModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowRejectModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Reject Machinery Return
              </Text>
              <TouchableOpacity
                onPress={() => setShowRejectModal(false)}
                style={styles.closeButton}
              >
                <X size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>
            
            <Text style={[styles.modalDescription, { color: colors.textSecondary }]}>
              Rejecting this return will keep the machinery in use status.
            </Text>
            
            <Text style={[styles.modalDescription, { color: colors.textSecondary, marginTop: 8 }]}>
              Please provide a reason for rejecting this return:
            </Text>
            
            <TextInput
              style={[
                styles.textInput,
                {
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text,
                }
              ]}
              placeholder="Rejection reason (required)..."
              placeholderTextColor={colors.textSecondary}
              value={rejectionReason}
              onChangeText={setRejectionReason}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
            
            <Text style={[styles.modalDescription, { color: colors.textSecondary, marginTop: 12 }]}>
              Additional description (optional):
            </Text>
            
            <TextInput
              style={[
                styles.textInput,
                {
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text,
                }
              ]}
              placeholder="Optional description..."
              placeholderTextColor={colors.textSecondary}
              value={adminDescription}
              onChangeText={setAdminDescription}
              multiline
              numberOfLines={2}
              textAlignVertical="top"
            />
            
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowRejectModal(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, styles.rejectButton]}
                onPress={handleReject}
                disabled={processing || !rejectionReason.trim()}
              >
                {processing ? (
                  <ActivityIndicator size={16} color="#fff" />
                ) : (
                  <Text style={styles.actionButtonText}>Reject</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 24,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginTop: 16,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: "center",
    marginTop: 8,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
  },
  statusIconContainer: {
    marginRight: 12,
  },
  statusContent: {
    flex: 1,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  statusSubtitle: {
    fontSize: 14,
  },
  section: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  infoGrid: {
    gap: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 14,
    flex: 1,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  conditionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: 6,
    flex: 1,
  },
  conditionIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  remarksText: {
    fontSize: 14,
    lineHeight: 20,
  },
  descriptionContainer: {
    marginTop: 8,
    padding: 12,
    backgroundColor: '#4CAF50' + '10',
    borderRadius: 8,
  },
  descriptionLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
  },
  rejectionContainer: {
    marginTop: 8,
    padding: 12,
    backgroundColor: '#F44336' + '10',
    borderRadius: 8,
  },
  rejectionLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  rejectionText: {
    fontSize: 14,
    lineHeight: 20,
  },
  actionContainer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 6,
  },
  approveButton: {
    backgroundColor: '#4CAF50',
  },
  rejectButton: {
    backgroundColor: '#F44336',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 12,
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  modalDescription: {
    fontSize: 14,
    marginBottom: 12,
    lineHeight: 20,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 80,
    marginBottom: 16,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: '#757575',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
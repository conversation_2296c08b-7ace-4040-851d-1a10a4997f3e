import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, TextInput, TouchableOpacity, Alert, ScrollView, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter, useLocalSearchParams } from "expo-router";
import { useAuth } from "@/context/auth-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { Request, getRequestById, createInventoryReturn } from "@/services/request-service";
import { ArrowLeft, Package, AlertCircle } from "lucide-react-native";

export default function ReturnRequestScreen() {
  const { user } = useAuth();
  const { colors } = useTheme();
  const { t } = useLanguage();
  const { selectedFarm } = useFarm();
  const router = useRouter();
  const { requestId, farmId } = useLocalSearchParams<{ requestId: string; farmId: string }>();
  
  const [request, setRequest] = useState<Request | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  
  // Form state
  const [quantityReturned, setQuantityReturned] = useState("");
  const [condition, setCondition] = useState<"good" | "used" | "damaged">("good");
  const [remarks, setRemarks] = useState("");
  
  const conditionOptions = [
    { value: "good", label: "Good", color: "#4CAF50" },
    { value: "used", label: "Used", color: "#FF9800" },
    { value: "damaged", label: "Damaged", color: "#F44336" },
  ];
  
  useEffect(() => {
    if (requestId && farmId) {
      loadRequest();
    }
  }, [requestId, farmId]);
  
  const loadRequest = async () => {
    try {
      setLoading(true);
      const requestData = await getRequestById(requestId, farmId);
      if (requestData) {
        setRequest(requestData);
      } else {
        Alert.alert("Error", "Request not found");
        router.back();
      }
    } catch (error) {
      console.error("Error loading request:", error);
      Alert.alert("Error", "Failed to load request");
      router.back();
    } finally {
      setLoading(false);
    }
  };
  
  const handleSubmit = async () => {
    if (!request || !user || !selectedFarm) {
      Alert.alert("Error", "Missing required information");
      return;
    }
    
    // Validation
    const quantity = parseFloat(quantityReturned);
    if (isNaN(quantity) || quantity <= 0) {
      Alert.alert("Error", "Please enter a valid quantity");
      return;
    }
    
    if (quantity > request.quantity) {
      Alert.alert("Error", `Quantity returned cannot exceed approved quantity (${request.quantity})`);
      return;
    }
    
    try {
      setSubmitting(true);
      
      await createInventoryReturn({
        requestId: request.id,
        caretakerId: user.uid,
        caretakerName: user.displayName || user.name || "Unknown",
        itemId: request.itemId,
        itemName: request.itemName,
        quantityReturned: quantity,
        quantityApproved: request.quantity,
        condition,
        remarks: remarks.trim() || undefined,
        farmId: selectedFarm.id,
        farmName: selectedFarm.name,
      });
      
      Alert.alert(
        "Success",
        "Return request submitted successfully",
        [
          {
            text: "OK",
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error) {
      console.error("Error submitting return:", error);
      Alert.alert("Error", "Failed to submit return request. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };
  
  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
        <Stack.Screen
          options={{
            title: "Return Item",
            headerStyle: { backgroundColor: colors.primary },
            headerTintColor: "#fff",
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>Loading request...</Text>
        </View>
      </SafeAreaView>
    );
  }
  
  if (!request) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
        <Stack.Screen
          options={{
            title: "Return Item",
            headerStyle: { backgroundColor: colors.primary },
            headerTintColor: "#fff",
          }}
        />
        <View style={styles.errorContainer}>
          <AlertCircle size={64} color={colors.textSecondary} />
          <Text style={[styles.errorTitle, { color: colors.text }]}>Request Not Found</Text>
          <Text style={[styles.errorSubtitle, { color: colors.textSecondary }]}>
            The request you're trying to return could not be found.
          </Text>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.primary }]}
            onPress={() => router.back()}
          >
            <ArrowLeft size={20} color="#fff" />
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: "Return Item",
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: "#fff",
          headerLeft: () => (
            <TouchableOpacity
              style={styles.headerBackButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Request Info */}
        <View style={[styles.section, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <View style={styles.sectionHeader}>
            <Package size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Request Information</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Item:</Text>
            <Text style={[styles.infoValue, { color: colors.text }]}>{request.itemName}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Approved Quantity:</Text>
            <Text style={[styles.infoValue, { color: colors.text }]}>
              {request.quantity} {request.unit}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Category:</Text>
            <Text style={[styles.infoValue, { color: colors.text }]}>{request.category || "N/A"}</Text>
          </View>
        </View>
        
        {/* Return Form */}
        <View style={[styles.section, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Return Details</Text>
          
          {/* Quantity Returned */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              Quantity Returning <Text style={styles.required}>*</Text>
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text,
                },
              ]}
              placeholder={`Max: ${request.quantity} ${request.unit}`}
              placeholderTextColor={colors.textSecondary}
              value={quantityReturned}
              onChangeText={setQuantityReturned}
              keyboardType="numeric"
            />
          </View>
          
          {/* Condition */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              Condition <Text style={styles.required}>*</Text>
            </Text>
            <View style={styles.conditionOptions}>
              {conditionOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.conditionOption,
                    {
                      backgroundColor: condition === option.value ? option.color + "20" : colors.background,
                      borderColor: condition === option.value ? option.color : colors.border,
                    },
                  ]}
                  onPress={() => setCondition(option.value as "good" | "used" | "damaged")}
                >
                  <View
                    style={[
                      styles.conditionIndicator,
                      { backgroundColor: option.color },
                    ]}
                  />
                  <Text
                    style={[
                      styles.conditionLabel,
                      {
                        color: condition === option.value ? option.color : colors.text,
                        fontWeight: condition === option.value ? "600" : "normal",
                      },
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          
          {/* Remarks */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors.text }]}>Remarks (Optional)</Text>
            <TextInput
              style={[
                styles.textArea,
                {
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text,
                },
              ]}
              placeholder="Add any additional notes about the return..."
              placeholderTextColor={colors.textSecondary}
              value={remarks}
              onChangeText={setRemarks}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
        </View>
        
        {/* Info Note */}
        <View style={[styles.infoNote, { backgroundColor: colors.primary + "10", borderColor: colors.primary + "30" }]}>
          <AlertCircle size={16} color={colors.primary} />
          <Text style={[styles.infoNoteText, { color: colors.primary }]}>
            Your return request will be reviewed by the admin. Items in good condition may be added back to inventory.
          </Text>
        </View>
      </ScrollView>
      
      {/* Submit Button */}
      <View style={[styles.footer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <TouchableOpacity
          style={[
            styles.submitButton,
            {
              backgroundColor: colors.primary,
              opacity: submitting ? 0.7 : 1,
            },
          ]}
          onPress={handleSubmit}
          disabled={submitting}
        >
          {submitting ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.submitButtonText}>Submit Return Request</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 24,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginTop: 16,
    textAlign: "center",
  },
  errorSubtitle: {
    fontSize: 14,
    textAlign: "center",
    marginTop: 8,
    marginBottom: 24,
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  backButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  headerBackButton: {
    padding: 8,
    marginLeft: -8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
  },
  infoLabel: {
    fontSize: 14,
    flex: 1,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: "600",
    flex: 1,
    textAlign: "right",
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
  },
  required: {
    color: "#F44336",
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    minHeight: 100,
  },
  conditionOptions: {
    gap: 12,
  },
  conditionOption: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 8,
    borderWidth: 2,
    gap: 12,
  },
  conditionIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  conditionLabel: {
    fontSize: 16,
    fontWeight: "500",
  },
  infoNote: {
    flexDirection: "row",
    alignItems: "flex-start",
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 16,
    gap: 12,
  },
  infoNoteText: {
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
  },
  submitButton: {
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  submitButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});
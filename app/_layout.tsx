import React from 'react';
import { Slot, Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { AuthProvider } from '@/context/auth-context';
import { ThemeProvider } from '@/context/theme-context';
import { LanguageProvider } from '@/context/language-context';
import { FarmProvider } from '@/context/farm-context';
import { MenuProvider } from 'react-native-popup-menu';

export default function RootLayout() {
  return (
    <MenuProvider>
      <LanguageProvider>
        <AuthProvider>
          <ThemeProvider>
            <FarmProvider>
              <StatusBar style="auto" />
              <Stack screenOptions={{ headerShown: false }}>
                <Stack.Screen name="(auth)" options={{ headerShown: false }} />
                <Stack.Screen name="(app)" options={{ headerShown: false }} />
                <Stack.Screen name="modal" options={{ presentation: 'modal' }} />
                <Stack.Screen name="index" options={{ href: null }} redirect={true} />
                <Stack.Screen name="(tabs)" options={{ href: null }} redirect={true} />
              </Stack>
            </FarmProvider>
          </ThemeProvider>
        </AuthProvider>
      </LanguageProvider>
    </MenuProvider>
  );
}

import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Image,
  ActivityIndicator,
  Alert,
  Platform,
  KeyboardAvoidingView
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { useTheme } from '@/context/theme-context';
import { useLanguage } from '@/context/language-context';
import { useFarm } from '@/context/farm-context';
import { useAuth } from '@/context/auth-context';
import {
  ArrowLeft,
  Camera,
  Upload,
  Calendar,
  ChevronDown,
  Truck,
  DollarSign,
  AlertTriangle,
  X,
  Save
} from 'lucide-react-native';
import { getInventoryItemById, updateInventoryItem, createInventoryItem, InventoryItem } from '@/services/inventory-service';
import * as ImagePicker from 'expo-image-picker';
import { uploadInventoryImage, deleteImageFromStorage } from '@/services/storage-service';
import DateTimePicker from '@react-native-community/datetimepicker';
import { LinearGradient } from 'expo-linear-gradient';
import { getCategories, getUnits } from '@/services/settings-service';

// Predefined categories and units
const CATEGORIES = [
  "Seeds",
  "Fertilizers",
  "Pesticides",
  "Tools",
  "Equipment",
  "Feed",
  "Medication",
  "Vaccination",
  "Fuel",
  "Other (Custom)",
];

const UNITS = [
  "kg",
  "g",
  "L",
  "mL",
  "units",
  "bags",
  "boxes",
  "bottles",
  "packs",
  "pieces",
  "Other (Custom)",
];

export default function EditInventoryItem() {
  const params = useLocalSearchParams();
  const router = useRouter();
  const { theme } = useTheme();
  const { t, isRTL } = useLanguage();
  const { selectedFarm } = useFarm();
  const { user } = useAuth();
  const isDarkMode = theme === 'dark';
  
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [item, setItem] = useState<InventoryItem | null>(null);
  const [isNewItem, setIsNewItem] = useState(!params.id);
  const [userRole, setUserRole] = useState(params.role as string || user?.role || 'owner');
  
  // Form state
  const [formData, setFormData] = useState({
    name: params.name as string || '',
    category: params.category as string || '',
    quantity: params.quantity as string || '',
    unit: params.unit as string || '',
    minQuantity: params.minQuantity as string || '0',
    description: params.description as string || '',
    supplier: params.supplier as string || '',
    price: params.price as string || '',
    location: params.location as string || selectedFarm?.id || '',
  });
  
  // Image state
  const [imageUri, setImageUri] = useState<string | null>(params.imageUrl as string || null);
  
  // Date states
  const [expiryDate, setExpiryDate] = useState<Date | null>(
    params.expiryDate ? new Date(params.expiryDate as string) : null
  );
  const [purchaseDate, setPurchaseDate] = useState<Date | null>(
    params.purchaseDate ? new Date(params.purchaseDate as string) : null
  );
  const [showExpiryDatePicker, setShowExpiryDatePicker] = useState(false);
  const [showPurchaseDatePicker, setShowPurchaseDatePicker] = useState(false);
  
  // Dropdown states
  const [categoryDropdownVisible, setCategoryDropdownVisible] = useState(false);
  const [unitDropdownVisible, setUnitDropdownVisible] = useState(false);
  const [customCategory, setCustomCategory] = useState('');
  const [customUnit, setCustomUnit] = useState('');
  
  // Validation state
  const [formErrors, setFormErrors] = useState({
    name: '',
    category: '',
    quantity: '',
    unit: '',
  });

  // AI Analysis info
  const analysisConfidence = params.analysisConfidence ? parseFloat(params.analysisConfidence as string) : null;
  const analysisDescription = params.analysisDescription as string;
  
  // Theme colors based on user role
  const getThemeColors = () => {
    const backgroundColor = isDarkMode ? '#121212' : '#f9f9f9';
    const cardColor = isDarkMode ? '#1e1e1e' : '#ffffff';
    const textColor = isDarkMode ? '#ffffff' : '#333333';
    const placeholderColor = isDarkMode ? '#aaaaaa' : '#888888';
    const borderColor = isDarkMode ? '#333333' : '#e0e0e0';
    
    // Set primary color based on role
    let primaryColor;
    if (userRole === 'caretaker') {
      primaryColor = '#FF9800'; // Orange for caretaker
    } else if (userRole === 'admin') {
      primaryColor = '#2196F3'; // Blue for admin
    } else {
      primaryColor = '#4CAF50'; // Green for owner/default
    }
    
    const errorColor = '#f44336';
    
    return {
      backgroundColor,
      cardColor,
      textColor,
      placeholderColor,
      borderColor,
      primaryColor,
      errorColor
    };
  };
  
  const colors = getThemeColors();
  
  // Load item data
  useEffect(() => {
    const loadItem = async () => {
      if (!params.id) {
        setIsNewItem(true);
        setLoading(false);
        return;
      }
      
      try {
        const itemData = await getInventoryItemById(params.id as string, selectedFarm?.id);
        if (itemData) {
          setItem(itemData);
          setIsNewItem(false);
          
          // Initialize form data from item
          setFormData({
            name: itemData.name || '',
            category: itemData.category || '',
            quantity: itemData.quantity ? itemData.quantity.toString() : '',
            unit: itemData.unit || '',
            minQuantity: itemData.minQuantity ? itemData.minQuantity.toString() : '0',
            description: itemData.description || '',
            supplier: itemData.supplier || '',
            price: itemData.price ? itemData.price.toString() : '',
            location: itemData.location || selectedFarm?.id || '',
          });
          
          // Set image and dates
          setImageUri(itemData.imageUrl || null);
          setExpiryDate(itemData.expiryDate ? new Date(itemData.expiryDate) : null);
          setPurchaseDate(itemData.purchaseDate ? new Date(itemData.purchaseDate) : null);
          
          // Check if category is custom
          if (itemData.category && !CATEGORIES.includes(itemData.category) && itemData.category !== 'Other (Custom)') {
            setCustomCategory(itemData.category);
          }
          
          // Check if unit is custom
          if (itemData.unit && !UNITS.includes(itemData.unit) && itemData.unit !== 'Other (Custom)') {
            setCustomUnit(itemData.unit);
          }
        }
      } catch (error) {
        console.error('Error loading item:', error);
        Alert.alert(t('common.error'), t('inventory.loadError'));
      } finally {
        setLoading(false);
      }
    };
    
    loadItem();
  }, [params.id, selectedFarm]);
  
  // Handle form submission
  const handleSubmit = async () => {
    // Validate form
    if (!validateForm()) {
      return;
    }
    
    if (!user) {
      Alert.alert(t('common.error'), t('auth.loginRequired'));
      return;
    }
    
    setSubmitting(true);
    
    try {
      // Determine final category and unit
      const finalCategory = customCategory || formData.category || '';
      const finalUnit = customUnit || formData.unit || '';

      let finalImageUrl = imageUri;

      // Upload image to Firebase Storage if a new local image is selected
      if (imageUri && imageUri.startsWith('file://')) {
        console.log("Uploading inventory image to Storage...");

        // Delete old image if it exists and is from Firebase Storage
        if (!isNewItem && item?.imageUrl) {
          await deleteImageFromStorage(item.imageUrl);
        }

        // Upload new image
        const itemId = isNewItem ? `temp_${Date.now()}_${Math.random().toString(36).substring(2, 15)}` : (params.id as string);
        finalImageUrl = await uploadInventoryImage(imageUri, itemId);
        console.log("Inventory image uploaded:", finalImageUrl);
      }

      // Create item data
      const itemData = {
        name: formData.name || '',
        category: finalCategory,
        quantity: parseInt(formData.quantity) || 0,
        unit: finalUnit,
        minQuantity: parseInt(formData.minQuantity) || 0,
        description: formData.description || undefined,
        supplier: formData.supplier || undefined,
        price: formData.price ? parseFloat(formData.price) : undefined,
        location: formData.location || selectedFarm?.id || '',
        imageUrl: finalImageUrl || undefined,
        expiryDate: expiryDate ? expiryDate.toISOString() : undefined,
        purchaseDate: purchaseDate ? purchaseDate.toISOString() : undefined,
      };
      
      if (isNewItem) {
        // Create new item
        await createInventoryItem(itemData, user.uid);
        Alert.alert(t('common.success'), t('inventory.itemAdded'), [
          { text: t('common.ok'), onPress: () => router.back() }
        ]);
      } else {
        // Update existing item
        await updateInventoryItem(params.id as string, itemData, user.uid);
        Alert.alert(t('common.success'), t('inventory.itemUpdated'), [
          { text: t('common.ok'), onPress: () => router.back() }
        ]);
      }
    } catch (error) {
      console.error('Error saving item:', error);
      Alert.alert(t('common.error'), isNewItem ? t('inventory.addError') : t('inventory.updateError'));
    } finally {
      setSubmitting(false);
    }
  };
  
  // Validate form
  const validateForm = () => {
    let valid = true;
    const errors = {
      name: '',
      category: '',
      quantity: '',
      unit: '',
    };
    
    if (!formData.name || !formData.name.trim()) {
      errors.name = t('inventory.itemNameRequired');
      valid = false;
    }
    
    if ((!formData.category || (formData.category && !formData.category.trim())) && (!customCategory || !customCategory.trim())) {
      errors.category = t('inventory.categoryRequired');
      valid = false;
    }
    
    if (!formData.quantity || !formData.quantity.trim() || isNaN(Number(formData.quantity))) {
      errors.quantity = t('inventory.validQuantityRequired');
      valid = false;
    } else if (Number(formData.quantity) < 0) {
      errors.quantity = t('inventory.positiveQuantityRequired');
      valid = false;
    }
    
    if ((!formData.unit || (formData.unit && !formData.unit.trim())) && (!customUnit || !customUnit.trim())) {
      errors.unit = t('inventory.unitRequired');
      valid = false;
    }
    
    setFormErrors(errors);
    return valid;
  };
  
  // Handle category selection
  const handleCategorySelect = (category: string) => {
    if (category === 'Other (Custom)') {
      setFormData({ ...formData, category: '' });
      setCustomCategory('');
    } else {
      setFormData({ ...formData, category });
      setCustomCategory('');
    }
    setCategoryDropdownVisible(false);
  };
  
  // Handle unit selection
  const handleUnitSelect = (unit: string) => {
    if (unit === 'Other (Custom)') {
      setFormData({ ...formData, unit: '' });
      setCustomUnit('');
    } else {
      setFormData({ ...formData, unit });
      setCustomUnit('');
    }
    setUnitDropdownVisible(false);
  };
  
  // Handle custom category input
  const handleCustomCategoryChange = (text: string) => {
    setCustomCategory(text);
    setFormData({ ...formData, category: text });
  };
  
  // Handle custom unit input
  const handleCustomUnitChange = (text: string) => {
    setCustomUnit(text);
    setFormData({ ...formData, unit: text });
  };
  
  // Pick image from gallery
  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });

      if (!result.canceled) {
        setImageUri(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(t('common.error'), t('inventory.imagePickError'));
    }
  };
  
  // Take photo with camera
  const takePhoto = async () => {
    if (Platform.OS === 'web') {
      Alert.alert(t('common.notAvailable'), t('inventory.cameraNotAvailableWeb'));
      return;
    }
    
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(t('common.permissionRequired'), t('inventory.cameraPermissionRequired'));
        return;
      }
      
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });
      
      if (!result.canceled) {
        setImageUri(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert(t('common.error'), t('inventory.cameraError'));
    }
  };
  
  // Date picker handlers
  const onExpiryDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || expiryDate;
    setShowExpiryDatePicker(Platform.OS === 'ios');
    if (currentDate) {
      setExpiryDate(currentDate);
    }
  };

  const onPurchaseDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || purchaseDate;
    setShowPurchaseDatePicker(Platform.OS === 'ios');
    if (currentDate) {
      setPurchaseDate(currentDate);
    }
  };
  
  // Format date for display
  const formatDate = (date: Date | null) => {
    if (!date) return '';
    return date.toLocaleDateString();
  };
  
  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundColor }]}>
        <Stack.Screen
          options={{
            title: isNewItem ? t('inventory.addItem') : t('inventory.editItem'),
            headerStyle: {
              backgroundColor: colors.primaryColor,
            },
            headerTintColor: '#fff',
            headerLeft: () => (
              <TouchableOpacity onPress={() => router.back()} style={{ marginLeft: 16 }}>
                <ArrowLeft size={24} color="#fff" />
              </TouchableOpacity>
            ),
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primaryColor} />
        </View>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: isNewItem ? t('inventory.addItem') : t('inventory.editItem'),
          headerStyle: {
            backgroundColor: colors.primaryColor,
          },
          headerTintColor: '#fff',
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} style={{ marginLeft: 16 }}>
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.content}>
            {/* Header Banner */}
            <LinearGradient
              colors={isDarkMode
                ? [colors.primaryColor, `${colors.primaryColor}80`]
                : [`${colors.primaryColor}20`, `${colors.primaryColor}10`]
              }
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.headerBanner}
            >
              <Text style={[styles.headerTitle, { color: isDarkMode ? '#fff' : colors.primaryColor, textAlign: isRTL ? 'right' : 'left' }]}>
                {isNewItem ? t('inventory.addItem') : t('inventory.editItem')}
              </Text>
              <Text style={[styles.headerSubtitle, { color: isDarkMode ? '#eee' : colors.primaryColor + 'CC', textAlign: isRTL ? 'right' : 'left' }]}>
                {isNewItem
                  ? t('inventory.addItemDescription')
                  : t('inventory.updateItemDescription', { name: formData.name })}
              </Text>
            </LinearGradient>

            {/* AI Analysis Banner */}
            {analysisConfidence && analysisDescription && isNewItem && (
              <View style={[styles.aiAnalysisBanner, { backgroundColor: colors.primaryColor + '15', borderColor: colors.primaryColor + '30' }]}>
                <View style={styles.aiAnalysisHeader}>
                  <View style={[styles.aiAnalysisIcon, { backgroundColor: colors.primaryColor }]}>
                    <Text style={styles.aiAnalysisIconText}>AI</Text>
                  </View>
                  <View style={styles.aiAnalysisInfo}>
                    <Text style={[styles.aiAnalysisTitle, { color: colors.primaryColor }]}>
                      {t('chat.aiAnalysisResults')}
                    </Text>
                    <Text style={[styles.aiAnalysisConfidence, { color: colors.textColor }]}>
                      {t('chat.confidence')}: {Math.round(analysisConfidence * 100)}%
                    </Text>
                  </View>
                </View>
                <Text style={[styles.aiAnalysisDescription, { color: colors.textColor }]}>
                  {analysisDescription}
                </Text>
              </View>
            )}

            {/* Image Picker */}
            <View style={styles.imagePickerContainer}>
              {imageUri ? (
                <View style={styles.selectedImageContainer}>
                  <Image source={{ uri: imageUri }} style={styles.selectedImage} />
                  <LinearGradient
                    colors={['transparent', 'rgba(0,0,0,0.7)']}
                    style={styles.imageOverlay}
                  />
                  <View style={[styles.imageActions, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                    <TouchableOpacity
                      style={[styles.imageActionButton, { backgroundColor: colors.primaryColor }]}
                      onPress={pickImage}
                    >
                      <Text style={styles.imageActionText}>{t('inventory.changeImage')}</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.imageActionButton, { backgroundColor: colors.errorColor }]}
                      onPress={() => setImageUri(null)}
                    >
                      <Text style={styles.imageActionText}>{t('inventory.removeImage')}</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : (
                <View style={[styles.imagePlaceholder, { borderColor: colors.borderColor }]}>
                  <View style={[styles.imageButtonsContainer, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                    <TouchableOpacity
                      style={[styles.imageButton, { backgroundColor: colors.primaryColor }]}
                      onPress={pickImage}
                    >
                      <Upload size={20} color="#fff" />
                      <Text style={styles.imageButtonText}>{t('common.gallery')}</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[styles.imageButton, { backgroundColor: colors.primaryColor }]}
                      onPress={takePhoto}
                    >
                      <Camera size={20} color="#fff" />
                      <Text style={styles.imageButtonText}>{t('common.camera')}</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            </View>
            
            {/* Form Card */}
            <View style={[styles.formCard, { backgroundColor: colors.cardColor, borderColor: colors.borderColor }]}>
              <Text style={[styles.formSectionTitle, { color: colors.textColor, textAlign: isRTL ? 'right' : 'left' }]}>
                {t('inventory.itemInformation')}
              </Text>
              
              {/* Name */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.textColor, textAlign: isRTL ? 'right' : 'left' }]}>
                  {t('inventory.itemName')} *
                </Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: colors.textColor,
                      borderColor: formErrors.name ? colors.errorColor : colors.borderColor,
                      backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                      textAlign: isRTL ? 'right' : 'left',
                      writingDirection: isRTL ? 'rtl' : 'ltr',
                    }
                  ]}
                  placeholder={t('inventory.enterItemName')}
                  placeholderTextColor={colors.placeholderColor}
                  value={formData.name}
                  onChangeText={(text) => setFormData({ ...formData, name: text })}
                />
                {formErrors.name ? (
                  <Text style={[styles.errorText, { color: colors.errorColor, textAlign: isRTL ? 'right' : 'left' }]}>
                    {formErrors.name}
                  </Text>
                ) : null}
              </View>
              
              {/* Category */}
              <View style={[styles.formGroup, { zIndex: 3 }]}>
                <Text style={[styles.label, { color: colors.textColor, textAlign: isRTL ? 'right' : 'left' }]}>
                  {t('inventory.category')} *
                </Text>
                <View>
                  <TouchableOpacity
                    style={[
                      styles.dropdownButton,
                      {
                        borderColor: formErrors.category ? colors.errorColor : colors.borderColor,
                        backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                        flexDirection: isRTL ? 'row-reverse' : 'row',
                      }
                    ]}
                    onPress={() => {
                      setCategoryDropdownVisible(!categoryDropdownVisible);
                      setUnitDropdownVisible(false);
                    }}
                  >
                    <Text
                      style={[
                        styles.dropdownButtonText,
                        {
                          color: formData.category ? colors.textColor : colors.placeholderColor,
                          textAlign: isRTL ? 'right' : 'left',
                          flex: 1,
                        },
                      ]}
                    >
                      {formData.category || t('inventory.selectCategory')}
                    </Text>
                    <ChevronDown size={20} color={colors.placeholderColor} />
                  </TouchableOpacity>
                  
                  {categoryDropdownVisible && (
                    <View 
                      style={[
                        styles.dropdown, 
                        { 
                          backgroundColor: colors.cardColor, 
                          borderColor: colors.borderColor,
                          ...Platform.select({
                            web: {
                              position: "absolute",
                              top: 45,
                              left: 0,
                              right: 0,
                              zIndex: 1000,
                            },
                            default: {
                              position: "absolute",
                              top: 45,
                              left: 0,
                              right: 0,
                              zIndex: 1000,
                            },
                          }),
                        }
                      ]}
                    >
                      <ScrollView style={{ maxHeight: 200 }} nestedScrollEnabled={true}>
                        {CATEGORIES.map((category) => (
                          <TouchableOpacity
                            key={category}
                            style={[
                              styles.dropdownItem,
                              formData.category === category && styles.selectedDropdownItem,
                              { borderBottomColor: colors.borderColor }
                            ]}
                            onPress={() => handleCategorySelect(category)}
                          >
                            <Text 
                              style={[
                                styles.dropdownItemText, 
                                { 
                                  color: formData.category === category ? colors.primaryColor : colors.textColor,
                                  fontWeight: formData.category === category ? "600" : "normal"
                                }
                              ]}
                            >
                              {category}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </ScrollView>
                    </View>
                  )}
                  
                  {(formData.category === "Other (Custom)" || customCategory) && (
                    <TextInput
                      style={[
                        styles.input,
                        {
                          color: colors.textColor,
                          borderColor: formErrors.category ? colors.errorColor : colors.borderColor,
                          backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                          marginTop: 8,
                          textAlign: isRTL ? 'right' : 'left',
                          writingDirection: isRTL ? 'rtl' : 'ltr',
                        }
                      ]}
                      placeholder={t('inventory.enterCustomCategory')}
                      placeholderTextColor={colors.placeholderColor}
                      value={customCategory}
                      onChangeText={handleCustomCategoryChange}
                    />
                  )}
                </View>
                {formErrors.category ? (
                  <Text style={[styles.errorText, { color: colors.errorColor, textAlign: isRTL ? 'right' : 'left' }]}>
                    {formErrors.category}
                  </Text>
                ) : null}
              </View>
              
              <Text style={[styles.formSectionTitle, { color: colors.textColor, marginTop: 16, textAlign: isRTL ? 'right' : 'left' }]}>
                {t('inventory.quantityDetails')}
              </Text>

              {/* Quantity and Unit */}
              <View style={[styles.formRow, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <View style={[styles.formGroup, { flex: 1, marginRight: isRTL ? 0 : 8, marginLeft: isRTL ? 8 : 0 }]}>
                  <Text style={[styles.label, { color: colors.textColor, textAlign: isRTL ? 'right' : 'left' }]}>
                    {t('inventory.quantity')} *
                  </Text>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: colors.textColor,
                        borderColor: formErrors.quantity ? colors.errorColor : colors.borderColor,
                        backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                        textAlign: isRTL ? 'right' : 'left',
                        writingDirection: isRTL ? 'rtl' : 'ltr',
                      }
                    ]}
                    placeholder={t('inventory.enterQuantity')}
                    placeholderTextColor={colors.placeholderColor}
                    value={formData.quantity}
                    onChangeText={(text) => setFormData({ ...formData, quantity: text })}
                    keyboardType="numeric"
                  />
                  {formErrors.quantity ? (
                    <Text style={[styles.errorText, { color: colors.errorColor, textAlign: isRTL ? 'right' : 'left' }]}>
                      {formErrors.quantity}
                    </Text>
                  ) : null}
                </View>
                
                <View style={[styles.formGroup, { flex: 1, zIndex: 2 }]}>
                  <Text style={[styles.label, { color: colors.textColor, textAlign: isRTL ? 'right' : 'left' }]}>
                    {t('inventory.unit')} *
                  </Text>
                  <View>
                    <TouchableOpacity
                      style={[
                        styles.dropdownButton,
                        {
                          borderColor: formErrors.unit ? colors.errorColor : colors.borderColor,
                          backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                          flexDirection: isRTL ? 'row-reverse' : 'row',
                        }
                      ]}
                      onPress={() => {
                        setUnitDropdownVisible(!unitDropdownVisible);
                        setCategoryDropdownVisible(false);
                      }}
                    >
                      <Text
                        style={[
                          styles.dropdownButtonText,
                          {
                            color: formData.unit ? colors.textColor : colors.placeholderColor,
                            textAlign: isRTL ? 'right' : 'left',
                            flex: 1,
                          },
                        ]}
                      >
                        {formData.unit || t('inventory.selectUnit')}
                      </Text>
                      <ChevronDown size={20} color={colors.placeholderColor} />
                    </TouchableOpacity>
                    
                    {unitDropdownVisible && (
                      <View 
                        style={[
                          styles.dropdown, 
                          { 
                            backgroundColor: colors.cardColor, 
                            borderColor: colors.borderColor,
                            ...Platform.select({
                              web: {
                                position: "absolute",
                                top: 45,
                                left: 0,
                                right: 0,
                                zIndex: 1000,
                              },
                              default: {
                                position: "absolute",
                                top: 45,
                                left: 0,
                                right: 0,
                                zIndex: 1000,
                              },
                            }),
                          }
                        ]}
                      >
                        <ScrollView style={{ maxHeight: 200 }} nestedScrollEnabled={true}>
                          {UNITS.map((unit) => (
                            <TouchableOpacity
                              key={unit}
                              style={[
                                styles.dropdownItem,
                                formData.unit === unit && styles.selectedDropdownItem,
                                { borderBottomColor: colors.borderColor }
                              ]}
                              onPress={() => handleUnitSelect(unit)}
                            >
                              <Text 
                                style={[
                                  styles.dropdownItemText, 
                                  { 
                                    color: formData.unit === unit ? colors.primaryColor : colors.textColor,
                                    fontWeight: formData.unit === unit ? "600" : "normal"
                                  }
                                ]}
                              >
                                {unit}
                              </Text>
                            </TouchableOpacity>
                          ))}
                        </ScrollView>
                      </View>
                    )}
                    
                    {(formData.unit === "Other (Custom)" || customUnit) && (
                      <TextInput
                        style={[
                          styles.input,
                          {
                            color: colors.textColor,
                            borderColor: formErrors.unit ? colors.errorColor : colors.borderColor,
                            backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                            marginTop: 8,
                            textAlign: isRTL ? 'right' : 'left',
                            writingDirection: isRTL ? 'rtl' : 'ltr',
                          }
                        ]}
                        placeholder={t('inventory.enterCustomUnit')}
                        placeholderTextColor={colors.placeholderColor}
                        value={customUnit}
                        onChangeText={handleCustomUnitChange}
                      />
                    )}
                  </View>
                  {formErrors.unit ? (
                    <Text style={[styles.errorText, { color: colors.errorColor, textAlign: isRTL ? 'right' : 'left' }]}>
                      {formErrors.unit}
                    </Text>
                  ) : null}
                </View>
              </View>
              
              {/* Min Quantity */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.textColor, textAlign: isRTL ? 'right' : 'left' }]}>
                  {t('inventory.minimumStock')}
                </Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: colors.textColor,
                      borderColor: colors.borderColor,
                      backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                      textAlign: isRTL ? 'right' : 'left',
                      writingDirection: isRTL ? 'rtl' : 'ltr',
                    }
                  ]}
                  placeholder="0"
                  placeholderTextColor={colors.placeholderColor}
                  value={formData.minQuantity}
                  onChangeText={(text) => setFormData({ ...formData, minQuantity: text })}
                  keyboardType="numeric"
                />
              </View>
              
              <Text style={[styles.formSectionTitle, { color: colors.textColor, marginTop: 16, textAlign: isRTL ? 'right' : 'left' }]}>
                {t('inventory.additionalDetails')}
              </Text>

              {/* Dates */}
              <View style={[styles.formRow, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <View style={[styles.formGroup, { flex: 1, marginRight: isRTL ? 0 : 8, marginLeft: isRTL ? 8 : 0 }]}>
                  <Text style={[styles.label, { color: colors.textColor, textAlign: isRTL ? 'right' : 'left' }]}>
                    {t('inventory.purchaseDate')}
                  </Text>
                  <TouchableOpacity
                    style={[
                      styles.dateButton,
                      {
                        borderColor: colors.borderColor,
                        backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                        flexDirection: isRTL ? 'row-reverse' : 'row',
                      }
                    ]}
                    onPress={() => setShowPurchaseDatePicker(true)}
                  >
                    <Text
                      style={{
                        color: purchaseDate ? colors.textColor : colors.placeholderColor,
                        textAlign: isRTL ? 'right' : 'left',
                        flex: 1,
                      }}
                    >
                      {purchaseDate ? formatDate(purchaseDate) : t('common.selectDate')}
                    </Text>
                    <Calendar size={20} color={colors.placeholderColor} />
                  </TouchableOpacity>
                  {showPurchaseDatePicker && (
                    <DateTimePicker
                      value={purchaseDate || new Date()}
                      mode="date"
                      display={Platform.OS === "ios" ? "spinner" : "default"}
                      onChange={onPurchaseDateChange}
                    />
                  )}
                </View>
                
                <View style={[styles.formGroup, { flex: 1 }]}>
                  <Text style={[styles.label, { color: colors.textColor, textAlign: isRTL ? 'right' : 'left' }]}>
                    {t('inventory.expiryDate')}
                  </Text>
                  <TouchableOpacity
                    style={[
                      styles.dateButton,
                      {
                        borderColor: colors.borderColor,
                        backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                        flexDirection: isRTL ? 'row-reverse' : 'row',
                      }
                    ]}
                    onPress={() => setShowExpiryDatePicker(true)}
                  >
                    <Text
                      style={{
                        color: expiryDate ? colors.textColor : colors.placeholderColor,
                        textAlign: isRTL ? 'right' : 'left',
                        flex: 1,
                      }}
                    >
                      {expiryDate ? formatDate(expiryDate) : t('common.selectDate')}
                    </Text>
                    <Calendar size={20} color={colors.placeholderColor} />
                  </TouchableOpacity>
                  {showExpiryDatePicker && (
                    <DateTimePicker
                      value={expiryDate || new Date()}
                      mode="date"
                      display={Platform.OS === "ios" ? "spinner" : "default"}
                      onChange={onExpiryDateChange}
                    />
                  )}
                </View>
              </View>
              
              {/* Supplier */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.textColor, textAlign: isRTL ? 'right' : 'left' }]}>
                  {t('inventory.supplier')}
                </Text>
                <View style={[styles.inputWithIcon, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                  <Truck size={20} color={colors.placeholderColor} style={[styles.inputIcon, { left: isRTL ? undefined : 16, right: isRTL ? 16 : undefined }]} />
                  <TextInput
                    style={[
                      styles.inputWithPadding,
                      {
                        color: colors.textColor,
                        borderColor: colors.borderColor,
                        backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                        paddingLeft: isRTL ? 16 : 48,
                        paddingRight: isRTL ? 48 : 16,
                        textAlign: isRTL ? 'right' : 'left',
                        writingDirection: isRTL ? 'rtl' : 'ltr',
                      }
                    ]}
                    placeholder={t('inventory.enterSupplier')}
                    placeholderTextColor={colors.placeholderColor}
                    value={formData.supplier}
                    onChangeText={(text) => setFormData({ ...formData, supplier: text })}
                  />
                </View>
              </View>

              {/* Price */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.textColor, textAlign: isRTL ? 'right' : 'left' }]}>
                  {t('inventory.price')}
                </Text>
                <View style={[styles.inputWithIcon, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                  <DollarSign size={20} color={colors.placeholderColor} style={[styles.inputIcon, { left: isRTL ? undefined : 16, right: isRTL ? 16 : undefined }]} />
                  <TextInput
                    style={[
                      styles.inputWithPadding,
                      {
                        color: colors.textColor,
                        borderColor: colors.borderColor,
                        backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                        paddingLeft: isRTL ? 16 : 48,
                        paddingRight: isRTL ? 48 : 16,
                        textAlign: isRTL ? 'right' : 'left',
                        writingDirection: isRTL ? 'rtl' : 'ltr',
                      }
                    ]}
                    placeholder="0.00"
                    placeholderTextColor={colors.placeholderColor}
                    value={formData.price}
                    onChangeText={(text) => setFormData({ ...formData, price: text })}
                    keyboardType="numeric"
                  />
                </View>
              </View>
              
              {/* Description */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.textColor, textAlign: isRTL ? 'right' : 'left' }]}>
                  {t('inventory.description')}
                </Text>
                <TextInput
                  style={[
                    styles.textArea,
                    {
                      color: colors.textColor,
                      borderColor: colors.borderColor,
                      backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                      textAlign: isRTL ? 'right' : 'left',
                      writingDirection: isRTL ? 'rtl' : 'ltr',
                    }
                  ]}
                  placeholder={t('inventory.enterDescription')}
                  placeholderTextColor={colors.placeholderColor}
                  value={formData.description}
                  onChangeText={(text) => setFormData({ ...formData, description: text })}
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                />
              </View>
            </View>

            <View style={[styles.buttonContainer, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
              <TouchableOpacity
                style={[
                  styles.button,
                  styles.cancelButton,
                  {
                    borderColor: colors.borderColor,
                    backgroundColor: isDarkMode ? '#252525' : '#f5f5f5',
                    marginRight: isRTL ? 0 : 8,
                    marginLeft: isRTL ? 8 : 0,
                  }
                ]}
                onPress={() => router.back()}
              >
                <Text style={[styles.buttonText, { color: colors.textColor }]}>
                  {t('common.cancel')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.button,
                  styles.saveButton,
                  {
                    backgroundColor: colors.primaryColor,
                    marginLeft: isRTL ? 0 : 8,
                    marginRight: isRTL ? 8 : 0,
                  }
                ]}
                onPress={handleSubmit}
                disabled={submitting}
              >
                {submitting ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <Save size={20} color="#fff" />
                    <Text style={styles.saveButtonText}>
                      {isNewItem ? t('inventory.addItem') : t('common.save')}
                    </Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom: 32,
  },
  headerBanner: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "bold",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
  },
  formCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  formSectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 12,
  },
  imagePickerContainer: {
    marginBottom: 16,
  },
  imagePlaceholder: {
    height: 180,
    borderRadius: 16,
    borderWidth: 1,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageButtonsContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  imageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 12,
    gap: 8,
  },
  imageButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  selectedImageContainer: {
    position: 'relative',
    borderRadius: 16,
    overflow: 'hidden',
    height: 180,
  },
  selectedImage: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  imageActions: {
    position: 'absolute',
    bottom: 12,
    left: 12,
    right: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 12,
  },
  imageActionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
  },
  imageActionText: {
    color: '#fff',
    fontWeight: '600',
  },
  formGroup: {
    marginBottom: 16,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  inputWithIcon: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputIcon: {
    position: 'absolute',
    left: 16,
    zIndex: 1,
  },
  inputWithPadding: {
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingLeft: 48,
    fontSize: 16,
    flex: 1,
  },
  textArea: {
    height: 100,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingTop: 12,
    fontSize: 16,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  dropdownButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
  },
  dropdownButtonText: {
    fontSize: 16,
  },
  dropdown: {
    borderWidth: 1,
    borderRadius: 12,
    zIndex: 1000,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  dropdownItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  selectedDropdownItem: {
    backgroundColor: 'rgba(25, 118, 210, 0.1)',
  },
  dropdownItemText: {
    fontSize: 16,
  },
  dateButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  button: {
    flex: 1,
    height: 52,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  cancelButton: {
    marginRight: 8,
    borderWidth: 1,
  },
  saveButton: {
    marginLeft: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  // RTL specific styles
  formRowRTL: {
    flexDirection: 'row-reverse',
  },
  buttonContainerRTL: {
    flexDirection: 'row-reverse',
  },
  imageActionsRTL: {
    flexDirection: 'row-reverse',
  },
  imageButtonsContainerRTL: {
    flexDirection: 'row-reverse',
  },
  dropdownButtonRTL: {
    flexDirection: 'row-reverse',
  },
  dateButtonRTL: {
    flexDirection: 'row-reverse',
  },
  inputWithIconRTL: {
    flexDirection: 'row-reverse',
  },
  // AI Analysis Banner Styles
  aiAnalysisBanner: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  aiAnalysisHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  aiAnalysisIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  aiAnalysisIconText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  aiAnalysisInfo: {
    flex: 1,
  },
  aiAnalysisTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  aiAnalysisConfidence: {
    fontSize: 12,
    fontWeight: '500',
  },
  aiAnalysisDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
});
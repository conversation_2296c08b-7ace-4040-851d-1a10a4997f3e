import React, { createContext, useContext, useState, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from './auth-context';

type Theme = 'light' | 'dark';

interface ThemeColors {
  primary: string;
  primaryDark: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  success: string;
  warning: string;
  error: string;
  info: string;
}

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
  colors: ThemeColors;
  userColors: ThemeColors;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Role-based color schemes
const getRoleColors = (role: string, isDark: boolean): ThemeColors => {
  const baseColors = {
    background: isDark ? "#121212" : "#f8f9fa",
    surface: isDark ? "#1e1e1e" : "#ffffff",
    text: isDark ? "#ffffff" : "#333333",
    textSecondary: isDark ? "#aaaaaa" : "#666666",
    border: isDark ? "#333333" : "#e0e0e0",
    success: isDark ? "#4CAF50" : "#2E7D32",
    warning: isDark ? "#FF9800" : "#F57C00",
    error: isDark ? "#F44336" : "#D32F2F",
    info: isDark ? "#2196F3" : "#1976D2",
  };

  switch (role) {
    case 'owner':
      return {
        ...baseColors,
        primary: isDark ? "#2E7D32" : "#4CAF50",
        primaryDark: isDark ? "#1B5E20" : "#2E7D32",
      };
    case 'admin':
      return {
        ...baseColors,
        primary: isDark ? "#1976D2" : "#2196F3",
        primaryDark: isDark ? "#0D47A1" : "#1976D2",
      };
    case 'caretaker':
      return {
        ...baseColors,
        primary: isDark ? "#F57C00" : "#FF9800",
        primaryDark: isDark ? "#E65100" : "#F57C00",
      };
    default:
      return {
        ...baseColors,
        primary: isDark ? "#2E7D32" : "#4CAF50",
        primaryDark: isDark ? "#1B5E20" : "#2E7D32",
      };
  }
};

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const deviceTheme = useColorScheme() as Theme;
  const [theme, setThemeState] = useState<Theme>(deviceTheme || 'light');
  const [isLoaded, setIsLoaded] = useState(false);
  const { user } = useAuth();

  // Load saved theme on mount
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem('theme');
        if (savedTheme) {
          setThemeState(savedTheme as Theme);
        }
      } catch (error) {
        console.error('Error loading theme:', error);
      } finally {
        setIsLoaded(true);
      }
    };

    loadTheme();
  }, []);

  // Save theme when it changes
  useEffect(() => {
    if (isLoaded) {
      const saveTheme = async () => {
        try {
          await AsyncStorage.setItem('theme', theme);
        } catch (error) {
          console.error('Error saving theme:', error);
        }
      };
      saveTheme();
    }
  }, [theme, isLoaded]);

  const toggleTheme = () => {
    setThemeState(prevTheme => (prevTheme === 'light' ? 'dark' : 'light'));
  };

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
  };

  // Get colors based on user role and theme
  const isDark = theme === 'dark';
  const userRole = user?.role || 'owner';
  const colors = getRoleColors(userRole, isDark);
  const userColors = colors; // For backward compatibility

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme, colors, userColors }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define the supported languages
export type Language = 'en' | 'ur';

// Define the context type
interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: string, options?: any) => string;
  isRTL: boolean;
}

// Create the context
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Define translations
const translations = {
  en: {
    common: {
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
      cancel: 'Cancel',
      save: 'Save',
      delete: 'Delete',
      fieldRequired: 'This field is required',
      invalidNumber: 'Please enter a valid number',
      next: 'Next',
      submit: 'Submit',
      yes: 'Yes',
      no: 'No',
      skip: 'Skip',
      optional: 'Optional',
      selectDate: 'Select Date',
      edit: 'Edit',
      create: 'Create',
      update: 'Update',
      confirm: 'Confirm',
      yes: 'Yes',
      no: 'No',
      ok: 'OK',
      done: 'Done',
      next: 'Next',
      previous: 'Previous',
      back: 'Back',
      search: 'Search',
      filter: 'Filter',
      all: 'All',
      none: 'None',
      select: 'Select',
      category: 'Category',
      date: 'Date',
      time: 'Time',
      description: 'Description',
      details: 'Details',
      name: 'Name',
      quantity: 'Quantity',
      price: 'Price',
      total: 'Total',
      status: 'Status',
      actions: 'Actions',
      notes: 'Notes',
      reason: 'Reason',
      bio: 'Bio',
      tapToViewDetails: 'Tap to view details',
      notSet: 'Not set',
      notSpecified: 'Not specified',
      invalidDate: 'Invalid date',
      today: 'Today',
      yesterday: 'Yesterday',
      monday: 'Monday',
      tuesday: 'Tuesday',
      wednesday: 'Wednesday',
      thursday: 'Thursday',
      friday: 'Friday',
      saturday: 'Saturday',
      sunday: 'Sunday',
      language: 'Language',
      version: 'Version',
      kilometers: 'Kilometers',
      logout: 'Logout',
      submit: 'Submit',
      unit: 'Unit',
      noFarmSelected: 'No farm selected',
      unknownError: 'An unknown error occurred',
      unknownFarm: 'Unknown Farm',
      unknown: 'Unknown',
      selectDate: 'Select date',
      camera: 'Camera',
      gallery: 'Gallery',
      remove: 'Remove',
      play: 'Play',
      pause: 'Pause',
      remaining: 'remaining',
      farmIdRequired: 'Farm ID is required',
      pleaseSelectFarm: 'Please select a farm to view',
      contactAdmin: 'Contact admin to add',
      noDataFound: 'No data found',
      tryDifferentSearch: 'Try a different search or filter',
      requestUse: 'Request Use',
      available: 'Available',
      maintenanceStatus: 'Maintenance',
      malfunction: 'Malfunction',
      inUse: 'In Use',
      totalMachinery: 'Total Machinery',
      requestMachineryFromAdmin: 'Request Machinery from Admin',
      requestMachineryFromOwner: 'Request Machinery from Owner',
      noMachineryFound: 'No machinery found',
      loadingMachinery: 'Loading machinery...',
      farmMachinery: 'Farm Machinery',
      caretakerRequests: 'Caretaker Requests',
      myRequests: 'My Requests',
      inventoryRequests: 'Inventory Requests',
      machineryRequests: 'Machinery Requests',
      returns: 'Returns',
      request: 'Request',
      searchMachineryRequests: 'Search machinery requests...',
      searchInventoryRequests: 'Search inventory requests...',
      noInventoryRequestsFromCaretakers: 'No inventory requests from caretakers',
      noInventoryRequestsToOwner: 'No inventory requests to owner',
      noMachineryRequestsFromCaretakers: 'No machinery requests from caretakers',
      noMachineryRequestsToOwner: 'No machinery requests to owner',
      inventoryRequestsFromCaretakersWillAppearHere: 'Inventory requests from caretakers will appear here',
      yourInventoryRequestsToOwnerWillAppearHere: 'Your inventory requests to the owner will appear here',
      machineryRequestsFromCaretakersWillAppearHere: 'Machinery requests from caretakers will appear here',
      yourMachineryRequestsToOwnerWillAppearHere: 'Your machinery requests to the owner will appear here',
      yourInventoryRequestsWillAppearHere: 'Your inventory requests will appear here',
      yourMachineryRequestsWillAppearHere: 'Your machinery requests will appear here',
      requestsFromAdminsWillAppearHere: 'Requests from admins will appear here',
      machineryRequestsFromAdminsWillAppearHere: 'Machinery requests from admins will appear here',
      warning: 'Warning',
      info: 'Info',
      requestSubmittedSuccessfully: 'Request submitted successfully',
      failedToSubmitRequest: 'Failed to submit request. Please try again.',
      missingRequiredInformation: 'Missing required information',
      machineryUseRequestSubmitted: 'Machinery use request submitted successfully',
      requestToUseMachinery: 'Request to use {machineryName} ({machineryModel})',
      fuel: 'Fuel',
      nextMaintenance: 'Next Maintenance',
      odometer: 'Odometer',
      hours: 'hours',
      model: 'Model',
      year: 'Year',
      type: 'Type',
      currentFuelLevel: 'Current Fuel Level',
      fuelCapacity: 'Fuel Capacity',
      nextMaintenanceDate: 'Next Maintenance Date',
      odometerReading: 'Odometer Reading',
      working: 'Working',
      inUseOtherFarm: 'In Use Other Farm',
      isRequired: 'is required',
      isInvalid: 'is invalid',
      currentFuelLevelExceedsCapacity: 'Current fuel level cannot exceed capacity',
      notAvailable: 'Not available',
      days: 'days',
      maintenanceOverdue: 'Maintenance overdue',
      low: 'Low',
      medium: 'Medium',
      high: 'High',
      emergency: 'Emergency',
      urgencyLevel: 'Urgency Level',
      startDate: 'Start Date',
      endDate: 'End Date',
      change: 'Change',
      submitting: 'Submitting...',
      maintenance: 'Maintenance',
      bio: 'Bio',
      totalInventory: 'Total Inventory',
      activeMachinery: 'Active Machinery',
      inventoryRequest: 'Inventory Request',
      machineryRequest: 'Machinery Request',
      pending: 'Pending',
      approved: 'Approved',
      rejected: 'Rejected',
      requestMachinery: 'Request Machinery',
      requestInventory: 'Request Inventory',
      newItem: 'New Item',
      existingItem: 'Existing Item',
      requestType: 'Request Type',
      requestForUse: 'Request for Use',
      requestForFuel: 'Request for Fuel',
      requestForMaintenance: 'Request for Maintenance',
      requestForTransfer: 'Request for Transfer',
      newItemRequest: 'New Item Request',
      createdBy: 'Created By',
      createdAt: 'Created At',
      lastUpdated: 'Last Updated',
      timestamps: 'Timestamps',
      withAudio: 'With Audio',
      withImages: 'With Images',
      textOnly: 'Text Only',
      multimedia: 'Multimedia',
      audio: 'Audio',
      images: 'Images',
      text: 'Text',
      searchInNote: 'Search in note...',
      noMatchesFound: 'No matches found for',
      tryAdjustingSearch: 'Try adjusting your search terms',
      listeningAndConverting: 'Listening and converting speech to text...',
      recognizedText: 'Recognized Text',
      speakClearly: 'Speak clearly to convert your voice to text',
      stopListening: 'Stop Listening',
      listening: 'Listening...',
      addAttachments: 'Add Attachments',
      voiceNote: 'Voice Note',
      recordVoiceNote: 'Record Voice Note',
      stopRecording: 'Stop Recording',
      playVoiceNote: 'Play Voice Note',
      pauseVoiceNote: 'Pause Voice Note',
      audioRecordingReady: 'Audio recording ready',
      playingAudio: 'Playing audio...',
      tapToPlay: 'Tap to play',
      tapToPause: 'Tap to pause',
      saveNote: 'Save Note',
      saveChanges: 'Save Changes',
      createNewNote: 'Create New Note',
      documentImportantObservations: 'Document important observations and updates',
      updateNoteWithNewInfo: 'Update your note with new information',
      enterDescriptiveTitle: 'Enter a descriptive title...',
      writeDetailedNote: 'Write your detailed note here...',
      attachments: 'Attachments',
      imagesCount: 'Images ({count})',
      audioRecording: 'Audio Recording',
      permissionsRequired: 'Permissions Required',
      grantCameraAndMicPermissions: 'Please grant camera and microphone permissions to use all features.',
      noteCreatedSuccessfully: 'Note created successfully',
      failedToCreateNote: 'Failed to create note. Please try again.',
      enterTitleForNote: 'Please enter a title for your note',
      enterContentForNote: 'Please enter some content for your note',
      selectFarmForNote: 'Please select a farm for your note',
      loadingNote: 'Loading note...',
      noteNotFound: 'Note not found',
      goBack: 'Go Back',
      deleteNote: 'Delete Note',
      areYouSureDeleteNote: 'Are you sure you want to delete this note?',
      noteDeletedSuccessfully: 'Note deleted successfully',
      failedToDeleteNote: 'Failed to delete note',
      failedToPlayAudio: 'Failed to play audio',
      editNote: 'Edit Note',
      noteUpdatedSuccessfully: 'Note updated successfully',
      failedToUpdateNote: 'Failed to update note. Please try again.',
      noteDetails: 'Note Details',
      noResultsFound: 'No results found',
      phone: 'Phone',
      // New filter and sorting translations
      filters: 'Filters',
      sortBy: 'Sort By',
      sortOrder: 'Sort Order',
      ascending: 'Ascending',
      descending: 'Descending',
      clearFilters: 'Clear Filters',
      applyFilters: 'Apply Filters',
      filterBy: 'Filter By',
      showAll: 'Show All',
      showOnly: 'Show Only',
      dateRange: 'Date Range',
      from: 'From',
      to: 'To',
      selectAll: 'Select All',
      deselectAll: 'Deselect All',
      noFiltersApplied: 'No filters applied',
      filtersApplied: 'Filters applied',
      resetFilters: 'Reset Filters',
      advancedFilters: 'Advanced Filters',
      quickFilters: 'Quick Filters',
      customFilter: 'Custom Filter',
      // Navigation and UI
      home: 'Home',
      dashboard: 'Dashboard',
      settings: 'Settings',
      profile: 'Profile',
      menu: 'Menu',
      close: 'Close',
      open: 'Open',
      expand: 'Expand',
      collapse: 'Collapse',
      refresh: 'Refresh',
      reload: 'Reload',
      retry: 'Retry',
      // Form elements
      required: 'Required',
      optional: 'Optional',
      placeholder: 'Enter value...',
      selectOption: 'Select an option',
      chooseFile: 'Choose File',
      uploadFile: 'Upload File',
      dragAndDrop: 'Drag and drop files here',
      browse: 'Browse',
      // Status indicators
      active: 'Active',
      inactive: 'Inactive',
      enabled: 'Enabled',
      disabled: 'Disabled',
      online: 'Online',
      offline: 'Offline',
      connected: 'Connected',
      disconnected: 'Disconnected',
      synced: 'Synced',
      syncing: 'Syncing',
      failed: 'Failed',
      completed: 'Completed',
      inProgress: 'In Progress',
      // Validation messages
      fieldRequired: 'This field is required',
      invalidFormat: 'Invalid format',
      invalidEmail: 'Invalid email address',
      invalidPhone: 'Invalid phone number',
      passwordTooShort: 'Password is too short',
      passwordsDoNotMatch: 'Passwords do not match',
      valueTooLow: 'Value is too low',
      valueTooHigh: 'Value is too high',
      invalidDate: 'Invalid date',
      dateInPast: 'Date cannot be in the past',
      dateInFuture: 'Date cannot be in the future',
      // Action confirmations
      confirmDelete: 'Are you sure you want to delete this item?',
      confirmSave: 'Are you sure you want to save changes?',
      confirmCancel: 'Are you sure you want to cancel? Unsaved changes will be lost.',
      confirmLogout: 'Are you sure you want to logout?',
      confirmReset: 'Are you sure you want to reset all data?',
      // Success messages
      savedSuccessfully: 'Saved successfully',
      deletedSuccessfully: 'Deleted successfully',
      updatedSuccessfully: 'Updated successfully',
      createdSuccessfully: 'Created successfully',
      uploadedSuccessfully: 'Uploaded successfully',
      // Error messages
      saveFailed: 'Failed to save',
      deleteFailed: 'Failed to delete',
      updateFailed: 'Failed to update',
      createFailed: 'Failed to create',
      uploadFailed: 'Failed to upload',
      loadFailed: 'Failed to load',
      connectionError: 'Connection error',
      serverError: 'Server error',
      networkError: 'Network error',
      timeoutError: 'Request timeout',
      // Pagination
      page: 'Page',
      of: 'of',
      itemsPerPage: 'Items per page',
      showingItems: 'Showing {start} to {end} of {total} items',
      noItemsToShow: 'No items to show',
      loadMore: 'Load More',
      // Time and date
      now: 'Now',
      justNow: 'Just now',
      minutesAgo: '{count} minutes ago',
      hoursAgo: '{count} hours ago',
      daysAgo: '{count} days ago',
      weeksAgo: '{count} weeks ago',
      monthsAgo: '{count} months ago',
      yearsAgo: '{count} years ago',
      // File operations
      download: 'Download',
      upload: 'Upload',
      import: 'Import',
      export: 'Export',
      share: 'Share',
      copy: 'Copy',
      move: 'Move',
      rename: 'Rename',
      duplicate: 'Duplicate',
      // Permissions
      permissionDenied: 'Permission denied',
      permissionRequired: 'Permission required',
      grantPermission: 'Grant Permission',
      cameraPermission: 'Camera permission',
      microphonePermission: 'Microphone permission',
      storagePermission: 'Storage permission',
      locationPermission: 'Location permission',
      // Farm detail screen specific
      farmDetails: 'Farm Details',
      loadingFarmDetails: 'Loading farm details...',
      farmNotFound: 'Farm Not Found',
      farmNotFoundMessage: 'The farm you are looking for could not be found.',
      basicInformation: 'Basic Information',
      farmType: 'Farm Type',
      type: 'Type',
      createdAt: 'Created At',
      size: 'Size',
      location: 'Location',
      openInMaps: 'Open in Maps',
      coordinates: 'Coordinates',
      statistics: 'Statistics',
      caretakers: 'Caretakers',
      inventoryItems: 'Inventory Items',
      assignedCaretakers: 'Assigned Caretakers',
      noCaretakersFound: 'No caretakers found for the assigned IDs.',
      farmInformation: 'Farm Information',
      created: 'Created',
      owner: 'Owner',
      unknownOwner: 'Unknown Owner',
      couldNotOpenMaps: 'Could not open maps',
      deleteFarm: 'Delete Farm',
      deleteFarmConfirmation: 'Are you sure you want to delete {farmName}? This action cannot be undone and will remove all associated data.',
      farmDeletedSuccessfully: 'Farm deleted successfully',
      failedToDeleteFarm: 'Failed to delete farm',
      // Inventory item detail screen specific
      itemDetails: 'Item Details',
      loadingItemDetails: 'Loading item details...',
      itemNotFound: 'Item Not Found',
      itemNotFoundMessage: 'The item you are looking for does not exist or has been removed.',
      backToInventory: 'Back to Inventory',
      searchInItem: 'Search in this item...',
      deleteItem: 'Delete Item',
      deleteItemConfirmation: 'Are you sure you want to delete this item? This action cannot be undone.',
      itemDeletedSuccessfully: 'Item deleted successfully',
      failedToDeleteItem: 'Failed to delete item',
      sharingNotAvailableWeb: 'Sharing is not available on web',
      failedToShareItem: 'Failed to share item',
      editItem: 'Edit Item',
      requestTransfer: 'Request Transfer',
      itemInformation: 'Item Information',
      currentStock: 'Current Stock',
      minimumStock: 'Minimum Stock',
      farmLocation: 'Farm Location',
      purchaseDate: 'Purchase Date',
      expiryDate: 'Expiry Date',
      supplier: 'Supplier',
      notesAndDescription: 'Notes & Description',
      noMatchesFoundFor: 'No matches found for "{query}"',
      clearSearch: 'Clear search',
      expired: 'EXPIRED',
      lowStock: 'LOW STOCK',
      expiringSoon: 'EXPIRES SOON',
      inStock: 'IN STOCK',
      itemExpired: 'Item Expired',
      itemExpiredMessage: 'This item expired on {date}',
      lowStockAlert: 'Low Stock Alert',
      lowStockMessage: 'Only {quantity} {unit} remaining (Minimum: {minQuantity} {unit})',
      expiringSoonAlert: 'Expiring Soon',
      expiringSoonMessage: 'This item will expire on {date}',
      // Additional common translations
      diesel: 'Diesel',
      gasoline: 'Gasoline',
      electric: 'Electric',
      hybrid: 'Hybrid',
      greenhouse: 'Greenhouse',
      orchard: 'Orchard',
      farm: 'Farm',
      admin: 'Admin',
      caretaker: 'Caretaker',
      owner: 'Owner',
      users: 'Users',
    },
    auth: {
      login: 'Login',
      signup: 'Sign Up',
      logout: 'Logout',
      email: 'Email',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      forgotPassword: 'Forgot Password?',
      resetPassword: 'Reset Password',
      dontHaveAccount: "Don't have an account?",
      alreadyHaveAccount: 'Already have an account?',
      loginSuccess: 'Login successful',
      loginError: 'Login failed',
      signupSuccess: 'Sign up successful',
      signupError: 'Sign up failed',
      logoutSuccess: 'Logout successful',
      logoutError: 'Logout failed',
      resetPasswordSuccess: 'Password reset email sent',
      resetPasswordError: 'Failed to send password reset email',
      passwordsDontMatch: 'Passwords do not match',
      invalidEmail: 'Invalid email address',
      weakPassword: 'Password is too weak',
      emailAlreadyInUse: 'Email is already in use',
      userNotFound: 'User not found',
      wrongPassword: 'Wrong password',
      tooManyRequests: 'Too many requests, try again later',
      networkError: 'Network error, check your connection',
      unknownError: 'An unknown error occurred',
      loginRequired: 'Login required',
      // New signup fields
      cnic: 'CNIC',
      address: 'Address',
      profileImage: 'Profile Image',
      enterCnic: 'Enter CNIC number',
      enterAddress: 'Enter your address',
      selectProfileImage: 'Select profile image',
      cnicRequired: 'CNIC is required',
      addressRequired: 'Address is required',
      invalidCnic: 'Invalid CNIC format',
      createAccount: 'Create Account',
      accountCreated: 'Account created successfully',
      redirectingToFarmSetup: 'Redirecting to farm setup...',
    },
    dashboard: {
      title: 'Dashboard',
      welcome: 'Welcome',
      recentActivity: 'Recent Activity',
      viewAll: 'View All',
      stats: 'Stats',
      totalItems: 'Total Items',
      lowStockItems: 'Low Stock',
      expiryAlerts: 'Expiry Alerts',
      pendingRequests: 'Pending Requests',
      caretakers: 'Caretakers',
      totalUsers: 'Total Users',
      totalFarms: 'Total Farms',
      availableItems: 'Available Items',
      approvedRequests: 'Approved Requests',
      quickActions: 'Quick Actions',
      viewInventory: 'View Inventory',
      createRequest: 'Create Request',
      addNote: 'Add Note',
      viewReports: 'View Reports',
      manageUsers: 'Manage Users',
      manageFarms: 'Manage Farms',
      noRecentActivity: 'No recent activity',
      activityRelatedTo: 'Activity related to {item}',
      totalInventory: 'Total Inventory',
      activeMachinery: 'Active Machinery',
      inventoryRequests: 'Inventory Requests',
      machineryRequests: 'Machinery Requests',
      alertsNotifications: 'Alerts & Notifications',
      lowStockAlert: 'Low Stock Alert',
      lowStockMessage: '{count} items are running low on stock',
      expiryAlert: 'Expiry Alert',
      expiryMessage: '{count} items are expiring soon',
      maintenanceAlert: 'Maintenance Alert',
      maintenanceMessage: '{count} machinery items need maintenance',
      pendingRequestsMessage: '{count} requests are pending approval',
      myPendingRequests: 'My Pending Requests',
      myPendingRequestsMessage: 'You have {count} pending requests',
      myMachineryRequestsMessage: 'You have {count} machinery requests',
      addInventory: 'Add Inventory',
      addMachinery: 'Add Machinery',
      requestMachinery: 'Request Machinery',
      manageRequests: 'Manage Requests',
      viewRequests: 'View Requests',
      myInventoryRequests: 'My Inventory Requests',
      myMachineryRequests: 'My Machinery Requests',
    },
    profile: {
      title: 'Profile',
      editProfile: 'Edit Profile',
      viewProfile: 'View Profile',
      personalInfo: 'Personal Information',
      personalInformation: 'Personal Information',
      contactInfo: 'Contact Information',
      roleInfo: 'Role Information',
      farmInfo: 'Farm Information',
      activityInfo: 'Activity Information',
      name: 'Name',
      email: 'Email',
      phone: 'Phone',
      role: 'Role',
      status: 'Status',
      lastLogin: 'Last Login',
      memberSince: 'Member Since',
      assignedFarms: 'Assigned Farms',
      recentActivity: 'Recent Activity',
      noRecentActivity: 'No recent activity',
      viewAllActivity: 'View All Activity',
      changePassword: 'Change Password',
      logout: 'Logout',
      profileUpdated: 'Profile updated successfully',
      profileUpdateError: 'Failed to update profile',
      uploadPhoto: 'Upload Photo',
      removePhoto: 'Remove Photo',
      photoUpdated: 'Photo updated successfully',
      photoUpdateError: 'Failed to update photo',
      photoRemoved: 'Photo removed successfully',
      photoRemoveError: 'Failed to remove photo',
      owner: 'Owner',
      admin: 'Admin',
      caretaker: 'Caretaker',
      myAssignedFarm: 'My Assigned Farm',
      myRequests: 'My Requests',
      viewRequests: 'View and manage your requests',
      darkMode: 'Dark Mode',
      lightMode: 'Light Mode',
      yourName: 'Your Name',
      yourPhoneNumber: 'Your Phone Number',
      notProvided: 'Not Provided',
      noBioProvided: 'No bio provided',
      settings: 'Settings',
      tellUsAboutYourself: 'Tell us about yourself',
      english: 'English',
      urdu: 'اردو',
      notSet: 'Not set',
      accountInfo: 'Account Information',
      logoutConfirm: 'Are you sure you want to logout?',
      imageUploadError: 'Failed to upload image',
      // New profile fields
      cnic: 'CNIC',
      address: 'Address',
      profileImage: 'Profile Image',
      updateProfileImage: 'Update Profile Image',
      cnicNumber: 'CNIC Number',
      fullAddress: 'Full Address',
      bio: 'Bio',
      gender: 'Gender',
      dateOfBirth: 'Date of Birth',
      saveChanges: 'Save Changes',
      editingProfile: 'Editing Profile',
    },
    changePassword: {
      title: 'Change Password',
      subtitle: 'Update your password to keep your account secure',
      updateSecurityDescription: 'Keep your account secure with a strong password',
      currentPassword: 'Current Password',
      newPassword: 'New Password',
      confirmPassword: 'Confirm New Password',
      enterCurrentPassword: 'Enter your current password',
      enterNewPassword: 'Enter your new password',
      confirmNewPassword: 'Confirm your new password',
      changePassword: 'Change Password',
      passwordStrength: 'Password Strength',
      weak: 'Weak',
      medium: 'Medium',
      good: 'Good',
      strong: 'Strong',
      passwordRequirements: 'Password Requirements',
      minLength: 'At least 8 characters',
      hasUpperCase: 'At least one uppercase letter',
      hasLowerCase: 'At least one lowercase letter',
      hasNumbers: 'At least one number',
      hasSpecialChar: 'At least one special character',
      passwordsMatch: 'Passwords match',
      currentPasswordRequired: 'Current password is required',
      newPasswordRequired: 'New password is required',
      confirmPasswordRequired: 'Please confirm your new password',
      passwordsDoNotMatch: 'Passwords do not match',
      newPasswordSameAsCurrent: 'New password must be different from current password',
      success: 'Success',
      error: 'Error',
      passwordChangedSuccessfully: 'Your password has been changed successfully',
      failedToChangePassword: 'Failed to change password. Please try again.',
      securityTips: 'Security Tips',
      securityTipsDescription: 'Use a strong password with a mix of letters, numbers, and symbols. Avoid using personal information or common words.',
    },
    inventory: {
      title: 'Inventory',
      addItem: 'Add Item',
      editItem: 'Edit Item',
      deleteItem: 'Delete Item',
      itemDetails: 'Item Details',
      addItemDescription: 'Add a new item to your inventory',
      updateItemDescription: 'Update {name} details',
      searchItems: 'Search items...',
      searchPlaceholder: 'Search by name, category, or supplier...',
      basicInformation: 'Basic Information',
      additionalDetails: 'Additional Details',
      itemName: 'Item Name',
      enterItemName: 'Enter item name',
      category: 'Category',
      selectCategory: 'Select category',
      orEnterCustomCategory: 'Or enter custom category',
      enterCustomCategory: 'Enter custom category',
      quantity: 'Quantity',
      enterQuantity: 'Enter quantity',
      unit: 'Unit',
      selectUnit: 'Select unit',
      enterCustomUnit: 'Enter custom unit',
      minimumQuantity: 'Minimum Quantity',
      enterMinQuantity: 'Enter minimum quantity',
      supplier: 'Supplier',
      enterSupplier: 'Enter supplier name',
      purchasePrice: 'Purchase Price',
      unitPrice: 'Unit Price',
      totalPrice: 'Total Price',
      purchaseDate: 'Purchase Date',
      supplier: 'Supplier',
      validPriceRequired: 'Valid price is required',
      purchaseDateRequired: 'Purchase date is required',
      estimatedPrice: 'Estimated Price',
      priceNotSpecified: 'Price not specified',
      totalAmount: 'Total Amount',
      purchaseDate: 'Purchase Date',
      selectPurchaseDate: 'Select purchase date',
      isConsumable: 'Consumable Item',
      consumableDescription: 'Check if this item is consumed during use',
      enterPrice: 'Enter price',
      enterUnitPrice: 'Enter unit price',
      expiryDate: 'Expiry Date',
      selectExpiryDate: 'Select expiry date',
      description: 'Description',
      enterDescription: 'Enter description',
      itemImage: 'Item Image',
      uploadImage: 'Upload Image',
      createItem: 'Create Item',
      itemCreated: 'Item created successfully',
      createError: 'Failed to create item',
      validation: {
        nameRequired: 'Item name is required',
        categoryRequired: 'Category is required',
        quantityRequired: 'Quantity is required',
        quantityInvalid: 'Please enter a valid quantity',
        minQuantityInvalid: 'Please enter a valid minimum quantity',
        priceInvalid: 'Please enter a valid price',
        priceRequired: 'Unit price is required',
        purchaseDateRequired: 'Purchase date is required',
      },
      units: {
        pcs: 'Pieces',
        kg: 'Kilograms',
        lbs: 'Pounds',
        liters: 'Liters',
        gallons: 'Gallons',
        bags: 'Bags',
        boxes: 'Boxes',
      },
      // Category translations
      category_seeds: 'Seeds',
      category_fertilizers: 'Fertilizers',
      category_pesticides: 'Pesticides',
      category_tools: 'Tools',
      category_equipment: 'Equipment',
      category_feed: 'Feed',
      category_medication: 'Medication',
      category_vaccination: 'Vaccination',
      category_fuel: 'Fuel',
      category_other_custom: 'Other (Custom)',
      // Unit translations
      unit_kg: 'Kilograms',
      unit_g: 'Grams',
      unit_l: 'Liters',
      unit_ml: 'Milliliters',
      unit_units: 'Units',
      unit_bags: 'Bags',
      unit_boxes: 'Boxes',
      unit_bottles: 'Bottles',
      unit_packs: 'Packs',
      unit_pieces: 'Pieces',
      unit_other_custom: 'Other (Custom)',
      // Form labels and placeholders
      form_label_item_name: 'Item Name',
      form_label_category: 'Category',
      form_label_current_quantity: 'Current Quantity',
      form_label_unit: 'Unit',
      form_label_low_stock_alert_level: 'Low Stock Alert Level',
      form_label_purchase_date: 'Purchase Date',
      form_label_expiry_date: 'Expiry Date',
      form_label_supplier: 'Supplier',
      form_label_price_per_unit: 'Price per Unit',
      form_label_notes_and_description: 'Notes & Description',
      form_placeholder_enter_item_name_example: 'e.g., Wheat Seeds, Fertilizer NPK',
      form_placeholder_select_category: 'Select a category',
      form_placeholder_enter_custom_category: 'Enter custom category',
      form_placeholder_unit: 'Select unit',
      form_placeholder_enter_custom_unit: 'Enter custom unit',
      form_placeholder_select_date: 'Select date',
      form_placeholder_enter_supplier_name: 'Enter supplier name',
      form_placeholder_add_additional_notes_placeholder: 'Add any additional notes or description...',
      // Section titles
      form_section_title_basic_information: 'Basic Information',
      form_section_title_quantity_and_stock: 'Quantity & Stock',
      form_section_title_additional_details: 'Additional Details',
      section_title_item_photo: 'Item Photo',
      // Button texts
      button_change: 'Change',
      button_remove: 'Remove',
      button_choose_photo: 'Choose Photo',
      button_take_photo: 'Take Photo',
      button_cancel: 'Cancel',
      button_add_item: 'Add Item',
      button_save_changes: 'Save Changes',
      button_ok: 'OK',
      // Page titles
      page_title_add_item: 'Add Item',
      page_title_edit_item: 'Edit Item',
      page_title_add_new_item: 'Add New Item',
      // Validation messages
      validation_item_name_required: 'Item name is required',
      validation_category_required: 'Category is required',
      validation_valid_quantity_required: 'Valid quantity is required',
      validation_quantity_must_be_positive: 'Quantity must be positive',
      validation_unit_required: 'Unit is required',
      // Success/Error messages
      success_title: 'Success',
      success_item_added_successfully: 'Item added successfully',
      success_item_updated_successfully: 'Item updated successfully',
      error_title: 'Error',
      error_failed_load_item_details: 'Failed to load item details',
      error_must_be_logged_in_to_save: 'You must be logged in to save',
      error_failed_to_add_item: 'Failed to add item',
      error_failed_to_update_item: 'Failed to update item',
      error_failed_to_pick_image: 'Failed to pick image',
      error_failed_to_take_photo: 'Failed to take photo',
      // Access control
      access_denied_title: 'Access Denied',
      access_denied_inventory_edit_message: 'Caretakers cannot edit inventory items',
      // Camera/Photo
      not_available_title: 'Not Available',
      camera_not_available_web_message: 'Camera is not available on web',
      permission_required_title: 'Permission Required',
      camera_permission_required_message: 'Camera permission is required',
      image_placeholder_text_add_photo_help_identify_item: 'Add a photo to help identify this item',
      // Help text
      form_help_text_low_stock_notification_help: 'You will be notified when stock falls below this level',
      noItems: 'No inventory items found',
      noItemsFound: 'No items match your search',
      addFirstItem: 'Add your first inventory item',
      tryDifferentSearch: 'Try a different search or filter',
      itemDeleted: 'Item deleted successfully',
      itemAdded: 'Item added successfully',
      itemUpdated: 'Item updated successfully',
      deleteConfirm: 'Are you sure you want to delete this item?',
      loadError: 'Failed to load inventory items',
      deleteError: 'Failed to delete item',
      addError: 'Failed to add item',
      updateError: 'Failed to update item',
      addItemDescription: 'Add a new item to your inventory',
      updateItemDescription: 'Update details for {name}',
      itemName: 'Item Name',
      category: 'Category',
      quantity: 'Quantity',
      unit: 'Unit',
      minimumStock: 'Min. Stock',
      location: 'Location',
      expiryDate: 'Expiry Date',
      purchaseDate: 'Purchase Date',
      description: 'Description',
      supplier: 'Supplier',
      price: 'Price',
      image: 'Image',
      addImage: 'Add Image',
      changeImage: 'Change Image',
      removeImage: 'Remove Image',
      history: 'History',
      noHistory: 'No history available',
      itemNameRequired: 'Item name is required',
      validQuantityRequired: 'Valid quantity is required',
      positiveQuantityRequired: 'Quantity must be positive',
      unitRequired: 'Unit is required',
      categoryRequired: 'Category is required',
      enterItemName: 'Enter item name',
      enterQuantity: 'Enter quantity',
      selectCategory: 'Select category',
      enterCustomCategory: 'Enter custom category',
      enterCustomUnit: 'Enter custom unit',
      selectUnit: 'Select unit',
      enterDescription: 'Enter description',
      enterSupplier: 'Enter supplier name',
      itemInformation: 'Item Information',
      quantityDetails: 'Quantity Details',
      additionalDetails: 'Additional Details',
      anItem: 'an item',
      lowStockAlert: 'Low Stock Alert',
      imagePickError: 'Failed to pick image',
      cameraError: 'Failed to use camera',
      cameraNotAvailableWeb: 'Camera is not available on web',
      cameraPermissionRequired: 'Camera permission is required',
      sharingNotAvailable: 'Sharing is not available on this platform',
      backToInventory: 'Back to Inventory',
      itemNotFound: 'Item not found',
      noActivity: 'No activity available',
      farm: 'Farm',
      created: 'Created',
      updated: 'Updated',
      transfer_in: 'Transfer In',
      transfer_out: 'Transfer Out',
      lowStock: 'Low Stock',
      expiringSoon: 'Expiring Soon',
      expired: 'Expired',
      requestTransfer: 'Request Transfer',
      seeds: 'Seeds',
      fertilizers: 'Fertilizers',
      pesticides: 'Pesticides',
      tools: 'Tools',
      equipment: 'Equipment',
      feed: 'Feed',
      medication: 'Medication',
      fuel: 'Fuel',
      other: 'Other',
      selectFarmFirst: 'Please select a farm first',
      noItemsMatchFilters: 'No items match your search or filters',
      otherCustom: 'Other (Custom)',
      customCategory: 'Custom category',
      customUnit: 'Custom unit',
      enterMinQuantity: 'Enter minimum quantity',
      selectExpiryDate: 'Select expiry date',
      selectPurchaseDate: 'Select purchase date',
      enterNotes: 'Enter notes',
      enterPrice: 'Enter price',
      fuelPrice: 'Fuel Price',
      enterFuelPrice: 'Enter fuel price',
      maintenancePrice: 'Maintenance Price',
      enterMaintenancePrice: 'Enter maintenance price',
      abbreviation: 'Abbreviation',
      categoryName: 'Category Name',
      enterCategoryName: 'Enter category name',
      categoryDescription: 'Category Description',
      enterCategoryDescription: 'Enter category description',
      unitName: 'Unit Name',
      enterUnitName: 'Enter unit name',
      enterAbbreviation: 'Enter abbreviation',
      inventoryCategories: 'Inventory Categories',
      measurementUnits: 'Measurement Units',
      addCategory: 'Add Category',
      addUnit: 'Add Unit',
      noCategoriesFound: 'No categories found',
      noUnitsFound: 'No units found',
      deleteCategory: 'Delete Category',
      deleteUnit: 'Delete Unit',
      deleteCategoryConfirm: 'Are you sure you want to delete this category?',
      deleteUnitConfirm: 'Are you sure you want to delete this unit?',
      categorySaved: 'Category saved successfully',
      unitSaved: 'Unit saved successfully',
      categoryDeleted: 'Category deleted successfully',
      unitDeleted: 'Unit deleted successfully',
      abbreviationRequired: 'Abbreviation is required',
      unnamedItem: 'Unnamed Item',
      // New inventory filter translations
      filterByCategory: 'Filter by Category',
      filterByStatus: 'Filter by Status',
      filterByExpiry: 'Filter by Expiry',
      filterByStock: 'Filter by Stock Level',
      filterBySupplier: 'Filter by Supplier',
      filterByLocation: 'Filter by Location',
      filterByDateAdded: 'Filter by Date Added',
      filterByPriceRange: 'Filter by Price Range',
      sortByName: 'Sort by Name',
      allItems: 'All Items',
      sortByQuantity: 'Sort by Quantity',
      sortByCategory: 'Sort by Category',
      sortByExpiry: 'Sort by Expiry Date',
      sortByDateAdded: 'Sort by Date Added',
      sortByPrice: 'Sort by Price',
      sortBySupplier: 'Sort by Supplier',
      sortByLocation: 'Sort by Location',
      showInStock: 'Show In Stock',
      showLowStock: 'Show Low Stock',
      showOutOfStock: 'Show Out of Stock',
      showExpired: 'Show Expired',
      showExpiringSoon: 'Show Expiring Soon',
      showAll: 'Show All Items',
      priceRange: 'Price Range',
      minPrice: 'Min Price',
      maxPrice: 'Max Price',
      stockLevel: 'Stock Level',
      inStock: 'In Stock',
      outOfStock: 'Out of Stock',
      criticalStock: 'Critical Stock',
      expiryStatus: 'Expiry Status',
      fresh: 'Fresh',
      nearExpiry: 'Near Expiry',
      expired: 'Expired',
      noExpiry: 'No Expiry Date',
      dateAdded: 'Date Added',
      lastWeek: 'Last Week',
      lastMonth: 'Last Month',
      last3Months: 'Last 3 Months',
      lastYear: 'Last Year',
      customDateRange: 'Custom Date Range',
      farmInventory: 'Farm Inventory',
      inventoryOverview: 'Inventory Overview',
      inventoryStats: 'Inventory Statistics',
      totalValue: 'Total Value',
      averagePrice: 'Average Price',
      mostExpensive: 'Most Expensive',
      leastExpensive: 'Least Expensive',
      topCategories: 'Top Categories',
      recentlyAdded: 'Recently Added',
      recentlyUpdated: 'Recently Updated',
      quickStats: 'Quick Stats',
      inventoryHealth: 'Inventory Health',
      stockAlerts: 'Stock Alerts',
      expiryAlerts: 'Expiry Alerts',
      status: {
        inStock: 'In Stock',
        lowStock: 'Low Stock',
        expired: 'Expired',
        expiringSoon: 'Expiring Soon',
      },
      categories: {
        seeds: 'Seeds',
        fertilizers: 'Fertilizers',
        pesticides: 'Pesticides',
        tools: 'Tools',
        equipment: 'Equipment',
        feed: 'Feed',
        medication: 'Medication',
        vaccination: 'Vaccination',
        fuel: 'Fuel',
        other: 'Other',
      },
    },
    requests: {
      title: 'Requests',
      createRequest: 'Create Request',
      editRequest: 'Edit Request',
      deleteRequest: 'Delete Request',
      requestDetails: 'Request Details',
      searchRequests: 'Search requests...',
      noRequests: 'No requests found',
      noRequestsFound: 'No requests match your search',
      createFirstRequest: 'Create your first request',
      tryDifferentSearch: 'Try a different search or filter',
      requestDeleted: 'Request deleted successfully',
      requestCreated: 'Request created successfully',
      requestUpdated: 'Request updated successfully',
      deleteConfirm: 'Are you sure you want to delete this request?',
      loadError: 'Failed to load requests',
      deleteError: 'Failed to delete request',
      createError: 'Failed to create request',
      updateError: 'Failed to update request',
      itemName: 'Item Name',
      quantity: 'Quantity',
      unit: 'Unit',
      reason: 'Reason',
      notes: 'Notes',
      requestedBy: 'Requested By',
      requestedAt: 'Requested At',
      approvedBy: 'Approved By',
      approvedAt: 'Approved At',
      rejectedBy: 'Rejected By',
      rejectedAt: 'Rejected At',
      rejectionReason: 'Rejection Reason',
      status: 'Status',
      pending: 'Pending',
      approved: 'Approved',
      rejected: 'Rejected',
      approve: 'Approve',
      reject: 'Reject',
      approveConfirm: 'Are you sure you want to approve this request?',
      rejectConfirm: 'Are you sure you want to reject this request?',
      approveSuccess: 'Request approved successfully',
      rejectSuccess: 'Request rejected successfully',
      approveError: 'Failed to approve request',
      rejectError: 'Failed to reject request',
      enterRejectionReason: 'Enter rejection reason',
      requestType: 'Request Type',
      inventory: 'Inventory',
      existing: 'Existing Item',
      newItem: 'New Item',
      newItemRequest: 'New Item Request',
      existingItemRequest: 'Existing Item Request',
      selectItem: 'Select Item',
      noItemsAvailable: 'No items available',
      caretakerRequests: 'Caretaker Requests',
      inventoryRequests: 'Inventory Requests',
      noCaretakerRequests: 'No caretaker requests',
      noInventoryRequests: 'No inventory requests',
      createFirstInventoryRequest: 'Create your first inventory request',
      waitingForCaretakerRequests: 'Waiting for caretaker requests',
      requestNewInventoryItem: 'Request New Inventory Item',
      submitRequestDescription: 'Submit a request for a new inventory item',
      reasonForRequest: 'Reason for Request',
      explainWhyNeeded: 'Explain why this item is needed',
      requestFor: 'Request for',
      submitRequest: 'Submit Request',
      fillAllFields: 'Please fill all required fields',
      invalidQuantity: 'Please enter a valid quantity',
      enterReason: 'Enter reason for request',
      requestNote: 'Your request will be sent to the admin for approval',
      manualItemEntry: 'Enter item details manually',
      transferRequested: 'Transfer Requested',
      transferApproved: 'Transfer Approved',
      transferRejected: 'Transfer Rejected',
      transferReceived: 'Transfer Received',
      machinery: 'Machinery',
      machineryName: 'Machinery Name',
      machineryType: 'Machinery Type',
      machineryRequests: 'Machinery Requests',
      noMachineryRequests: 'No machinery requests',
      createFirstMachineryRequest: 'Create your first machinery request',
      requestMachinery: 'Request Machinery',
      machineryRequestSubmitted: 'Machinery request submitted successfully',
      failedToSubmitMachineryRequest: 'Failed to submit machinery request',
      requestMachineryUse: 'Request Machinery Use',
      requestMachineryFromCard: 'Request this machinery',
      reasonRequired: 'Reason is required',
      reasonForTransfer: 'Reason for Transfer',
      explainWhyTransferNeeded: 'Explain why this transfer is needed',
      submitTransferRequest: 'Submit Transfer Request',
      submitTransferRequestDescription: 'Submit a request to transfer this item',
      transferRequestCreated: 'Transfer request created successfully',
      transferRequestNote: 'Your transfer request will be reviewed by the admin',
      requestTransfer: 'Request Transfer',
      machineryRequestNote: 'Your machinery request will be reviewed by the appropriate authority',
      submitMachineryRequestDescription: 'Submit a request for machinery usage or maintenance',
      ownersCannotCreateRequests: 'Owners cannot create requests',
      ownersCanOnlyApproveReject: 'As an owner, you can only approve or reject requests from admins',
      machineryRequest: 'Machinery Request',
      machineryRequestDetails: 'Machinery Request Details',
      approvalInformation: 'Approval Information',
      rejectionInformation: 'Rejection Information',
      reasonForRejection: 'Reason for Rejection',
      confirmRejection: 'Confirm Rejection',
      requestNotFound: 'Request not found',
    },
    farm: {
      title: 'Farms',
      assignedFarms: 'Assigned Farms',
      addFarm: 'Add Farm',
      editFarm: 'Edit Farm',
      deleteFarm: 'Delete Farm',
      farmDetails: 'Farm Details',
      searchFarms: 'Search farms...',
      noFarms: 'No farms found',
      noFarmsFound: 'No farms match your search',
      addFirstFarm: 'Add your first farm',
      tryDifferentSearch: 'Try a different search or filter',
      farmDeleted: 'Farm deleted successfully',
      farmAdded: 'Farm added successfully',
      farmUpdated: 'Farm updated successfully',
      deleteConfirm: 'Are you sure you want to delete this farm?',
      loadError: 'Failed to load farms',
      deleteError: 'Failed to delete farm',
      addError: 'Failed to add farm',
      updateError: 'Failed to update farm',
      farmName: 'Farm Name',
      location: 'Location',
      locationNotSet: 'Location not set',
      size: 'Size',
      owner: 'Owner',
      admins: 'Admins',
      caretakers: 'Caretakers',
      inventory: 'Inventory',
      requests: 'Requests',
      notes: 'Notes',
      reports: 'Reports',
      activities: 'Activities',
      settings: 'Settings',
      noFarmSelected: 'No farm selected',
      selectFarmFirst: 'Please select a farm first',
      selectFarm: 'Select Farm',
      changeFarm: 'Change Farm',
      // New farm setup translations
      createFarm: 'Create Farm',
      farmSetup: 'Farm Setup',
      setupYourFarm: 'Setup Your Farm',
      farmCreatedSuccessfully: 'Farm created successfully',
      redirectingToDashboard: 'Redirecting to dashboard...',
      enterFarmName: 'Enter farm name',
      enterFarmLocation: 'Enter farm location',
      enterFarmSize: 'Enter farm size (in acres)',
      farmNameRequired: 'Farm name is required',
      farmLocationRequired: 'Farm location is required',
      farmSizeRequired: 'Farm size is required',
      invalidFarmSize: 'Please enter a valid farm size',
      farmInformation: 'Farm Information',
      basicDetails: 'Basic Details',
      farmDescription: 'Farm Description',
      enterFarmDescription: 'Enter farm description (optional)',
      farmType: 'Farm Type',
      selectFarmType: 'Select farm type',
      cropFarm: 'Crop Farm',
      livestockFarm: 'Livestock Farm',
      mixedFarm: 'Mixed Farm',
      dairyFarm: 'Dairy Farm',
      poultyFarm: 'Poultry Farm',
      fishFarm: 'Fish Farm',
      organicFarm: 'Organic Farm',
      acres: 'Acres',
      hectares: 'Hectares',
      squareFeet: 'Square Feet',
      squareMeters: 'Square Meters',
      sizeUnit: 'Size Unit',
      selectSizeUnit: 'Select size unit',
      establishedYear: 'Established Year',
      selectEstablishedYear: 'Select established year',
      farmImage: 'Farm Image',
      addFarmImage: 'Add farm image (optional)',
      farmContact: 'Farm Contact',
      farmPhone: 'Farm Phone',
      farmEmail: 'Farm Email',
      enterFarmPhone: 'Enter farm phone (optional)',
      enterFarmEmail: 'Enter farm email (optional)',
      farmAddress: 'Farm Address',
      enterFarmAddress: 'Enter complete farm address',
      farmAddressRequired: 'Farm address is required',
      coordinates: 'Coordinates',
      latitude: 'Latitude',
      longitude: 'Longitude',
      enterLatitude: 'Enter latitude (optional)',
      enterLongitude: 'Enter longitude (optional)',
      useCurrentLocation: 'Use Current Location',
      gettingLocation: 'Getting current location...',
      locationPermissionDenied: 'Location permission denied',
      failedToGetLocation: 'Failed to get current location',
      farmCreation: 'Farm Creation',
      creatingFarm: 'Creating farm...',
      farmCreationFailed: 'Failed to create farm',
      myFarms: 'My Farms',
      assignedFarms: 'Assigned Farms',
      ownedFarms: 'Owned Farms',
      managedFarms: 'Managed Farms',
      totalFarms: 'Total Farms',
      activeFarms: 'Active Farms',
      farmOverview: 'Farm Overview',
      farmStats: 'Farm Statistics',
      farmActivities: 'Farm Activities',
      recentFarmActivity: 'Recent Farm Activity',
      farmInventoryCount: 'Farm Inventory Count',
      farmMachineryCount: 'Farm Machinery Count',
      farmUsersCount: 'Farm Users Count',
      farmRequestsCount: 'Farm Requests Count',
      farmNotesCount: 'Farm Notes Count',
      basicInformation: 'Basic Information',
      loadingFarmDetails: 'Loading farm details...',
      assignCaretakers: 'Assign Caretakers',
      noCaretakersAvailable: 'No caretakers available. Create caretaker accounts first.',
      updateFarm: 'Update Farm',
    },
    users: {
      title: 'Users',
      addUser: 'Add User',
      editUser: 'Edit User',
      deleteUser: 'Delete User',
      userDetails: 'User Details',
      searchUsers: 'Search users...',
      noUsers: 'No users found',
      noUsersFound: 'No users match your search',
      addFirstUser: 'Add your first user',
      tryDifferentSearch: 'Try a different search or filter',
      userDeleted: 'User deleted successfully',
      userAdded: 'User added successfully',
      userUpdated: 'User updated successfully',
      deleteConfirm: 'Are you sure you want to delete this user?',
      loadError: 'Failed to load users',
      deleteError: 'Failed to delete user',
      addError: 'Failed to add user',
      updateError: 'Failed to update user',
      userName: 'User Name',
      email: 'Email',
      phone: 'Phone',
      role: 'Role',
      status: 'Status',
      owner: 'Owner',
      admin: 'Admin',
      caretaker: 'Caretaker',
      active: 'Active',
      inactive: 'Inactive',
      suspended: 'Suspended',
      lastLogin: 'Last Login',
      createdAt: 'Created At',
      updatedAt: 'Updated At',
      assignedFarms: 'Assigned Farms',
      noAssignedFarms: 'No assigned farms',
      assignFarm: 'Assign Farm',
      unassignFarm: 'Unassign Farm',
      assignFarmSuccess: 'Farm assigned successfully',
      unassignFarmSuccess: 'Farm unassigned successfully',
      assignFarmError: 'Failed to assign farm',
      unassignFarmError: 'Failed to unassign farm',
      adminCanOnlyAddCaretakers: 'As an admin, you can only add caretakers',
      allUsers: 'All Users',
      farmUsers: 'Farm Users',
      noUsersFoundForFarm: 'No users found for this farm',
      adminViewOnly: 'You are viewing this farm as an admin. You cannot edit or delete farm information.',
      caretakerViewOnly: 'You are viewing this farm as a caretaker. You cannot edit or delete farm information.',
      noUsers: 'No users assigned to this farm',
      deleteUserConfirmation: 'Are you sure you want to delete {userName}? This action cannot be undone.',
      // Add User form specific translations
      fullName: 'Full Name',
      enterFullName: 'Enter full name',
      emailAddress: 'Email Address',
      enterEmailAddress: 'Enter email address',
      gender: 'Gender',
      male: 'Male',
      female: 'Female',
      other: 'Other',
      dateOfBirth: 'Date of Birth',
      dateFormat: 'DD/MM/YYYY',
      cnic: 'CNIC',
      cnicFormat: 'XXXXX-XXXXXXX-X',
      contactInformation: 'Contact Information',
      phoneNumber: 'Phone Number',
      enterPhoneNumber: 'Enter phone number',
      address: 'Address',
      enterCompleteAddress: 'Enter complete address',
      profileImage: 'Profile Image',
      uploadImage: 'Upload Image',
      takePhoto: 'Take Photo',
      removeImage: 'Remove',
      bio: 'Bio',
      enterBio: 'Enter bio (optional)',
      security: 'Security',
      password: 'Password',
      enterPassword: 'Enter password (min. 6 characters)',
      confirmPassword: 'Confirm Password',
      confirmPasswordPlaceholder: 'Confirm password',
      farmAssignment: 'Farm Assignment',
      assignToFarms: 'Assign to Farms',
      noFarmsAvailable: 'No farms available. Please contact your administrator.',
      createUser: 'Create User',
      capturePhoto: 'Capture Photo',
      flipCamera: 'Flip Camera',
      capture: 'Capture',
      // Validation messages
      nameRequired: 'Name is required',
      emailRequired: 'Email is required',
      emailInvalid: 'Email is invalid',
      passwordRequired: 'Password is required',
      passwordTooShort: 'Password must be at least 6 characters',
      confirmPasswordRequired: 'Please confirm your password',
      passwordsDoNotMatch: 'Passwords do not match',
      farmSelectionRequired: 'At least one farm must be selected',
      genderRequired: 'Gender is required',
      dateOfBirthRequired: 'Date of birth is required',
      dateFormatInvalid: 'Date must be in DD/MM/YYYY format',
      cnicRequired: 'CNIC is required',
      cnicFormatInvalid: 'CNIC must be in XXXXX-XXXXXXX-X format',
      userCreatedSuccessfully: 'User created successfully',
      failedToCreateUser: 'Failed to create user. Please try again.',
      permissionDeniedCamera: 'Permission denied. Sorry, we need camera roll permissions to make this work!',
      permissionDeniedCameraCapture: 'Permission denied. Sorry, we need camera permissions to make this work!',
    },
    notes: {
      title: 'Notes',
      addNote: 'Add Note',
      editNote: 'Edit Note',
      deleteNote: 'Delete Note',
      noteDetails: 'Note Details',
      searchNotes: 'Search notes...',
      noNotes: 'No notes found',
      noNotesFound: 'No notes match your search',
      addFirstNote: 'Add your first note',
      tryDifferentSearch: 'Try a different search or filter',
      noteDeleted: 'Note deleted successfully',
      noteAdded: 'Note added successfully',
      noteUpdated: 'Note updated successfully',
      deleteConfirm: 'Are you sure you want to delete this note?',
      loadError: 'Failed to load notes',
      deleteError: 'Failed to delete note',
      addError: 'Failed to add note',
      updateError: 'Failed to update note',
      noteTitle: 'Note Title',
      content: 'Content',
      createdBy: 'Created By',
      createdAt: 'Created At',
      updatedAt: 'Updated At',
      category: 'Category',
      priority: 'Priority',
      status: 'Status',
      farm: 'Farm',
      assignedTo: 'Assigned To',
      dueDate: 'Due Date',
      completedAt: 'Completed At',
      completedBy: 'Completed By',
      attachments: 'Attachments',
      addAttachment: 'Add Attachment',
      removeAttachment: 'Remove Attachment',
      voiceNote: 'Voice Note',
      recordVoiceNote: 'Record Voice Note',
      stopRecording: 'Stop Recording',
      titleRequired: 'Title is required',
      contentRequired: 'Content is required',
      missingData: 'Missing required data',
      idRequired: 'Note ID is required',
      notFound: 'Note not found',
      cannotEditOthers: 'You cannot edit notes created by others',
      imagePickError: 'Failed to pick image',
      cameraError: 'Failed to use camera',
      cameraNotAvailableWeb: 'Camera is not available on web',
      cameraPermissionRequired: 'Camera permission is required',
      audioRecordingNotAvailableWeb: 'Audio recording is not available on web',
      microphonePermissionRequired: 'Microphone permission is required',
      recordingStartError: 'Failed to start recording',
      recordingStopError: 'Failed to stop recording',
      audioPlayError: 'Failed to play audio',
      enterNoteTitle: 'Enter note title',
      enterNoteContent: 'Enter note content',
      updateNoteDescription: 'Update your note details',
      noFarmSelected: 'No farm selected',
      selectFarmFirst: 'Please select a farm first',
      categories: {
        general: 'General',
        task: 'Task',
        reminder: 'Reminder',
        meeting: 'Meeting',
        other: 'Other',
      },
      priorities: {
        low: 'Low',
        medium: 'Medium',
        high: 'High',
        urgent: 'Urgent',
      },
      statuses: {
        active: 'Active',
        completed: 'Completed',
        archived: 'Archived',
      },
      noteAdded: 'Note Added',
      noteUpdated: 'Note Updated',
      filters: {
        all: 'All',
        withAudio: 'With Audio',
        withImages: 'With Images',
        textOnly: 'Text Only',
        multimedia: 'Multimedia',
        audio: 'Audio',
        images: 'Images',
        text: 'Text',
      },
    },
    reports: {
      title: 'Reports',
      generateReport: 'Generate Report',
      viewReport: 'View Report',
      deleteReport: 'Delete Report',
      reportDetails: 'Report Details',
      searchReports: 'Search reports...',
      noReports: 'No reports found',
      noReportsFound: 'No reports match your search',
      generateFirstReport: 'Generate your first report',
      tryDifferentSearch: 'Try a different search or filter',
      reportDeleted: 'Report deleted successfully',
      reportGenerated: 'Report generated successfully',
      deleteConfirm: 'Are you sure you want to delete this report?',
      loadError: 'Failed to load reports',
      deleteError: 'Failed to delete report',
      generateError: 'Failed to generate report',
      reportType: 'Report Type',
      dateRange: 'Date Range',
      startDate: 'Start Date',
      endDate: 'End Date',
      farm: 'Farm',
      generatedBy: 'Generated By',
      generatedAt: 'Generated At',
      downloadReport: 'Download Report',
      printReport: 'Print Report',
      shareReport: 'Share Report',
      scheduleReport: 'Schedule Report',
      frequency: 'Frequency',
      recipients: 'Recipients',
      addRecipient: 'Add Recipient',
      removeRecipient: 'Remove Recipient',
      scheduledReports: 'Scheduled Reports',
      noScheduledReports: 'No scheduled reports',
      scheduleFirstReport: 'Schedule your first report',
      reportScheduled: 'Report scheduled successfully',
      reportUnscheduled: 'Report unscheduled successfully',
      scheduleError: 'Failed to schedule report',
      unscheduleError: 'Failed to unschedule report',
      types: {
        inventory: 'Inventory Report',
        requests: 'Requests Report',
        activities: 'Activities Report',
        users: 'Users Report',
        farms: 'Farms Report',
        notes: 'Notes Report',
        custom: 'Custom Report',
      },
      frequencies: {
        daily: 'Daily',
        weekly: 'Weekly',
        monthly: 'Monthly',
        quarterly: 'Quarterly',
        yearly: 'Yearly',
      },
    },
    activities: {
      title: 'Activities',
      viewActivity: 'View Activity',
      searchActivities: 'Search activities...',
      noActivities: 'No activities found',
      noActivitiesFound: 'No activities match your search',
      tryDifferentSearch: 'Try a different search or filter',
      loadError: 'Failed to load activities',
      activityType: 'Activity Type',
      user: 'User',
      farm: 'Farm',
      date: 'Date',
      time: 'Time',
      details: 'Details',
      item: 'Item',
      quantity: 'Quantity',
      unit: 'Unit',
      types: {
        login: 'Login',
        logout: 'Logout',
        inventory_created: 'Inventory Created',
        inventory_updated: 'Inventory Updated',
        inventory_deleted: 'Inventory Deleted',
        request_created: 'Request Created',
        request_updated: 'Request Updated',
        request_deleted: 'Request Deleted',
        request_approved: 'Request Approved',
        request_rejected: 'Request Rejected',
        note_created: 'Note Created',
        note_updated: 'Note Updated',
        note_deleted: 'Note Deleted',
        user_created: 'User Created',
        user_updated: 'User Updated',
        user_deleted: 'User Deleted',
        farm_created: 'Farm Created',
        farm_updated: 'Farm Updated',
        farm_deleted: 'Farm Deleted',
        report_generated: 'Report Generated',
        report_deleted: 'Report Deleted',
        report_scheduled: 'Report Scheduled',
        report_unscheduled: 'Report Unscheduled',
        transfer_approved: 'Transfer Approved',
        transfer_rejected: 'Transfer Rejected',
        transfer_received: 'Transfer Received',
      },
    },
    settings: {
      title: 'Settings',
      account: 'Account',
      profile: 'Profile',
      security: 'Security',
      notifications: 'Notifications',
      language: 'Language',
      theme: 'Theme',
      about: 'About',
      help: 'Help',
      logout: 'Logout',
      accountSettings: 'Account Settings',
      profileSettings: 'Profile Settings',
      securitySettings: 'Security Settings',
      notificationSettings: 'Notification Settings',
      languageSettings: 'Language Settings',
      themeSettings: 'Theme Settings',
      aboutApp: 'About App',
      helpCenter: 'Help Center',
      logoutConfirm: 'Are you sure you want to logout?',
      name: 'Name',
      email: 'Email',
      phone: 'Phone',
      role: 'Role',
      changePassword: 'Change Password',
      currentPassword: 'Current Password',
      newPassword: 'New Password',
      confirmNewPassword: 'Confirm New Password',
      passwordChanged: 'Password changed successfully',
      passwordChangeError: 'Failed to change password',
      profileUpdated: 'Profile updated successfully',
      profileUpdateError: 'Failed to update profile',
      profileDetails: 'Profile Details',
      personalInformation: 'Personal Information',
      viewDetails: 'View Details',
      enableNotifications: 'Enable Notifications',
      emailNotifications: 'Email Notifications',
      pushNotifications: 'Push Notifications',
      smsNotifications: 'SMS Notifications',
      notificationUpdated: 'Notification settings updated',
      notificationUpdateError: 'Failed to update notification settings',
      selectLanguage: 'Select Language',
      english: 'English',
      urdu: 'Urdu',
      languageChanged: 'Language changed successfully',
      selectTheme: 'Select Theme',
      light: 'Light',
      dark: 'Dark',
      system: 'System',
      themeChanged: 'Theme changed successfully',
      version: 'Version',
      buildNumber: 'Build Number',
      developer: 'Developer',
      copyright: 'Copyright',
      termsOfService: 'Terms of Service',
      privacyPolicy: 'Privacy Policy',
      contactUs: 'Contact Us',
      faq: 'FAQ',
      tutorials: 'Tutorials',
      supportEmail: 'Support Email',
      supportPhone: 'Support Phone',
      website: 'Website',
    },
    machinery: {
      title: 'Machinery',
      addMachinery: 'Add Machinery',
      editMachinery: 'Edit Machinery',
      deleteMachinery: 'Delete Machinery',
      machineryDetails: 'Machinery Details',
      details: 'Details',
      searchMachinery: 'Search machinery...',
      noMachinery: 'No machinery found',
      noMachineryFound: 'No machinery match your search',
      addFirstMachinery: 'Add your first machinery',
      tryDifferentSearch: 'Try a different search or filter',
      machineryDeleted: 'Machinery deleted successfully',
      machineryAdded: 'Machinery added successfully',
      machineryUpdated: 'Machinery updated successfully',
      deleteConfirm: 'Are you sure you want to delete this machinery?',
      deleteConfirmTitle: 'Delete Machinery',
      deleteConfirmMessage: 'Are you sure you want to delete this machinery? This action cannot be undone.',
      deleteSuccess: 'Machinery deleted successfully',
      loadError: 'Failed to load machinery',
      loadingDetails: 'Loading machinery details...',
      deleteError: 'Failed to delete machinery',
      addError: 'Failed to add machinery',
      updateError: 'Failed to update machinery',
      notFound: 'Machinery not found',
      noIdProvided: 'No machinery ID provided',
      machineryName: 'Machinery Name',
      machineryType: 'Machinery Type',
      model: 'Model',
      year: 'Year',
      status: 'Status',
      location: 'Location',
      currentLocation: 'Current Location',
      fuelLevel: 'Fuel Level',
      fuelType: 'Fuel Type',
      maintenanceDate: 'Maintenance Date',
      working: 'Working',
      maintenanceStatus: 'Maintenance',
      malfunction: 'Malfunction',
      inUse: 'In Use',
      requestUse: 'Request Use',
      overview: 'Overview',
      requests: 'Requests',
      maintenanceSection: 'Maintenance',
      fuel: 'Fuel',
      nextMaintenance: 'Next Maintenance',
      odometer: 'Odometer',
      hours: 'hours',
      currentFuelLevel: 'Current Fuel Level',
      fuelCapacity: 'Fuel Capacity',
      nextMaintenanceDate: 'Next Maintenance Date',
      lastMaintenanceDate: 'Last Maintenance Date',
      odometerReading: 'Odometer Reading',
      inUseOtherFarm: 'In Use Other Farm',
      totalMachinery: 'Total Machinery',
      available: 'Available',
      requestMachineryFromAdmin: 'Request Machinery from Admin',
      requestMachineryFromOwner: 'Request Machinery from Owner',
      loadingMachinery: 'Loading machinery...',
      farmMachinery: 'Farm Machinery',
      addMachineryDescription: 'Add a new machinery to your farm',
      enterMachineryName: 'Enter machinery name',
      selectType: 'Select type',
      enterModel: 'Enter model',
      enterYear: 'Enter year',
      priceRs: 'Price (Rs)',
      enterPrice: 'Enter price in Rs',
      loadingDetails: 'Loading machinery details...',
      serialNumber: 'Serial Number',
      registrationNumber: 'Registration Number',
      enterSerialNumber: 'Enter serial number',
      enterRegistrationNumber: 'Enter registration number',
      selectStatus: 'Select status',
      selectFuelType: 'Select fuel type',
      maintenanceIntervalHours: 'Maintenance Interval',
      enterNotes: 'Enter notes',
      basicInformation: 'Basic Information',
      fuelInformation: 'Fuel Information',
      maintenanceInformation: 'Maintenance Information',
      requestFromCard: 'Request Use',
      selectMachinery: 'Select Machinery',
      requestType: 'Request Type',
      requestUse: 'Request for Use',
      requestFuel: 'Request for Fuel',
      requestMaintenance: 'Request for Maintenance',
      requestTransfer: 'Request for Transfer',
      requestNewItem: 'New Machinery',
      reasonPlaceholder: 'Please explain why you need this machinery...',
      scheduleAndSpecifics: 'Schedule & Specifics',
      destinationFarm: 'Destination Farm',
      estimatedHours: 'Estimated Hours',
      hoursPlaceholder: 'e.g., 40',
      fuelAmount: 'Fuel Amount',
      fuelAmountPlaceholder: 'e.g., 100',
      litterPrice: 'Litter Price',
      enterLitterPrice: 'Enter price per liter',
      validLitterPriceRequired: 'Please enter a valid litter price',
      totalPrice: 'Total Price',
      totalPriceCalculated: 'Automatically calculated (Fuel Amount × Litter Price)',
      maintenanceType: 'Maintenance Type',
      maintenanceTypePlaceholder: 'e.g., Routine maintenance, Oil change',
      noMachineryAvailable: 'No machinery available',
      noOtherFarmsAvailable: 'No other farms available',
      startDateRequired: 'Start date is required',
      selectUnit: 'Select Unit',
      basicInformation: 'Basic Information',
      maintenanceInformation: 'Maintenance Information',
      machineryName: 'Machinery Name',
      enterMachineryName: 'Enter machinery name',
      machineryType: 'Machinery Type',
      selectType: 'Select Type',
      enterNotes: 'Enter notes',
      updateMachinery: 'Update Machinery',
      selectStatus: 'Select Status',
      selectFuelType: 'Select Fuel Type',
      endDateRequired: 'End date is required',
      dateFormatInvalid: 'Date format is invalid',
      destinationFarmRequired: 'Destination farm is required',
      endDateBeforeStartDate: 'End date cannot be before start date',
      validFuelAmountRequired: 'Valid fuel amount is required',
      fuelAmountRequired: 'Fuel amount is required',
      litterPriceRequired: 'Litter price is required',
      maintenancePriceRequired: 'Maintenance price is required',
      maintenanceTypeRequired: 'Maintenance type is required',
      validHoursRequired: 'Valid hours required',
      requestSubmitted: 'Request submitted successfully',
      requestMachinery: 'Request Machinery',
      every: 'Every',
      newMachineryRequest: 'New Machinery Request',
      newMachineryItem: 'New Machinery Item',
      updateMachinery: 'Update Machinery',
      validFuelCapacityRequired: 'Valid fuel capacity is required',
      validCurrentFuelRequired: 'Valid current fuel level is required',
      validOdometerRequired: 'Valid odometer reading is required',
      validMaintenanceIntervalRequired: 'Valid maintenance interval is required',
      // Machinery types
      tractor: 'Tractor',
      harvester: 'Harvester',
      planter: 'Planter',
      sprayer: 'Sprayer',
      cultivator: 'Cultivator',
      car: 'Car',
      jeep: 'Jeep',
      motorbike: 'Motorbike',
      bicycle: 'Bicycle',
      other_custom: 'Other (Custom)',
      // Fuel types
      diesel: 'Diesel',
      gasoline: 'Gasoline',
      petrol: 'Petrol',
      electric: 'Electric',
      hybrid: 'Hybrid',
      // Status types
      maintenance: 'Under Maintenance',
      // Custom input placeholders
      enterCustomType: 'Enter custom machinery type',
      enterCustomFuelType: 'Enter custom fuel type',
      customTypeRequired: 'Custom type is required',
      types: {
        tractor: 'Tractor',
        harvester: 'Harvester',
        planter: 'Planter',
        sprayer: 'Sprayer',
        cultivator: 'Cultivator',
        other: 'Other',
      },
    },
    expenses: {
      title: 'Expenses',
      farmExpenses: 'Farm Expenses',
      userExpenses: 'User Expenses',
      addExpense: 'Add Expense',
      editExpense: 'Edit Expense',
      expenseDetails: 'Expense Details',
      expenseType: 'Expense Type',
      farmExpense: 'Farm Expense',
      userExpense: 'User Expense',
      amount: 'Amount',
      description: 'Description',
      category: 'Category',
      date: 'Date',
      user: 'User',
      totalExpenses: 'Total Expenses',
      monthlyExpenses: 'Monthly Expenses',
      yearlyExpenses: 'Yearly Expenses',
      recentExpenses: 'Recent Expenses',
      allExpenses: 'All Expenses',
      viewAll: 'View All',
      noExpenses: 'No expenses found',
      noMatchingExpenses: 'No matching expenses found',
      noExpensesFound: 'No expenses found',
      tryDifferentFilters: 'Try adjusting your filters or search terms',
      loadError: 'Failed to load expenses',
      loadingExpenses: 'Loading expenses...',
      allTypes: 'All Types',
      allCategories: 'All Categories',
      searchExpenses: 'Search expenses...',
      expensesFound: 'expenses found',
      viewRequest: 'View Request',
      expenseSummary: 'Expense Summary',
      expensesShowing: 'expenses showing',
      totalAmount: 'Total Amount',
      avgAmount: 'Avg Amount',
      totalCount: 'Count',
      totalValue: 'Total Value',
      advancedFilter: 'Advanced Filter',
      clearFilters: 'Clear Filters',
      applyFilters: 'Apply Filters',
      dateRange: 'Date Range',
      fromDate: 'From Date',
      toDate: 'To Date',
      selectDate: 'Select Date',
      amountRange: 'Amount Range',
      minAmount: 'Min Amount',
      maxAmount: 'Max Amount',
      endOfList: 'End of list',
      inventoryMachineryTotal: 'Total from inventory & machinery purchases',
      allUserExpensesTotal: 'Total of all user expenses',
      userExpensesThisMonth: 'User expenses this month',
      expenseCreated: 'Expense created successfully',
      expenseUpdated: 'Expense updated successfully',
      expenseDeleted: 'Expense deleted successfully',
      adjustment: 'Adjustment',
      returnAdjustment: 'Return Adjustment',
      tracking: 'Tracking',
      deleteExpenseConfirm: 'Are you sure you want to delete this expense?',
      enterAmount: 'Enter amount',
      enterDescription: 'Enter description',
      selectCategory: 'Select category',
      selectUser: 'Select user',
      selectDate: 'Select date',
      amountRequired: 'Amount is required',
      descriptionRequired: 'Description is required',
      categoryRequired: 'Category is required',
      userRequired: 'User is required',
      dateRequired: 'Date is required',
      invalidAmount: 'Please enter a valid amount',
      categories: {
        inventory: 'Inventory',
        machinery: 'Machinery',
        fuel: 'Fuel',
        maintenance: 'Maintenance',
        labor: 'Labor',
        utilities: 'Utilities',
        transport: 'Transport',
        other: 'Other',
      },
      filterByType: 'Filter by Type',
      filterByCategory: 'Filter by Category',
      filterByUser: 'Filter by User',
      filterByDate: 'Filter by Date',
      allTypes: 'All Types',
      allCategories: 'All Categories',
      allUsers: 'All Users',
      thisMonth: 'This Month',
      lastMonth: 'Last Month',
      thisYear: 'This Year',
      customRange: 'Custom Range',
      from: 'From',
      to: 'To',
      apply: 'Apply',
      reset: 'Reset',
      exportExpenses: 'Export Expenses',
      expenseReport: 'Expense Report',
      totalFarmExpenses: 'Total Farm Expenses',
      totalUserExpenses: 'Total User Expenses',
      expenseBreakdown: 'Expense Breakdown',
      topCategories: 'Top Categories',
      topUsers: 'Top Users',
      recentExpenses: 'Recent Expenses',
      viewAll: 'View All',
      searchExpenses: 'Search expenses...',
      sortBy: 'Sort By',
      sortByDate: 'Sort by Date',
      sortByAmount: 'Sort by Amount',
      sortByCategory: 'Sort by Category',
      ascending: 'Ascending',
      descending: 'Descending',
    },
    allocations: {
      title: 'User Allocations',
      userAllocations: 'User Allocations',
      allocationDetails: 'Allocation Details',
      allocatedItems: 'Allocated Items',
      allocatedMachinery: 'Allocated Machinery',
      allocatedTo: 'Allocated To',
      allocatedBy: 'Allocated By',
      allocationDate: 'Allocation Date',
      returnDate: 'Return Date',
      expectedReturn: 'Expected Return',
      actualReturn: 'Actual Return',
      status: 'Status',
      quantity: 'Quantity',
      condition: 'Condition',
      notes: 'Notes',
      returnItem: 'Return Item',
      returnMachinery: 'Return Machinery',
      markAsReturned: 'Mark as Returned',
      returnConfirm: 'Confirm Return',
      returnSuccess: 'Item returned successfully',
      returnError: 'Failed to return item',
      noAllocations: 'No allocations found',
      searchAllocations: 'Search allocations...',
      filterByUser: 'Filter by User',
      filterByType: 'Filter by Type',
      filterByStatus: 'Filter by Status',
      allUsers: 'All Users',
      allTypes: 'All Types',
      allStatuses: 'All Statuses',
      inventory: 'Inventory',
      machinery: 'Machinery',
      allocated: 'Allocated',
      returned: 'Returned',
      overdue: 'Overdue',
      damaged: 'Damaged',
      lost: 'Lost',
      good: 'Good',
      fair: 'Fair',
      poor: 'Poor',
      excellent: 'Excellent',
      returnCondition: 'Return Condition',
      returnNotes: 'Return Notes',
      enterReturnNotes: 'Enter return notes...',
      selectCondition: 'Select condition',
      conditionRequired: 'Condition is required',
      returnNotesRequired: 'Return notes are required',
      allocationHistory: 'Allocation History',
      totalAllocations: 'Total Allocations',
      activeAllocations: 'Active Allocations',
      returnedAllocations: 'Returned Allocations',
      overdueAllocations: 'Overdue Allocations',
      allocationReport: 'Allocation Report',
      exportAllocations: 'Export Allocations',
      viewAllocationHistory: 'View Allocation History',
      allocationCreated: 'Allocation created successfully',
      allocationUpdated: 'Allocation updated successfully',
      allocationDeleted: 'Allocation deleted successfully',
      deleteAllocationConfirm: 'Are you sure you want to delete this allocation?',
      itemName: 'Item Name',
      machineryName: 'Machinery Name',
      allocationType: 'Allocation Type',
      daysAllocated: 'Days Allocated',
      costPerDay: 'Cost per Day',
      totalCost: 'Total Cost',
      calculateCost: 'Calculate Cost',
      costCalculation: 'Cost Calculation',
      dailyRate: 'Daily Rate',
      weeklyRate: 'Weekly Rate',
      monthlyRate: 'Monthly Rate',
      customRate: 'Custom Rate',
      rateType: 'Rate Type',
      rate: 'Rate',
      enterRate: 'Enter rate',
      rateRequired: 'Rate is required',
      invalidRate: 'Please enter a valid rate',
      myAllocations: 'My Allocations',
      allAllocations: 'All Allocations',
      userAllocationSummary: 'User Allocation Summary',
      topAllocatedUsers: 'Top Allocated Users',
      mostAllocatedItems: 'Most Allocated Items',
      allocationTrends: 'Allocation Trends',
      returnTrends: 'Return Trends',
      lateReturns: 'Late Returns',
      onTimeReturns: 'On Time Returns',
      earlyReturns: 'Early Returns',
      averageAllocationDuration: 'Average Allocation Duration',
      allocationEfficiency: 'Allocation Efficiency',
      returnRate: 'Return Rate',
      damageRate: 'Damage Rate',
      lossRate: 'Loss Rate',
      // Allocation Requests
      requestAllocation: 'Request Allocation',
      allocationRequests: 'Allocation Requests',
      returnRequests: 'Return Requests',
      allocations: 'allocations',
      active: 'Active',
      noAllocationsForUser: 'No allocations for this user',
      allocated: 'Allocated',
      returned: 'Returned',
      remaining: 'Remaining',
      return: 'Return',
      partially_returned: 'Partially Returned',
      requestedQuantity: 'Requested Quantity',
      returnQuantity: 'Return Quantity',
      selectQuantity: 'Select Quantity',
      quantityRequired: 'Quantity is required',
      invalidQuantity: 'Please enter a valid quantity',
      maxQuantity: 'Maximum quantity',
      reasonForRequest: 'Reason for Request',
      reasonForReturn: 'Reason for Return',
      enterReason: 'Enter reason...',
      reasonRequired: 'Reason is required',
      urgencyLevel: 'Urgency Level',
      selectUrgency: 'Select urgency',
      urgencyRequired: 'Urgency is required',
      expectedReturnDate: 'Expected Return Date',
      selectReturnDate: 'Select return date',
      returnCondition: 'Return Condition',
      selectCondition: 'Select condition',
      conditionRequired: 'Condition is required',
      returnNotes: 'Return Notes',
      enterReturnNotes: 'Enter return notes...',
      allocationRequestCreated: 'Allocation request created successfully',
      returnRequestCreated: 'Return request created successfully',
      allocationRequestApproved: 'Allocation request approved',
      allocationRequestRejected: 'Allocation request rejected',
      returnRequestApproved: 'Return request approved',
      returnRequestRejected: 'Return request rejected',
      approveRequest: 'Approve Request',
      rejectRequest: 'Reject Request',
      rejectionReason: 'Rejection Reason',
      enterRejectionReason: 'Enter rejection reason...',
      rejectionReasonRequired: 'Rejection reason is required',
      pendingRequests: 'Pending Requests',
      approvedRequests: 'Approved Requests',
      rejectedRequests: 'Rejected Requests',
      requestDetails: 'Request Details',
      requestedBy: 'Requested By',
      requestedTo: 'Requested To',
      requestedOn: 'Requested On',
      approvedBy: 'Approved By',
      approvedOn: 'Approved On',
      rejectedBy: 'Rejected By',
      rejectedOn: 'Rejected On',
      viewAllocationDetails: 'View Allocation Details',
      allocationCreated: 'Allocation Created',
      expenseAdjusted: 'Expense Adjusted',
      costDeducted: 'Cost Deducted',
      newTotalCost: 'New Total Cost',
      noInventoryAllocations: 'No inventory allocations found',
      noMachineryAllocations: 'No machinery allocations found',
      allocatedOn: 'Allocated on',
      inUse: 'In Use',
      currentlyInUse: 'Currently in use by this user',
      returnedToFarm: 'Returned to farm',
      partiallyReturned: 'Partially returned to farm',
      expectedReturn: 'Expected return',
      returnedOn: 'Returned on',
      inventoryAllocations: 'Inventory Allocations',
      machineryAllocations: 'Machinery Allocations',
      condition: 'Condition',
      excellent: 'Excellent',
      good: 'Good',
      fair: 'Fair',
      poor: 'Poor',
      partiallyReturned: 'Partially Returned',
      filter: 'Filter',
      all: 'All',
      active: 'Active',
      returned: 'Returned',
      notes: 'Notes',
      noActiveInventory: 'No active inventory allocations',
      noReturnedInventory: 'No returned inventory allocations',
      noActiveMachinery: 'No active machinery allocations',
      noReturnedMachinery: 'No returned machinery allocations',
      myAllocations: 'My Allocations',
    },
    chat: {
      aiAssistant: 'AI Assistant',
      welcome: 'Welcome',
      iAmYourAssistant: 'I am your farm management assistant. I can help you with inventory, machinery, and requests.',
      ownerCapabilities: 'As an owner, you can',
      adminCapabilities: 'As an admin, you can',
      caretakerCapabilities: 'As a caretaker, you can',
      createInventory: 'Create inventory items',
      createMachinery: 'Create machinery',
      createRequests: 'Create requests',
      typeHelpForMore: 'Type "help" for more information.',
      typeMessage: 'Type your message...',
      you: 'You',
      assistant: 'Assistant',
      typing: 'typing',
      suggestions: 'Suggestions',
      clearChat: 'Clear Chat',
      clearChatConfirm: 'Are you sure you want to clear the chat history?',
      noFarmSelected: 'No farm selected. Please select a farm first.',
      noFarm: 'No Farm',
      errorProcessingCommand: 'Sorry, I encountered an error while processing your command. Please try again.',
      user: 'User',
      openAiAssistant: 'Open AI Assistant to get help with farm management',
      selectImageSource: 'Select Image Source',
      selectImageSourceMessage: 'Choose how you want to add an image',
      camera: 'Camera',
      gallery: 'Gallery',
      cameraPermissionRequired: 'Camera permission is required to take photos',
      galleryPermissionRequired: 'Gallery permission is required to select photos',
      errorOpeningCamera: 'Error opening camera. Please try again.',
      errorOpeningGallery: 'Error opening gallery. Please try again.',
      imageAnalysis: 'Image Analysis',
      detectedInventory: 'Detected Inventory Item',
      detectedMachinery: 'Detected Machinery',
      detectedUnknown: 'Could not identify item',
      confidence: 'Confidence',
      extractedData: 'Extracted Data',
      imageUploaded: 'Image uploaded for analysis',
      addingInventory: 'Adding Inventory Item',
      addingMachinery: 'Adding Machinery',
      cancelFormConfirm: 'Are you sure you want to cancel? All entered data will be lost.',
      aiAnalysisResults: 'AI Analysis Results',
      selectUseMachinery: 'Select Machinery to Use',
      selectMaintenanceMachinery: 'Select Machinery for Maintenance',
      selectFuelMachinery: 'Select Machinery for Fuel',
      selectMachineryFromList: 'Choose from the available machinery below:',
      cancelRequestConfirm: 'Are you sure you want to cancel this request?',
      requestCancelled: 'Request cancelled.',
      requestAll: 'Request All',
      selectFromSuggestions: 'Select from the suggestions below or type in chat',
      enterDateInChat: 'Type the date in chat (e.g., tomorrow, 2024-01-15)',
      typeResponseInChat: 'Type your response in the chat input below',
      tapToSelect: 'Tap to select',
      selectDateAbove: 'Tap the date button above to select a date',
      tapToViewDetails: 'Tap to view details',
    },
  },
  ur: {
    common: {
      loading: 'لوڈ ہو رہا ہے...',
      error: 'خرابی',
      success: 'کامیابی',
      cancel: 'منسوخ کریں',
      save: 'محفوظ کریں',
      delete: 'حذف کریں',
      fieldRequired: 'یہ فیلڈ ضروری ہے',
      invalidNumber: 'براہ کرم صحیح نمبر درج کریں',
      next: 'اگلا',
      submit: 'جمع کریں',
      yes: 'ہاں',
      no: 'نہیں',
      skip: 'چھوڑیں',
      optional: 'اختیاری',
      selectDate: 'تاریخ منتخب کریں',
      edit: 'ترمیم کریں',
      create: 'بنائیں',
      update: 'اپڈیٹ کریں',
      confirm: 'تصدیق کریں',
      yes: 'ہاں',
      no: 'نہیں',
      ok: 'ٹھیک ہے',
      done: 'ہو گیا',
      next: 'اگلا',
      previous: 'پچھلا',
      back: 'واپس',
      search: 'تلاش کریں',
      filter: 'فلٹر',
      all: 'تمام',
      none: 'کوئی نہیں',
      select: 'منتخب کریں',
      category: 'زمرہ',
      date: 'تاریخ',
      time: 'وقت',
      description: 'تفصیل',
      details: 'تفصیلات',
      name: 'نام',
      quantity: 'مقدار',
      price: 'قیمت',
      total: 'کل',
      status: 'حالت',
      actions: 'اقدامات',
      notes: 'نوٹس',
      reason: 'وجہ',
      bio: 'تعارف',
      tapToViewDetails: 'تفصیلات دیکھنے کے لیے ٹیپ کریں',
      notSet: 'طے نہیں',
      notSpecified: 'مخصوص نہیں',
      invalidDate: 'غلط تاریخ',
      today: 'آج',
      yesterday: 'کل',
      monday: 'پیر',
      tuesday: 'منگل',
      wednesday: 'بدھ',
      thursday: 'جمعرات',
      friday: 'جمعہ',
      saturday: 'ہفتہ',
      sunday: 'اتوار',
      language: 'زبان',
      version: 'ورژن',
      kilometers: 'کلومیٹر',
      logout: 'لاگ آؤٹ',
      submit: 'جمع کریں',
      unit: 'یونٹ',
      noFarmSelected: 'کوئی فارم منتخب نہیں',
      unknownError: 'ایک نامعلوم خرابی پیش آئی',
      unknownFarm: 'نامعلوم فارم',
      unknown: 'نامعلوم',
      selectDate: 'تاریخ منتخب کریں',
      camera: 'کیمرہ',
      gallery: 'گیلری',
      remove: 'ہٹائیں',
      play: 'چلائیں',
      pause: 'روکیں',
      remaining: 'باقی',
      farmIdRequired: 'فارم آئی ڈی ضروری ہے',
      pleaseSelectFarm: 'دیکھنے کے لیے براہ کرم فارم منتخب کریں',
      contactAdmin: 'شامل کرنے کے لیے ایڈمن سے رابطہ کریں',
      noDataFound: 'کوئی ڈیٹا نہیں ملا',
      tryDifferentSearch: 'مختلف تلاش یا فلٹر آزمائیں',
      requestUse: 'استعمال کی درخواست کریں',
      available: 'دستیاب',
      maintenanceStatus: 'دیکھ بھال',
      malfunction: 'خرابی',
      inUse: 'استعمال میں',
      totalMachinery: 'کل مشینری',
      requestMachineryFromAdmin: 'ایڈمن سے مشینری کی درخواست کریں',
      requestMachineryFromOwner: 'مالک سے مشینری کی درخواست کریں',
      noMachineryFound: 'کوئی مشینری نہیں ملی',
      loadingMachinery: 'مشینری لوڈ ہو رہی ہے...',
      farmMachinery: 'فارم مشینری',
      caretakerRequests: 'نگہداشت کنندہ کی درخواستیں',
      myRequests: 'میری درخواستیں',
      inventoryRequests: 'انوینٹری کی درخواستیں',
      machineryRequests: 'مشینری کی درخواستیں',
      returns: 'واپسی',
      request: 'درخواست',
      searchMachineryRequests: 'مشینری کی درخواستیں تلاش کریں...',
      searchInventoryRequests: 'انوینٹری کی درخواستیں تلاش کریں...',
      noInventoryRequestsFromCaretakers: 'نگہداشت کنندگان سے کوئی انوینٹری درخواست نہیں',
      noInventoryRequestsToOwner: 'مالک کو کوئی انوینٹری درخواست نہیں',
      noMachineryRequestsFromCaretakers: 'نگہداشت کنندگان سے کوئی مشینری درخواست نہیں',
      noMachineryRequestsToOwner: 'مالک کو کوئی مشینری درخواست نہیں',
      inventoryRequestsFromCaretakersWillAppearHere: 'نگہداشت کنندگان کی انوینٹری درخواستیں یہاں ظاہر ہوں گی',
      yourInventoryRequestsToOwnerWillAppearHere: 'مالک کو آپ کی انوینٹری درخواستیں یہاں ظاہر ہوں گی',
      machineryRequestsFromCaretakersWillAppearHere: 'نگہداشت کنندگان کی مشینری درخواستیں یہاں ظاہر ہوں گی',
      yourMachineryRequestsToOwnerWillAppearHere: 'مالک کو آپ کی مشینری درخواستیں یہاں ظاہر ہوں گی',
      yourInventoryRequestsWillAppearHere: 'آپ کی انوینٹری درخواستیں یہاں ظاہر ہوں گی',
      yourMachineryRequestsWillAppearHere: 'آپ کی مشینری درخواستیں یہاں ظاہر ہوں گی',
      requestsFromAdminsWillAppearHere: 'ایڈمنز کی درخواستیں یہاں ظاہر ہوں گی',
      machineryRequestsFromAdminsWillAppearHere: 'ایڈمنز کی مشینری درخواستیں یہاں ظاہر ہوں گی',
      warning: 'انتباہ',
      info: 'معلومات',
      requestSubmittedSuccessfully: 'درخواست کامیابی سے جمع کر دی گئی',
      failedToSubmitRequest: 'درخواست جمع کرنے میں ناکامی۔ براہ کرم دوبارہ کوشش کریں۔',
      missingRequiredInformation: 'ضروری معلومات غائب ہیں',
      machineryUseRequestSubmitted: 'مشینری استعمال کی درخواست کامیابی سے جمع کر دی گئی',
      requestToUseMachinery: '{machineryName} ({machineryModel}) استعمال کرنے کی درخواست',
      fuel: 'ایندھن',
      nextMaintenance: 'اگلی دیکھ بھال',
      odometer: 'اوڈومیٹر',
      hours: 'گھنٹے',
      model: 'ماڈل',
      year: 'سال',
      type: 'قسم',
      currentFuelLevel: 'موجودہ ایندھن کی سطح',
      fuelCapacity: 'ایندھن کی گنجائش',
      nextMaintenanceDate: 'اگلی دیکھ بھال کی تاریخ',
      odometerReading: 'اوڈومیٹر ریڈنگ',
      working: 'کام کر رہا',
      inUseOtherFarm: 'دوسرے فارم میں استعمال',
      isRequired: 'ضروری ہے',
      isInvalid: 'غلط ہے',
      currentFuelLevelExceedsCapacity: 'موجودہ ایندھن کی سطح گنجائش سے زیادہ نہیں ہو سکتی',
      notAvailable: 'دستیاب نہیں',
      days: 'دن',
      maintenanceOverdue: 'دیکھ بھال کی مدت ختم',
      low: 'کم',
      medium: 'درمیانی',
      high: 'زیادہ',
      emergency: 'فوری',
      urgencyLevel: 'فوری ترین سطح',
      startDate: 'شروع کی تاریخ',
      endDate: 'اختتامی تاریخ',
      change: 'تبدیل کریں',
      submitting: 'جمع کر رہے ہیں...',
      maintenance: 'دیکھ بھال',
      bio: 'تعارف',
      totalInventory: 'کل انوینٹری',
      activeMachinery: 'فعال مشینری',
      inventoryRequest: 'انوینٹری درخواست',
      machineryRequest: 'مشینری درخواست',
      pending: 'زیر التواء',
      approved: 'منظور شدہ',
      rejected: 'مسترد شدہ',
      requestMachinery: 'مشینری کی درخواست کریں',
      requestInventory: 'انوینٹری کی درخواست کریں',
      newItem: 'نیا آئٹم',
      existingItem: 'موجودہ آئٹم',
      requestType: 'درخواست کی قسم',
      requestForUse: 'استعمال کی درخواست',
      requestForFuel: 'ایندھن کی درخواست',
      requestForMaintenance: 'دیکھ بھال کی درخواست',
      requestForTransfer: 'منتقلی کی درخواست',
      newItemRequest: 'نئے آئٹم کی درخواست',
      createdBy: 'بنانے والا',
      createdAt: 'بنانے کی تاریخ',
      lastUpdated: 'آخری اپڈیٹ',
      timestamps: 'ٹائم سٹیمپس',
      withAudio: 'آڈیو کے ساتھ',
      withImages: 'تصاویر کے ساتھ',
      textOnly: 'صرف متن',
      multimedia: 'ملٹی میڈیا',
      audio: 'آڈیو',
      images: 'تصاویر',
      text: 'متن',
      searchInNote: 'نوٹ میں تلاش کریں...',
      noMatchesFound: 'کے لیے کوئی نتیجہ نہیں ملا',
      tryAdjustingSearch: 'اپنی تلاش کی شرائط کو ایڈجسٹ کرنے کی کوشش کریں',
      listeningAndConverting: 'سن رہے ہیں اور تقریر کو متن میں تبدیل کر رہے ہیں...',
      recognizedText: 'پہچانا گیا متن',
      speakClearly: 'اپنی آواز کو متن میں تبدیل کرنے کے لیے صاف بولیں',
      stopListening: 'سننا بند کریں',
      listening: 'سن رہے ہیں...',
      addAttachments: 'منسلکات شامل کریں',
      voiceNote: 'صوتی نوٹ',
      recordVoiceNote: 'صوتی نوٹ ریکارڈ کریں',
      stopRecording: 'ریکارڈنگ بند کریں',
      playVoiceNote: 'صوتی نوٹ چلائیں',
      pauseVoiceNote: 'صوتی نوٹ روکیں',
      audioRecordingReady: 'آڈیو ریکارڈنگ تیار ہے',
      playingAudio: 'آڈیو چل رہا ہے...',
      tapToPlay: 'چلانے کے لیے ٹیپ کریں',
      tapToPause: 'روکنے کے لیے ٹیپ کریں',
      saveNote: 'نوٹ محفوظ کریں',
      saveChanges: 'تبدیلیاں محفوظ کریں',
      createNewNote: 'نیا نوٹ بنائیں',
      documentImportantObservations: 'اہم مشاہدات اور اپڈیٹس کو دستاویز کریں',
      updateNoteWithNewInfo: 'اپنے نوٹ کو نئی معلومات کے ساتھ اپڈیٹ کریں',
      enterDescriptiveTitle: 'وضاحتی عنوان درج کریں...',
      writeDetailedNote: 'یہاں اپنا تفصیلی نوٹ لکھیں...',
      attachments: 'منسلکات',
      imagesCount: 'تصاویر ({count})',
      audioRecording: 'آڈیو ریکارڈنگ',
      permissionsRequired: 'اجازات درکار ہیں',
      grantCameraAndMicPermissions: 'تمام فیچرز استعمال کرنے کے لیے براہ کرم کیمرہ اور مائیکروفون کی اجازات دیں۔',
      noteCreatedSuccessfully: 'نوٹ کامیابی سے بنایا گیا',
      failedToCreateNote: 'نوٹ بنانے میں ناکامی۔ براہ کرم دوبارہ کوشش کریں۔',
      enterTitleForNote: 'براہ کرم اپنے نوٹ کے لیے عنوان درج کریں',
      enterContentForNote: 'براہ کرم اپنے نوٹ کے لیے کچھ مواد درج کریں',
      selectFarmForNote: 'براہ کرم اپنے نوٹ کے لیے فارم منتخب کریں',
      loadingNote: 'نوٹ لوڈ ہو رہا ہے...',
      noteNotFound: 'نوٹ نہیں ملا',
      goBack: 'واپس جائیں',
      deleteNote: 'نوٹ حذف کریں',
      areYouSureDeleteNote: 'کیا آپ واقعی اس نوٹ کو حذف کرنا چاہتے ہیں؟',
      noteDeletedSuccessfully: 'نوٹ کامیابی سے حذف کر دیا گیا',
      failedToDeleteNote: 'نوٹ حذف کرنے میں ناکامی',
      failedToPlayAudio: 'آڈیو چلانے میں ناکامی',
      editNote: 'نوٹ میں ترمیم کریں',
      noteUpdatedSuccessfully: 'نوٹ کامیابی سے اپڈیٹ ہو گیا',
      failedToUpdateNote: 'نوٹ اپڈیٹ کرنے میں ناکامی۔ براہ کرم دوبارہ کوشش کریں۔',
      noteDetails: 'نوٹ کی تفصیلات',
      noResultsFound: 'کوئی نتائج نہیں ملے',
      phone: 'فون',
      // New filter and sorting translations
      filters: 'فلٹرز',
      sortBy: 'ترتیب دیں',
      sortOrder: 'ترتیب کا طریقہ',
      ascending: 'صاعد',
      descending: 'نازل',
      clearFilters: 'فلٹرز صاف کریں',
      applyFilters: 'فلٹرز لاگو کریں',
      filterBy: 'فلٹر کریں',
      showAll: 'تمام دکھائیں',
      showOnly: 'صرف دکھائیں',
      dateRange: 'تاریخ کی رینج',
      from: 'سے',
      to: 'تک',
      selectAll: 'تمام منتخب کریں',
      deselectAll: 'تمام غیر منتخب کریں',
      noFiltersApplied: 'کوئی فلٹر لاگو نہیں',
      filtersApplied: 'فلٹرز لاگو کیے گئے',
      resetFilters: 'فلٹرز ری سیٹ کریں',
      advancedFilters: 'ایڈوانس فلٹرز',
      quickFilters: 'فوری فلٹرز',
      customFilter: 'کسٹم فلٹر',
      // Navigation and UI
      home: 'ہوم',
      dashboard: 'ڈیش بورڈ',
      settings: 'سیٹنگز',
      profile: 'پروفائل',
      menu: 'مینو',
      close: 'بند کریں',
      open: 'کھولیں',
      expand: 'پھیلائیں',
      collapse: 'سکیڑیں',
      refresh: 'تازہ کریں',
      reload: 'دوبارہ لوڈ کریں',
      retry: 'دوبارہ کوشش کریں',
      // Form elements
      required: 'ضروری',
      optional: 'اختیاری',
      placeholder: 'قیمت درج کریں...',
      selectOption: 'ایک آپشن منتخب کریں',
      chooseFile: 'فائل منتخب کریں',
      uploadFile: 'فائل اپ لوڈ کریں',
      dragAndDrop: 'فائلیں یہاں ڈریگ اور ڈراپ کریں',
      browse: 'براؤز کریں',
      // Status indicators
      active: 'فعال',
      inactive: 'غیر فعال',
      enabled: 'فعال',
      disabled: 'غیر فعال',
      online: 'آن لائن',
      offline: 'آف لائن',
      connected: 'جڑا ہوا',
      disconnected: 'منقطع',
      synced: 'ہم آہنگ',
      syncing: 'ہم آہنگ ہو رہا ہے',
      failed: 'ناکام',
      completed: 'مکمل',
      inProgress: 'جاری',
      // Validation messages
      fieldRequired: 'یہ فیلڈ ضروری ہے',
      invalidFormat: 'غلط فارمیٹ',
      invalidEmail: 'غلط ای میل ایڈریس',
      invalidPhone: 'غلط فون نمبر',
      passwordTooShort: 'پاس ورڈ بہت چھوٹا ہے',
      passwordsDoNotMatch: 'پاس ورڈ میل نہیں کھاتے',
      valueTooLow: 'قیمت بہت کم ہے',
      valueTooHigh: 'قیمت بہت زیادہ ہے',
      invalidDate: 'غلط تاریخ',
      dateInPast: 'تاریخ ماضی میں نہیں ہو سکتی',
      dateInFuture: 'تاریخ مستقبل میں نہیں ہو سکتی',
      // Action confirmations
      confirmDelete: 'کیا آپ واقعی اس آئٹم کو حذف کرنا چاہتے ہیں؟',
      confirmSave: 'کیا آپ واقعی تبدیلیاں محفوظ کرنا چاہتے ہیں؟',
      confirmCancel: 'کیا آپ واقعی منسوخ کرنا چاہتے ہیں؟ غیر محفوظ شدہ تبدیلیاں ضائع ہو جائیں گی۔',
      confirmLogout: 'کیا آپ واقعی لاگ آؤٹ کرنا چاہتے ہیں؟',
      confirmReset: 'کیا آپ واقعی تمام ڈیٹا ری سیٹ کرنا چاہتے ہیں؟',
      // Success messages
      savedSuccessfully: 'کامیابی سے محفوظ ہو گیا',
      deletedSuccessfully: 'کامیابی سے حذف ہو گیا',
      updatedSuccessfully: 'کامیابی سے اپڈیٹ ہو گیا',
      createdSuccessfully: 'کامیابی سے بنایا گیا',
      uploadedSuccessfully: 'کامیابی سے اپ لوڈ ہو گیا',
      // Error messages
      saveFailed: 'محفوظ کرنے میں ناکامی',
      deleteFailed: 'حذف کرنے میں ناکامی',
      updateFailed: 'اپڈیٹ کرنے میں ناکامی',
      createFailed: 'بنانے میں ناکامی',
      uploadFailed: 'اپ لوڈ کرنے میں ناکامی',
      loadFailed: 'لوڈ کرنے میں ناکامی',
      connectionError: 'کنکشن کی خرابی',
      serverError: 'سرور کی خرابی',
      networkError: 'نیٹ ورک کی خرابی',
      timeoutError: 'درخواست کا وقت ختم',
      // Pagination
      page: 'صفحہ',
      of: 'کا',
      itemsPerPage: 'فی صفحہ آئٹمز',
      showingItems: '{total} میں سے {start} سے {end} تک آئٹمز دکھا رہے ہیں',
      noItemsToShow: 'دکھانے کے لیے کوئی آئٹم نہیں',
      loadMore: 'مزید لوڈ کریں',
      // Time and date
      now: 'اب',
      justNow: 'ابھی ابھی',
      minutesAgo: '{count} منٹ پہلے',
      hoursAgo: '{count} گھنٹے پہلے',
      daysAgo: '{count} دن پہلے',
      weeksAgo: '{count} ہفتے پہلے',
      monthsAgo: '{count} مہینے پہلے',
      yearsAgo: '{count} سال پہلے',
      // File operations
      download: 'ڈاؤن لوڈ',
      upload: 'اپ لوڈ',
      import: 'درآمد',
      export: 'برآمد',
      share: 'شیئر',
      copy: 'کاپی',
      move: 'منتقل',
      rename: 'نام تبدیل کریں',
      duplicate: 'نقل',
      // Permissions
      permissionDenied: 'اجازت مسترد',
      permissionRequired: 'اجازت درکار',
      grantPermission: 'اجازت دیں',
      cameraPermission: 'کیمرہ کی اجازت',
      microphonePermission: 'مائیکروفون کی اجازت',
      storagePermission: 'اسٹوریج کی اجازت',
      locationPermission: 'مقام کی اجازت',
      // Farm detail screen specific
      farmDetails: 'فارم کی تفصیلات',
      loadingFarmDetails: 'فارم کی تفصیلات لوڈ ہو رہی ہیں...',
      farmNotFound: 'فارم نہیں ملا',
      farmNotFoundMessage: 'آپ جس فارم کو تلاش کر رہے ہیں وہ نہیں ملا۔',
      basicInformation: 'بنیادی معلومات',
      farmType: 'فارم کی قسم',
      type: 'قسم',
      createdAt: 'بنانے کی تاریخ',
      size: 'سائز',
      location: 'مقام',
      openInMaps: 'نقشے میں کھولیں',
      coordinates: 'کوآرڈینیٹس',
      statistics: 'اعداد و شمار',
      caretakers: 'نگہداشت کنندگان',
      inventoryItems: 'انوینٹری آئٹمز',
      assignedCaretakers: 'تفویض شدہ نگہداشت کنندگان',
      noCaretakersFound: 'تفویض شدہ آئی ڈیز کے لیے کوئی نگہداشت کنندہ نہیں ملا۔',
      farmInformation: 'فارم کی معلومات',
      created: 'بنایا گیا',
      owner: 'مالک',
      unknownOwner: 'نامعلوم مالک',
      couldNotOpenMaps: 'نقشے نہیں کھل سکے',
      deleteFarm: 'فارم حذف کریں',
      deleteFarmConfirmation: 'کیا آپ واقعی {farmName} کو حذف کرنا چاہتے ہیں؟ یہ عمل واپس نہیں ہو سکتا اور تمام متعلقہ ڈیٹا ہٹا دے گا۔',
      farmDeletedSuccessfully: 'فارم کامیابی سے حذف کر دیا گیا',
      failedToDeleteFarm: 'فارم حذف کرنے میں ناکامی',
      // Inventory item detail screen specific
      itemDetails: 'آئٹم کی تفصیلات',
      loadingItemDetails: 'آئٹم کی تفصیلات لوڈ ہو رہی ہیں...',
      itemNotFound: 'آئٹم نہیں ملا',
      itemNotFoundMessage: 'آپ جس آئٹم کو تلاش کر رہے ہیں وہ موجود نہیں یا ہٹا دیا گیا ہے۔',
      backToInventory: 'انوینٹری پر واپس',
      searchInItem: 'اس آئٹم میں تلاش کریں...',
      deleteItem: 'آئٹم حذف کریں',
      deleteItemConfirmation: 'کیا آپ واقعی اس آئٹم کو حذف کرنا چاہتے ہیں؟ یہ عمل واپس نہیں ہو سکتا۔',
      itemDeletedSuccessfully: 'آئٹم کامیابی سے حذف کر دیا گیا',
      failedToDeleteItem: 'آئٹم حذف کرنے میں ناکامی',
      sharingNotAvailableWeb: 'ویب پر شیئرنگ دستیاب نہیں',
      failedToShareItem: 'آئٹم شیئر کرنے میں ناکامی',
      editItem: 'آئٹم میں ترمیم کریں',
      requestTransfer: 'منتقلی کی درخواست کریں',
      itemInformation: 'آئٹم کی معلومات',
      currentStock: 'موجودہ اسٹاک',
      minimumStock: 'کم سے کم اسٹاک',
      farmLocation: 'فارم کا مقام',
      purchaseDate: 'خریداری کی تاریخ',
      expiryDate: 'ختم ہونے کی تاریخ',
      supplier: 'سپلائر',
      notesAndDescription: 'نوٹس اور تفصیل',
      noMatchesFoundFor: '"{query}" کے لیے کوئی نتیجہ نہیں ملا',
      clearSearch: 'تلاش صاف کریں',
      expired: 'ختم ہو گیا',
      lowStock: 'کم اسٹاک',
      expiringSoon: 'جلد ختم ہونے والا',
      inStock: 'اسٹاک میں',
      itemExpired: 'آئٹم ختم ہو گیا',
      itemExpiredMessage: 'یہ آئٹم {date} کو ختم ہو گیا',
      lowStockAlert: 'کم اسٹاک کی انتباہ',
      lowStockMessage: 'صرف {quantity} {unit} باقی ہے (کم سے کم: {minQuantity} {unit})',
      expiringSoonAlert: 'جلد ختم ہونے والا',
      expiringSoonMessage: 'یہ آئٹم {date} کو ختم ہو جائے گا',
      // Additional common translations
      diesel: 'ڈیزل',
      gasoline: 'پیٹرول',
      electric: 'برقی',
      hybrid: 'ہائبرڈ',
      greenhouse: 'گرین ہاؤس',
      orchard: 'باغ',
      farm: 'فارم',
      admin: 'ایڈمن',
      caretaker: 'نگہداشت کنندہ',
      owner: 'مالک',
      users: 'صارفین',
    },
    auth: {
      login: 'لاگ ان',
      signup: 'سائن اپ',
      logout: 'لاگ آؤٹ',
      email: 'ای میل',
      password: 'پاس ورڈ',
      confirmPassword: 'پاس ورڈ کی تصدیق کریں',
      forgotPassword: 'پاس ورڈ بھول گئے؟',
      resetPassword: 'پاس ورڈ ری سیٹ کریں',
      dontHaveAccount: 'اکاؤنٹ نہیں ہے؟',
      alreadyHaveAccount: 'پہلے سے اکاؤنٹ ہے؟',
      loginSuccess: 'لاگ ان کامیاب',
      loginError: 'لاگ ان ناکام',
      signupSuccess: 'سائن اپ کامیاب',
      signupError: 'سائن اپ ناکام',
      logoutSuccess: 'لاگ آؤٹ کامیاب',
      logoutError: 'لاگ آؤٹ ناکام',
      resetPasswordSuccess: 'پاس ورڈ ری سیٹ ای میل بھیجا گیا',
      resetPasswordError: 'پاس ورڈ ری سیٹ ای میل بھیجنے میں ناکامی',
      passwordsDontMatch: 'پاس ورڈ میل نہیں کھاتے',
      invalidEmail: 'غلط ای میل ایڈریس',
      weakPassword: 'پاس ورڈ بہت کمزور ہے',
      emailAlreadyInUse: 'ای میل پہلے سے استعمال میں ہے',
      userNotFound: 'صارف نہیں ملا',
      wrongPassword: 'غلط پاس ورڈ',
      tooManyRequests: 'بہت زیادہ درخواستیں، بعد میں کوشش کریں',
      networkError: 'نیٹ ورک کی خرابی، اپنا کنکشن چیک کریں',
      unknownError: 'ایک نامعلوم خرابی پیش آئی',
      loginRequired: 'لاگ ان ضروری ہے',
      // New signup fields
      cnic: 'شناختی کارڈ',
      address: 'پتہ',
      profileImage: 'پروفائل تصویر',
      enterCnic: 'شناختی کارڈ نمبر درج کریں',
      enterAddress: 'اپنا پتہ درج کریں',
      selectProfileImage: 'پروفائل تصویر منتخب کریں',
      cnicRequired: 'شناختی کارڈ ضروری ہے',
      addressRequired: 'پتہ ضروری ہے',
      invalidCnic: 'غلط شناختی کارڈ فارمیٹ',
      createAccount: 'اکاؤنٹ بنائیں',
      accountCreated: 'اکاؤنٹ کامیابی سے بنایا گیا',
      redirectingToFarmSetup: 'فارم سیٹ اپ کی طرف بھیجا جا رہا ہے...',
    },
    dashboard: {
      title: 'ڈیش بورڈ',
      welcome: 'خوش آمدید',
      recentActivity: 'حالیہ سرگرمی',
      viewAll: 'تمام دیکھیں',
      stats: 'اعداد و شمار',
      totalItems: 'کل اشیاء',
      lowStockItems: 'کم اسٹاک',
      expiryAlerts: 'ختم ہونے کی انتباہ',
      pendingRequests: 'زیر التواء درخواستیں',
      caretakers: 'نگہداشت کنندگان',
      totalUsers: 'کل صارفین',
      totalFarms: 'کل فارمز',
      availableItems: 'دستیاب اشیاء',
      approvedRequests: 'منظور شدہ درخواستیں',
      quickActions: 'فوری اقدامات',
      viewInventory: 'انوینٹری دیکھیں',
      createRequest: 'درخواست بنائیں',
      addNote: 'نوٹ شامل کریں',
      viewReports: 'رپورٹس دیکھیں',
      manageUsers: 'صارفین کا انتظام',
      manageFarms: 'فارمز کا انتظام',
      noRecentActivity: 'کوئی حالیہ سرگرمی نہیں',
      activityRelatedTo: '{item} سے متعلق سرگرمی',
      totalInventory: 'کل انوینٹری',
      activeMachinery: 'فعال مشینری',
      inventoryRequests: 'انوینٹری کی درخواستیں',
      machineryRequests: 'مشینری کی درخواستیں',
      alertsNotifications: 'انتباہات اور اطلاعات',
      lowStockAlert: 'کم اسٹاک کی انتباہ',
      lowStockMessage: '{count} اشیاء کا اسٹاک کم ہو رہا ہے',
      expiryAlert: 'ختم ہونے کی انتباہ',
      expiryMessage: '{count} اشیاء جلد ختم ہونے والی ہیں',
      maintenanceAlert: 'دیکھ بھال کی انتباہ',
      maintenanceMessage: '{count} مشینری کو دیکھ بھال کی ضرورت ہے',
      pendingRequestsMessage: '{count} درخواستیں منظوری کے انتظار میں ہیں',
      myPendingRequests: 'میری زیر التواء درخواستیں',
      myPendingRequestsMessage: 'آپ کی {count} درخواستیں زیر التواء ہیں',
      myMachineryRequestsMessage: 'آپ کی {count} مشینری کی درخواستیں ہیں',
      addInventory: 'انوینٹری شامل کریں',
      addMachinery: 'مشینری شامل کریں',
      requestMachinery: 'مشینری کی درخواست کریں',
      manageRequests: 'درخواستوں کا انتظام',
      viewRequests: 'درخواستیں دیکھیں',
      myInventoryRequests: 'میری انوینٹری کی درخواستیں',
      myMachineryRequests: 'میری مشینری کی درخواستیں',
    },
    profile: {
      title: 'پروفائل',
      editProfile: 'پروفائل میں ترمیم',
      viewProfile: 'پروفائل دیکھیں',
      personalInfo: 'ذاتی معلومات',
      personalInformation: 'ذاتی معلومات',
      contactInfo: 'رابطے کی معلومات',
      roleInfo: 'کردار کی معلومات',
      farmInfo: 'فارم کی معلومات',
      activityInfo: 'سرگرمی کی معلومات',
      name: 'نام',
      email: 'ای میل',
      phone: 'فون',
      role: 'کردار',
      status: 'حالت',
      lastLogin: 'آخری لاگ ان',
      memberSince: 'ممبر بننے کی تاریخ',
      assignedFarms: 'تفویض شدہ فارمز',
      recentActivity: 'حالیہ سرگرمی',
      noRecentActivity: 'کوئی حالیہ سرگرمی نہیں',
      viewAllActivity: 'تمام سرگرمی دیکھیں',
      changePassword: 'پاس ورڈ تبدیل کریں',
      logout: 'لاگ آؤٹ',
      profileUpdated: 'پروفائل کامیابی سے اپڈیٹ ہو گیا',
      profileUpdateError: 'پروفائل اپڈیٹ کرنے میں ناکامی',
      uploadPhoto: 'تصویر اپ لوڈ کریں',
      removePhoto: 'تصویر ہٹائیں',
      photoUpdated: 'تصویر کامیابی سے اپڈیٹ ہو گئی',
      photoUpdateError: 'تصویر اپڈیٹ کرنے میں ناکامی',
      photoRemoved: 'تصویر کامیابی سے ہٹا دی گئی',
      photoRemoveError: 'تصویر ہٹانے میں ناکامی',
      owner: 'مالک',
      admin: 'ایڈمن',
      caretaker: 'نگہداشت کنندہ',
      myAssignedFarm: 'میرا تفویض شدہ فارم',
      myRequests: 'میری درخواستیں',
      viewRequests: 'اپنی درخواستوں کو دیکھیں اور منظم کریں',
      darkMode: 'ڈارک موڈ',
      lightMode: 'لائٹ موڈ',
      yourName: 'آپ کا نام',
      yourPhoneNumber: 'آپ کا فون نمبر',
      notProvided: 'فراہم نہیں کیا گیا',
      noBioProvided: 'کوئی تعارف فراہم نہیں کیا گیا',
      settings: 'سیٹنگز',
      tellUsAboutYourself: 'اپنے بارے میں بتائیں',
      english: 'انگریزی',
      urdu: 'اردو',
      notSet: 'طے نہیں',
      accountInfo: 'اکاؤنٹ کی معلومات',
      logoutConfirm: 'کیا آپ واقعی لاگ آؤٹ کرنا چاہتے ہیں؟',
      imageUploadError: 'تصویر اپ لوڈ کرنے میں ناکامی',
      // New profile fields
      cnic: 'شناختی کارڈ',
      address: 'پتہ',
      profileImage: 'پروفائل تصویر',
      updateProfileImage: 'پروفائل تصویر اپڈیٹ کریں',
      cnicNumber: 'شناختی کارڈ نمبر',
      fullAddress: 'مکمل پتہ',
      bio: 'تعارف',
      gender: 'جنس',
      dateOfBirth: 'تاریخ پیدائش',
      saveChanges: 'تبدیلیاں محفوظ کریں',
      editingProfile: 'پروفائل میں ترمیم',
    },
    changePassword: {
      title: 'پاس ورڈ تبدیل کریں',
      subtitle: 'اپنے اکاؤنٹ کو محفوظ رکھنے کے لیے اپنا پاس ورڈ اپڈیٹ کریں',
      updateSecurityDescription: 'مضبوط پاس ورڈ کے ساتھ اپنے اکاؤنٹ کو محفوظ رکھیں',
      currentPassword: 'موجودہ پاس ورڈ',
      newPassword: 'نیا پاس ورڈ',
      confirmPassword: 'نیا پاس ورڈ کی تصدیق کریں',
      enterCurrentPassword: 'اپنا موجودہ پاس ورڈ درج کریں',
      enterNewPassword: 'اپنا نیا پاس ورڈ درج کریں',
      confirmNewPassword: 'اپنے نئے پاس ورڈ کی تصدیق کریں',
      changePassword: 'پاس ورڈ تبدیل کریں',
      passwordStrength: 'پاس ورڈ کی مضبوطی',
      weak: 'کمزور',
      medium: 'درمیانی',
      good: 'اچھا',
      strong: 'مضبوط',
      passwordRequirements: 'پاس ورڈ کی ضروریات',
      minLength: 'کم سے کم 8 حروف',
      hasUpperCase: 'کم سے کم ایک بڑا حرف',
      hasLowerCase: 'کم سے کم ایک چھوٹا حرف',
      hasNumbers: 'کم سے کم ایک نمبر',
      hasSpecialChar: 'کم سے کم ایک خاص حرف',
      passwordsMatch: 'پاس ورڈ میل کھاتے ہیں',
      currentPasswordRequired: 'موجودہ پاس ورڈ ضروری ہے',
      newPasswordRequired: 'نیا پاس ورڈ ضروری ہے',
      confirmPasswordRequired: 'براہ کرم اپنے نئے پاس ورڈ کی تصدیق کریں',
      passwordsDoNotMatch: 'پاس ورڈ میل نہیں کھاتے',
      newPasswordSameAsCurrent: 'نیا پاس ورڈ موجودہ پاس ورڈ سے مختلف ہونا چاہیے',
      success: 'کامیابی',
      error: 'خرابی',
      passwordChangedSuccessfully: 'آپ کا پاس ورڈ کامیابی سے تبدیل ہو گیا ہے',
      failedToChangePassword: 'پاس ورڈ تبدیل کرنے میں ناکامی۔ براہ کرم دوبارہ کوشش کریں۔',
      securityTips: 'سیکیورٹی ٹپس',
      securityTipsDescription: 'حروف، نمبرز اور علامات کے مرکب کے ساتھ مضبوط پاس ورڈ استعمال کریں۔ ذاتی معلومات یا عام الفاظ استعمال کرنے سے بچیں۔',
    },
    inventory: {
      title: 'انوینٹری',
      addItem: 'آئٹم شامل کریں',
      editItem: 'آئٹم میں ترمیم',
      deleteItem: 'آئٹم حذف کریں',
      itemDetails: 'آئٹم کی تفصیلات',
      addItemDescription: 'اپنی انوینٹری میں نیا آئٹم شامل کریں',
      updateItemDescription: '{name} کی تفصیلات اپڈیٹ کریں',
      searchItems: 'اشیاء تلاش کریں...',
      searchPlaceholder: 'نام، زمرہ، یا سپلائر کے ذریعے تلاش کریں...',
      basicInformation: 'بنیادی معلومات',
      additionalDetails: 'اضافی تفصیلات',
      itemName: 'آئٹم کا نام',
      enterItemName: 'آئٹم کا نام درج کریں',
      category: 'زمرہ',
      selectCategory: 'زمرہ منتخب کریں',
      orEnterCustomCategory: 'یا کسٹم زمرہ درج کریں',
      enterCustomCategory: 'کسٹم زمرہ درج کریں',
      quantity: 'مقدار',
      enterQuantity: 'مقدار درج کریں',
      unit: 'یونٹ',
      selectUnit: 'یونٹ منتخب کریں',
      enterCustomUnit: 'کسٹم یونٹ درج کریں',
      minimumQuantity: 'کم سے کم مقدار',
      enterMinQuantity: 'کم سے کم مقدار درج کریں',
      supplier: 'سپلائر',
      enterSupplier: 'سپلائر کا نام درج کریں',
      purchasePrice: 'خریداری کی قیمت',
      unitPrice: 'یونٹ کی قیمت',
      totalPrice: 'کل قیمت',
      purchaseDate: 'خریداری کی تاریخ',
      supplier: 'سپلائر',
      validPriceRequired: 'درست قیمت ضروری ہے',
      purchaseDateRequired: 'خریداری کی تاریخ ضروری ہے',
      estimatedPrice: 'تخمینی قیمت',
      priceNotSpecified: 'قیمت مخصوص نہیں',
      totalAmount: 'کل رقم',
      purchaseDate: 'خریداری کی تاریخ',
      selectPurchaseDate: 'خریداری کی تاریخ منتخب کریں',
      isConsumable: 'استعمال ہونے والا سامان',
      consumableDescription: 'اگر یہ سامان استعمال میں ختم ہو جاتا ہے تو چیک کریں',
      enterPrice: 'قیمت درج کریں',
      enterUnitPrice: 'یونٹ کی قیمت درج کریں',
      expiryDate: 'ختم ہونے کی تاریخ',
      selectExpiryDate: 'ختم ہونے کی تاریخ منتخب کریں',
      description: 'تفصیل',
      enterDescription: 'تفصیل درج کریں',
      itemImage: 'آئٹم کی تصویر',
      uploadImage: 'تصویر اپ لوڈ کریں',
      createItem: 'آئٹم بنائیں',
      itemCreated: 'آئٹم کامیابی سے بنایا گیا',
      createError: 'آئٹم بنانے میں ناکامی',
      validation: {
        nameRequired: 'آئٹم کا نام ضروری ہے',
        categoryRequired: 'زمرہ ضروری ہے',
        quantityRequired: 'مقدار ضروری ہے',
        quantityInvalid: 'براہ کرم درست مقدار درج کریں',
        minQuantityInvalid: 'براہ کرم درست کم سے کم مقدار درج کریں',
        priceInvalid: 'براہ کرم درست قیمت درج کریں',
        priceRequired: 'یونٹ کی قیمت ضروری ہے',
        purchaseDateRequired: 'خریداری کی تاریخ ضروری ہے',
      },
      units: {
        pcs: 'ٹکڑے',
        kg: 'کلوگرام',
        lbs: 'پاؤنڈ',
        liters: 'لیٹر',
        gallons: 'گیلن',
        bags: 'بیگز',
        boxes: 'ڈبے',
      },
      // Category translations
      category_seeds: 'بیج',
      category_fertilizers: 'کھاد',
      category_pesticides: 'کیڑے مار دوا',
      category_tools: 'اوزار',
      category_equipment: 'سامان',
      category_feed: 'خوراک',
      category_medication: 'دوا',
      category_vaccination: 'ویکسینیشن',
      category_fuel: 'ایندھن',
      category_other_custom: 'دیگر (کسٹم)',
      // Unit translations
      unit_kg: 'کلوگرام',
      unit_g: 'گرام',
      unit_l: 'لیٹر',
      unit_ml: 'ملی لیٹر',
      unit_units: 'یونٹس',
      unit_bags: 'بیگز',
      unit_boxes: 'ڈبے',
      unit_bottles: 'بوتلیں',
      unit_packs: 'پیکس',
      unit_pieces: 'ٹکڑے',
      unit_other_custom: 'دیگر (کسٹم)',
      // Form labels and placeholders
      form_label_item_name: 'آئٹم کا نام',
      form_label_category: 'زمرہ',
      form_label_current_quantity: 'موجودہ مقدار',
      form_label_unit: 'یونٹ',
      form_label_low_stock_alert_level: 'کم اسٹاک کی انتباہ کی سطح',
      form_label_purchase_date: 'خریداری کی تاریخ',
      form_label_expiry_date: 'ختم ہونے کی تاریخ',
      form_label_supplier: 'سپلائر',
      form_label_price_per_unit: 'فی یونٹ قیمت',
      form_label_notes_and_description: 'نوٹس اور تفصیل',
      form_placeholder_enter_item_name_example: 'مثال: گندم کے بیج، کھاد NPK',
      form_placeholder_select_category: 'زمرہ منتخب کریں',
      form_placeholder_enter_custom_category: 'کسٹم زمرہ درج کریں',
      form_placeholder_unit: 'یونٹ منتخب کریں',
      form_placeholder_enter_custom_unit: 'کسٹم یونٹ درج کریں',
      form_placeholder_select_date: 'تاریخ منتخب کریں',
      form_placeholder_enter_supplier_name: 'سپلائر کا نام درج کریں',
      form_placeholder_add_additional_notes_placeholder: 'کوئی اضافی نوٹس یا تفصیل شامل کریں...',
      // Section titles
      form_section_title_basic_information: 'بنیادی معلومات',
      form_section_title_quantity_and_stock: 'مقدار اور اسٹاک',
      form_section_title_additional_details: 'اضافی تفصیلات',
      section_title_item_photo: 'آئٹم کی تصویر',
      // Button texts
      button_change: 'تبدیل کریں',
      button_remove: 'ہٹائیں',
      button_choose_photo: 'تصویر منتخب کریں',
      button_take_photo: 'تصویر لیں',
      button_cancel: 'منسوخ کریں',
      button_add_item: 'آئٹم شامل کریں',
      button_save_changes: 'تبدیلیاں محفوظ کریں',
      button_ok: 'ٹھیک ہے',
      // Page titles
      page_title_add_item: 'آئٹم شامل کریں',
      page_title_edit_item: 'آئٹم میں ترمیم',
      page_title_add_new_item: 'نیا آئٹم شامل کریں',
      // Validation messages
      validation_item_name_required: 'آئٹم کا نام ضروری ہے',
      validation_category_required: 'زمرہ ضروری ہے',
      validation_valid_quantity_required: 'درست مقدار ضروری ہے',
      validation_quantity_must_be_positive: 'مقدار مثبت ہونی چاہیے',
      validation_unit_required: 'یونٹ ضروری ہے',
      // Success/Error messages
      success_title: 'کامیابی',
      success_item_added_successfully: 'آئٹم کامیابی سے شامل کر دیا گیا',
      success_item_updated_successfully: 'آئٹم کامیابی سے اپڈیٹ ہو گیا',
      error_title: 'خرابی',
      error_failed_load_item_details: 'آئٹم کی تفصیلات لوڈ کرنے میں ناکامی',
      error_must_be_logged_in_to_save: 'محفوظ کرنے کے لیے آپ کا لاگ ان ہونا ضروری ہے',
      error_failed_to_add_item: 'آئٹم شامل کرنے میں ناکامی',
      error_failed_to_update_item: 'آئٹم اپڈیٹ کرنے میں ناکامی',
      error_failed_to_pick_image: 'تصویر منتخب کرنے میں ناکامی',
      error_failed_to_take_photo: 'تصویر لینے میں ناکامی',
      // Access control
      access_denied_title: 'رسائی مسترد',
      access_denied_inventory_edit_message: 'نگہداشت کنندگان انوینٹری آئٹمز میں ترمیم نہیں کر سکتے',
      // Camera/Photo
      not_available_title: 'دستیاب نہیں',
      camera_not_available_web_message: 'ویب پر کیمرہ دستیاب نہیں',
      permission_required_title: 'اجازت درکار',
      camera_permission_required_message: 'کیمرہ کی اجازت ضروری ہے',
      image_placeholder_text_add_photo_help_identify_item: 'اس آئٹم کی شناخت میں مدد کے لیے تصویر شامل کریں',
      // Help text
      form_help_text_low_stock_notification_help: 'جب اسٹاک اس سطح سے نیچے گرے گا تو آپ کو اطلاع دی جائے گی',
      noItems: 'کوئی انوینٹری آئٹم نہیں ملا',
      noItemsFound: 'آپ کی تلاش سے کوئی آئٹم میل نہیں کھاتا',
      addFirstItem: 'اپنا پہلا انوینٹری آئٹم شامل کریں',
      tryDifferentSearch: 'مختلف تلاش یا فلٹر آزمائیں',
      itemDeleted: 'آئٹم کامیابی سے حذف کر دیا گیا',
      itemAdded: 'آئٹم کامیابی سے شامل کر دیا گیا',
      itemUpdated: 'آئٹم کامیابی سے اپڈیٹ ہو گیا',
      deleteConfirm: 'کیا آپ واقعی اس آئٹم کو حذف کرنا چاہتے ہیں؟',
      loadError: 'انوینٹری آئٹمز لوڈ کرنے میں ناکامی',
      deleteError: 'آئٹم حذف کرنے میں ناکامی',
      addError: 'آئٹم شامل کرنے میں ناکامی',
      updateError: 'آئٹم اپڈیٹ کرنے میں ناکامی',
      addItemDescription: 'اپنی انوینٹری میں نیا آئٹم شامل کریں',
      updateItemDescription: 'آئٹم {name} کی تفصیلات اپڈیٹ کریں',
      itemName: 'آئٹم کا نام',
      enterItemName: 'آئٹم کا نام درج کریں',
      category: 'زمرہ',
      selectCategory: 'زمرہ منتخب کریں',
      enterCustomCategory: 'اپنا زمرہ درج کریں',
      quantity: 'مقدار',
      enterQuantity: 'مقدار درج کریں',
      unit: 'یونٹ',
      selectUnit: 'یونٹ منتخب کریں',
      enterCustomUnit: 'اپنا یونٹ درج کریں',
      minimumStock: 'کم از کم اسٹاک',
      supplier: 'سپلائر',
      enterSupplier: 'سپلائر کا نام درج کریں',
      price: 'قیمت',
      enterPrice: 'قیمت درج کریں',
      description: 'تفصیل',
      enterDescription: 'تفصیل درج کریں',
      purchaseDate: 'خریداری کی تاریخ',
      expiryDate: 'میعاد ختم ہونے کی تاریخ',
      location: 'مقام',
      enterLocation: 'مقام درج کریں',
      changeImage: 'تصویر تبدیل کریں',
      removeImage: 'تصویر ہٹائیں',
      gallery: 'گیلری',
      camera: 'کیمرہ',
      quantityDetails: 'مقدار کی تفصیلات',
      additionalDetails: 'اضافی تفصیلات',
      itemNameRequired: 'آئٹم کا نام درکار ہے',
      categoryRequired: 'زمرہ درکار ہے',
      quantityRequired: 'مقدار درکار ہے',
      unitRequired: 'یونٹ درکار ہے',
      validQuantityRequired: 'درست مقدار درکار ہے',
      positiveQuantityRequired: 'مقدار مثبت ہونی چاہیے',
      unitRequired: 'یونٹ ضروری ہے',
      categoryRequired: 'زمرہ ضروری ہے',
      enterItemName: 'آئٹم کا نام درج کریں',
      enterQuantity: 'مقدار درج کریں',
      selectCategory: 'زمرہ منتخب کریں',
      enterCustomCategory: 'کسٹم زمرہ درج کریں',
      enterCustomUnit: 'کسٹم یونٹ درج کریں',
      selectUnit: 'یونٹ منتخب کریں',
      enterDescription: 'تفصیل درج کریں',
      enterSupplier: 'سپلائر کا نام درج کریں',
      itemInformation: 'آئٹم کی معلومات',
      quantityDetails: 'مقدار کی تفصیلات',
      additionalDetails: 'اضافی تفصیلات',
      anItem: 'ایک آئٹم',
      lowStockAlert: 'کم اسٹاک کی انتباہ',
      imagePickError: 'تصویر منتخب کرنے میں ناکامی',
      cameraError: 'کیمرہ استعمال کرنے میں ناکامی',
      cameraNotAvailableWeb: 'ویب پر کیمرہ دستیاب نہیں',
      cameraPermissionRequired: 'کیمرہ کی اجازت ضروری ہے',
      sharingNotAvailable: 'اس پلیٹ فارم پر شیئرنگ دستیاب نہیں',
      backToInventory: 'انوینٹری پر واپس',
      itemNotFound: 'آئٹم نہیں ملا',
      noActivity: 'کوئی سرگرمی دستیاب نہیں',
      farm: 'فارم',
      created: 'بنایا گیا',
      updated: 'اپڈیٹ کیا گیا',
      transfer_in: 'منتقلی اندر',
      transfer_out: 'منتقلی باہر',
      lowStock: 'کم اسٹاک',
      expiringSoon: 'جلد ختم ہونے والا',
      expired: 'ختم ہو گیا',
      requestTransfer: 'منتقلی کی درخواست',
      seeds: 'بیج',
      fertilizers: 'کھاد',
      pesticides: 'کیڑے مار دوا',
      tools: 'اوزار',
      equipment: 'سامان',
      feed: 'خوراک',
      medication: 'دوا',
      fuel: 'ایندھن',
      other: 'دیگر',
      selectFarmFirst: 'پہلے فارم منتخب کریں',
      noItemsMatchFilters: 'آپ کی تلاش یا فلٹرز سے کوئی آئٹم میل نہیں کھاتا',
      otherCustom: 'دیگر (کسٹم)',
      customCategory: 'کسٹم زمرہ',
      customUnit: 'کسٹم یونٹ',
      enterMinQuantity: 'کم سے کم مقدار درج کریں',
      selectExpiryDate: 'ختم ہونے کی تاریخ منتخب کریں',
      selectPurchaseDate: 'خریداری کی تاریخ منتخب کریں',
      enterNotes: 'نوٹس درج کریں',
      enterPrice: 'قیمت درج کریں',
      fuelPrice: 'ایندھن کی قیمت',
      enterFuelPrice: 'ایندھن کی قیمت درج کریں',
      maintenancePrice: 'دیکھ بھال کی قیمت',
      enterMaintenancePrice: 'دیکھ بھال کی قیمت درج کریں',
      abbreviation: 'مخفف',
      categoryName: 'زمرہ کا نام',
      enterCategoryName: 'زمرہ کا نام درج کریں',
      categoryDescription: 'زمرہ کی تفصیل',
      enterCategoryDescription: 'زمرہ کی تفصیل درج کریں',
      unitName: 'یونٹ کا نام',
      enterUnitName: 'یونٹ کا نام درج کریں',
      enterAbbreviation: 'مخفف درج کریں',
      inventoryCategories: 'انوینٹری کے زمرے',
      measurementUnits: 'پیمائش کے یونٹس',
      addCategory: 'زمرہ شامل کریں',
      addUnit: 'یونٹ شامل کریں',
      noCategoriesFound: 'کوئی زمرے نہیں ملے',
      noUnitsFound: 'کوئی یونٹس نہیں ملے',
      deleteCategory: 'زمرہ حذف کریں',
      deleteUnit: 'یونٹ حذف کریں',
      deleteCategoryConfirm: 'کیا آپ واقعی اس زمرے کو حذف کرنا چاہتے ہیں؟',
      deleteUnitConfirm: 'کیا آپ واقعی اس یونٹ کو حذف کرنا چاہتے ہیں؟',
      categorySaved: 'زمرہ کامیابی سے محفوظ ہو گیا',
      unitSaved: 'یونٹ کامیابی سے محفوظ ہو گیا',
      categoryDeleted: 'زمرہ کامیابی سے حذف کر دیا گیا',
      unitDeleted: 'یونٹ کامیابی سے حذف کر دیا گیا',
      abbreviationRequired: 'مخفف ضروری ہے',
      unnamedItem: 'بے نام آئٹم',
      // New inventory filter translations
      filterByCategory: 'زمرے کے ذریعے فلٹر کریں',
      filterByStatus: 'حالت کے ذریعے فلٹر کریں',
      filterByExpiry: 'ختم ہونے کے ذریعے فلٹر کریں',
      filterByStock: 'اسٹاک لیول کے ذریعے فلٹر کریں',
      filterBySupplier: 'سپلائر کے ذریعے فلٹر کریں',
      filterByLocation: 'مقام کے ذریعے فلٹر کریں',
      filterByDateAdded: 'شامل کرنے کی تاریخ کے ذریعے فلٹر کریں',
      filterByPriceRange: 'قیمت کی رینج کے ذریعے فلٹر کریں',
      sortByName: 'نام کے ذریعے ترتیب دیں',
      sortByQuantity: 'مقدار کے ذریعے ترتیب دیں',
      sortByCategory: 'زمرے کے ذریعے ترتیب دیں',
      sortByExpiry: 'ختم ہونے کی تاریخ کے ذریعے ترتیب دیں',
      sortByDateAdded: 'شامل کرنے کی تاریخ کے ذریعے ترتیب دیں',
      sortByPrice: 'قیمت کے ذریعے ترتیب دیں',
      sortBySupplier: 'سپلائر کے ذریعے ترتیب دیں',
      sortByLocation: 'مقام کے ذریعے ترتیب دیں',
      showInStock: 'اسٹاک میں دکھائیں',
      showLowStock: 'کم اسٹاک دکھائیں',
      showOutOfStock: 'اسٹاک ختم دکھائیں',
      showExpired: 'ختم ہو چکے دکھائیں',
      showExpiringSoon: 'جلد ختم ہونے والے دکھائیں',
      showAll: 'تمام آئٹمز دکھائیں',
      priceRange: 'قیمت کی رینج',
      minPrice: 'کم سے کم قیمت',
      maxPrice: 'زیادہ سے زیادہ قیمت',
      stockLevel: 'اسٹاک لیول',
      inStock: 'اسٹاک میں',
      outOfStock: 'اسٹاک ختم',
      criticalStock: 'انتہائی کم اسٹاک',
      expiryStatus: 'ختم ہونے کی حالت',
      fresh: 'تازہ',
      nearExpiry: 'ختم ہونے کے قریب',
      expired: 'ختم ہو گیا',
      noExpiry: 'کوئی ختم ہونے کی تاریخ نہیں',
      dateAdded: 'شامل کرنے کی تاریخ',
      lastWeek: 'پچھلا ہفتہ',
      lastMonth: 'پچھلا مہینہ',
      last3Months: 'پچھلے 3 مہینے',
      lastYear: 'پچھلا سال',
      customDateRange: 'کسٹم تاریخ کی رینج',
      farmInventory: 'فارم انوینٹری',
      inventoryOverview: 'انوینٹری کا جائزہ',
      inventoryStats: 'انوینٹری کے اعداد و شمار',
      totalValue: 'کل قیمت',
      averagePrice: 'اوسط قیمت',
      mostExpensive: 'سب سے مہنگا',
      leastExpensive: 'سب سے سستا',
      topCategories: 'اعلیٰ زمرے',
      recentlyAdded: 'حال ہی میں شامل کیے گئے',
      recentlyUpdated: 'حال ہی میں اپڈیٹ کیے گئے',
      quickStats: 'فوری اعداد و شمار',
      inventoryHealth: 'انوینٹری کی صحت',
      stockAlerts: 'اسٹاک کی انتباہات',
      expiryAlerts: 'ختم ہونے کی انتباہات',
      status: {
        inStock: 'اسٹاک میں',
        lowStock: 'کم اسٹاک',
        expired: 'ختم ہو گیا',
        expiringSoon: 'جلد ختم ہونے والا',
      },
      categories: {
        seeds: 'بیج',
        fertilizers: 'کھاد',
        pesticides: 'کیڑے مار دوا',
        tools: 'اوزار',
        equipment: 'سامان',
        feed: 'خوراک',
        medication: 'دوا',
        vaccination: 'ویکسینیشن',
        fuel: 'ایندھن',
        other: 'دیگر',
      },
    },
    requests: {
      title: 'درخواستیں',
      createRequest: 'درخواست بنائیں',
      editRequest: 'درخواست میں ترمیم',
      deleteRequest: 'درخواست حذف کریں',
      requestDetails: 'درخواست کی تفصیلات',
      searchRequests: 'درخواستیں تلاش کریں...',
      noRequests: 'کوئی درخواست نہیں ملی',
      noRequestsFound: 'آپ کی تلاش سے کوئی درخواست میل نہیں کھاتی',
      createFirstRequest: 'اپنی پہلی درخواست بنائیں',
      tryDifferentSearch: 'مختلف تلاش یا فلٹر آزمائیں',
      requestDeleted: 'درخواست کامیابی سے حذف کر دی گئی',
      requestCreated: 'درخواست کامیابی سے بنائی گئی',
      requestUpdated: 'درخواست کامیابی سے اپڈیٹ ہو گئی',
      deleteConfirm: 'کیا آپ واقعی اس درخواست کو حذف کرنا چاہتے ہیں؟',
      loadError: 'درخواستیں لوڈ کرنے میں ناکامی',
      deleteError: 'درخواست حذف کرنے میں ناکامی',
      createError: 'درخواست بنانے میں ناکامی',
      updateError: 'درخواست اپڈیٹ کرنے میں ناکامی',
      itemName: 'آئٹم کا نام',
      quantity: 'مقدار',
      unit: 'یونٹ',
      reason: 'وجہ',
      notes: 'نوٹس',
      requestedBy: 'درخواست کنندہ',
      requestedAt: 'درخواست کی تاریخ',
      approvedBy: 'منظور کنندہ',
      approvedAt: 'منظوری کی تاریخ',
      rejectedBy: 'مسترد کنندہ',
      rejectedAt: 'مسترد کرنے کی تاریخ',
      rejectionReason: 'مسترد کرنے کی وجہ',
      status: 'حالت',
      pending: 'زیر التواء',
      approved: 'منظور شدہ',
      rejected: 'مسترد شدہ',
      approve: 'منظور کریں',
      reject: 'مسترد کریں',
      approveConfirm: 'کیا آپ واقعی اس درخواست کو منظور کرنا چاہتے ہیں؟',
      rejectConfirm: 'کیا آپ واقعی اس درخواست کو مسترد کرنا چاہتے ہیں؟',
      approveSuccess: 'درخواست کامیابی سے منظور ہو گئی',
      rejectSuccess: 'درخواست کامیابی سے مسترد ہو گئی',
      approveError: 'درخواست منظور کرنے میں ناکامی',
      rejectError: 'درخواست مسترد کرنے میں ناکامی',
      enterRejectionReason: 'مسترد کرنے کی وجہ درج کریں',
      requestType: 'درخواست کی قسم',
      inventory: 'انوینٹری',
      existing: 'موجودہ آئٹم',
      newItem: 'نیا آئٹم',
      newItemRequest: 'نئے آئٹم کی درخواست',
      existingItemRequest: 'موجودہ آئٹم کی درخواست',
      selectItem: 'آئٹم منتخب کریں',
      noItemsAvailable: 'کوئی آئٹم دستیاب نہیں',
      caretakerRequests: 'نگہداشت کنندہ کی درخواستیں',
      inventoryRequests: 'انوینٹری کی درخواستیں',
      noCaretakerRequests: 'کوئی نگہداشت کنندہ کی درخواست نہیں',
      noInventoryRequests: 'کوئی انوینٹری کی درخواست نہیں',
      createFirstInventoryRequest: 'اپنی پہلی انوینٹری کی درخواست بنائیں',
      waitingForCaretakerRequests: 'نگہداشت کنندہ کی درخواستوں کا انتظار',
      requestNewInventoryItem: 'نئے انوینٹری آئٹم کی درخواست',
      submitRequestDescription: 'نئے انوینٹری آئٹم کے لیے درخواست جمع کریں',
      reasonForRequest: 'درخواست کی وجہ',
      explainWhyNeeded: 'وضاحت کریں کہ یہ آئٹم کیوں ضروری ہے',
      requestFor: 'کے لیے درخواست',
      submitRequest: 'درخواست جمع کریں',
      fillAllFields: 'براہ کرم تمام ضروری فیلڈز بھریں',
      invalidQuantity: 'براہ کرم درست مقدار درج کریں',
      enterReason: 'درخواست کی وجہ درج کریں',
      requestNote: 'آپ کی درخواست منظوری کے لیے ایڈمن کو بھیجی جائے گی',
      manualItemEntry: 'آئٹم کی تفصیلات دستی طور پر درج کریں',
      transferRequested: 'منتقلی کی درخواست',
      transferApproved: 'منتقلی منظور',
      transferRejected: 'منتقلی مسترد',
      transferReceived: 'منتقلی موصول',
      machinery: 'مشینری',
      machineryName: 'مشینری کا نام',
      machineryType: 'مشینری کی قسم',
      machineryRequests: 'مشینری کی درخواستیں',
      noMachineryRequests: 'کوئی مشینری کی درخواست نہیں',
      createFirstMachineryRequest: 'اپنی پہلی مشینری کی درخواست بنائیں',
      requestMachinery: 'مشینری کی درخواست',
      machineryRequestSubmitted: 'مشینری کی درخواست کامیابی سے جمع کر دی گئی',
      failedToSubmitMachineryRequest: 'مشینری کی درخواست جمع کرنے میں ناکامی',
      requestMachineryUse: 'مشینری کے استعمال کی درخواست',
      requestMachineryFromCard: 'اس مشینری کی درخواست کریں',
      reasonRequired: 'وجہ ضروری ہے',
      reasonForTransfer: 'منتقلی کی وجہ',
      explainWhyTransferNeeded: 'وضاحت کریں کہ یہ منتقلی کیوں ضروری ہے',
      submitTransferRequest: 'منتقلی کی درخواست جمع کریں',
      submitTransferRequestDescription: 'اس آئٹم کی منتقلی کے لیے درخواست جمع کریں',
      transferRequestCreated: 'منتقلی کی درخواست کامیابی سے بنائی گئی',
      transferRequestNote: 'آپ کی منتقلی کی درخواست ایڈمن کی جانب سے جانچی جائے گی',
      requestTransfer: 'منتقلی کی درخواست',
      machineryRequestNote: 'آپ کی مشینری کی درخواست متعلقہ اتھارٹی کی جانب سے جانچی جائے گی',
      submitMachineryRequestDescription: 'مشینری کے استعمال یا دیکھ بھال کے لیے درخواست جمع کریں',
      ownersCannotCreateRequests: 'مالکان درخواست نہیں بنا سکتے',
      ownersCanOnlyApproveReject: 'مالک کے طور پر، آپ صرف ایڈمنز کی درخواستوں کو منظور یا مسترد کر سکتے ہیں',
      machineryRequest: 'مشینری کی درخواست',
      machineryRequestDetails: 'مشینری کی درخواست کی تفصیلات',
      approvalInformation: 'منظوری کی معلومات',
      rejectionInformation: 'مسترد کرنے کی معلومات',
      reasonForRejection: 'مسترد کرنے کی وجہ',
      confirmRejection: 'مسترد کرنے کی تصدیق',
      requestNotFound: 'درخواست نہیں ملی',
    },
    farm: {
      title: 'فارمز',
      assignedFarms: 'تفویض شدہ فارمز',
      addFarm: 'فارم شامل کریں',
      editFarm: 'فارم میں ترمیم',
      deleteFarm: 'فارم حذف کریں',
      farmDetails: 'فارم کی تفصیلات',
      searchFarms: 'فارمز تلاش کریں...',
      noFarms: 'کوئی فارم نہیں ملا',
      noFarmsFound: 'آپ کی تلاش سے کوئی فارم میل نہیں کھاتا',
      addFirstFarm: 'اپنا پہلا فارم شامل کریں',
      tryDifferentSearch: 'مختلف تلاش یا فلٹر آزمائیں',
      farmDeleted: 'فارم کامیابی سے حذف کر دیا گیا',
      farmAdded: 'فارم کامیابی سے شامل کر دیا گیا',
      farmUpdated: 'فارم کامیابی سے اپڈیٹ ہو گیا',
      deleteConfirm: 'کیا آپ واقعی اس فارم کو حذف کرنا چاہتے ہیں؟',
      loadError: 'فارمز لوڈ کرنے میں ناکامی',
      deleteError: 'فارم حذف کرنے میں ناکامی',
      addError: 'فارم شامل کرنے میں ناکامی',
      updateError: 'فارم اپڈیٹ کرنے میں ناکامی',
      farmName: 'فارم کا نام',
      location: 'مقام',
      locationNotSet: 'مقام مقرر نہیں',
      size: 'سائز',
      owner: 'مالک',
      admins: 'ایڈمنز',
      caretakers: 'نگہداشت کنندگان',
      inventory: 'انوینٹری',
      requests: 'درخواستیں',
      notes: 'نوٹس',
      reports: 'رپورٹس',
      activities: 'سرگرمیاں',
      settings: 'سیٹنگز',
      noFarmSelected: 'کوئی فارم منتخب نہیں',
      selectFarmFirst: 'پہلے فارم منتخب کریں',
      selectFarm: 'فارم منتخب کریں',
      changeFarm: 'فارم تبدیل کریں',
      // New farm setup translations
      createFarm: 'فارم بنائیں',
      farmSetup: 'فارم سیٹ اپ',
      setupYourFarm: 'اپنا فارم سیٹ اپ کریں',
      farmCreatedSuccessfully: 'فارم کامیابی سے بنایا گیا',
      redirectingToDashboard: 'ڈیش بورڈ کی طرف بھیجا جا رہا ہے...',
      enterFarmName: 'فارم کا نام درج کریں',
      enterFarmLocation: 'فارم کا مقام درج کریں',
      enterFarmSize: 'فارم کا سائز درج کریں (ایکڑ میں)',
      farmNameRequired: 'فارم کا نام ضروری ہے',
      farmLocationRequired: 'فارم کا مقام ضروری ہے',
      farmSizeRequired: 'فارم کا سائز ضروری ہے',
      invalidFarmSize: 'براہ کرم درست فارم سائز درج کریں',
      farmInformation: 'فارم کی معلومات',
      basicDetails: 'بنیادی تفصیلات',
      farmDescription: 'فارم کی تفصیل',
      enterFarmDescription: 'فارم کی تفصیل درج کریں (اختیاری)',
      farmType: 'فارم کی قسم',
      selectFarmType: 'فارم کی قسم منتخب کریں',
      cropFarm: 'فصل فارم',
      livestockFarm: 'مویشی فارم',
      mixedFarm: 'مخلوط فارم',
      dairyFarm: 'ڈیری فارم',
      poultyFarm: 'مرغی فارم',
      fishFarm: 'مچھلی فارم',
      organicFarm: 'آرگینک فارم',
      acres: 'ایکڑ',
      hectares: 'ہیکٹر',
      squareFeet: 'مربع فٹ',
      squareMeters: 'مربع میٹر',
      sizeUnit: 'سائز یونٹ',
      selectSizeUnit: 'سائز یونٹ منتخب کریں',
      establishedYear: 'قائم کرنے کا سال',
      selectEstablishedYear: 'قائم کرنے کا سال منتخب کریں',
      farmImage: 'فارم کی تصویر',
      addFarmImage: 'فارم کی تصویر شامل کریں (اختیاری)',
      farmContact: 'فارم رابطہ',
      farmPhone: 'فارم فون',
      farmEmail: 'فارم ای میل',
      enterFarmPhone: 'فارم فون درج کریں (اختیاری)',
      enterFarmEmail: 'فارم ای میل درج کریں (اختیاری)',
      farmAddress: 'فارم کا پتہ',
      enterFarmAddress: 'فارم کا مکمل پتہ درج کریں',
      farmAddressRequired: 'فارم کا پتہ ضروری ہے',
      coordinates: 'کوآرڈینیٹس',
      latitude: 'عرض البلد',
      longitude: 'طول البلد',
      enterLatitude: 'عرض البلد درج کریں (اختیاری)',
      enterLongitude: 'طول البلد درج کریں (اختیاری)',
      useCurrentLocation: 'موجودہ مقام استعمال کریں',
      gettingLocation: 'موجودہ مقام حاصل کر رہے ہیں...',
      locationPermissionDenied: 'مقام کی اجازت مسترد',
      failedToGetLocation: 'موجودہ مقام حاصل کرنے میں ناکامی',
      farmCreation: 'فارم بنانا',
      creatingFarm: 'فارم بنایا جا رہا ہے...',
      farmCreationFailed: 'فارم بنانے میں ناکامی',
      myFarms: 'میرے فارمز',
      assignedFarms: 'تفویض شدہ فارمز',
      ownedFarms: 'ملکیتی فارمز',
      managedFarms: 'منظم شدہ فارمز',
      totalFarms: 'کل فارمز',
      activeFarms: 'فعال فارمز',
      farmOverview: 'فارم کا جائزہ',
      farmStats: 'فارم کے اعداد و شمار',
      farmActivities: 'فارم کی سرگرمیاں',
      recentFarmActivity: 'حالیہ فارم سرگرمی',
      farmInventoryCount: 'فارم انوینٹری کی تعداد',
      farmMachineryCount: 'فارم مشینری کی تعداد',
      farmUsersCount: 'فارم صارفین کی تعداد',
      farmRequestsCount: 'فارم درخواستوں کی تعداد',
      farmNotesCount: 'فارم نوٹس کی تعداد',
      basicInformation: 'بنیادی معلومات',
      loadingFarmDetails: 'فارم کی تفصیلات لوڈ ہو رہی ہیں...',
      assignCaretakers: 'نگہداشت کنندگان تفویض کریں',
      noCaretakersAvailable: 'کوئی نگہداشت کنندگان دستیاب نہیں۔ پہلے نگہداشت کنندگان کے اکاؤنٹس بنائیں۔',
      updateFarm: 'فارم اپڈیٹ کریں',
    },
    users: {
      title: 'صارفین',
      addUser: 'صارف شامل کریں',
      editUser: 'صارف میں ترمیم',
      deleteUser: 'صارف حذف کریں',
      userDetails: 'صارف کی تفصیلات',
      searchUsers: 'صارفین تلاش کریں...',
      noUsers: 'کوئی صارف نہیں ملا',
      noUsersFound: 'آپ کی تلاش سے کوئی صارف میل نہیں کھاتا',
      addFirstUser: 'اپنا پہلا صارف شامل کریں',
      tryDifferentSearch: 'مختلف تلاش یا فلٹر آزمائیں',
      userDeleted: 'صارف کامیابی سے حذف کر دیا گیا',
      userAdded: 'صارف کامیابی سے شامل کر دیا گیا',
      userUpdated: 'صارف کامیابی سے اپڈیٹ ہو گیا',
      deleteConfirm: 'کیا آپ واقعی اس صارف کو حذف کرنا چاہتے ہیں؟',
      loadError: 'صارفین لوڈ کرنے میں ناکامی',
      deleteError: 'صارف حذف کرنے میں ناکامی',
      addError: 'صارف شامل کرنے میں ناکامی',
      updateError: 'صارف اپڈیٹ کرنے میں ناکامی',
      userName: 'صارف کا نام',
      email: 'ای میل',
      phone: 'فون',
      role: 'کردار',
      status: 'حالت',
      owner: 'مالک',
      admin: 'ایڈمن',
      caretaker: 'نگہداشت کنندہ',
      active: 'فعال',
      inactive: 'غیر فعال',
      suspended: 'معطل',
      lastLogin: 'آخری لاگ ان',
      createdAt: 'بنانے کی تاریخ',
      updatedAt: 'اپڈیٹ کی تاریخ',
      assignedFarms: 'تفویض شدہ فارمز',
      noAssignedFarms: 'کوئی تفویض شدہ فارم نہیں',
      assignFarm: 'فارم تفویض کریں',
      unassignFarm: 'فارم کی تفویض ختم کریں',
      assignFarmSuccess: 'فارم کامیابی سے تفویض ہو گیا',
      unassignFarmSuccess: 'فارم کی تفویض کامیابی سے ختم ہو گئی',
      assignFarmError: 'فارم تفویض کرنے میں ناکامی',
      unassignFarmError: 'فارم کی تفویض ختم کرنے میں ناکامی',
      adminCanOnlyAddCaretakers: 'ایڈمن کے طور پر، آپ صرف نگہداشت کنندگان کو شامل کر سکتے ہیں',
      allUsers: 'تمام صارفین',
      farmUsers: 'فارم صارفین',
      noUsersFoundForFarm: 'اس فارم کے لیے کوئی صارف نہیں ملا',
      adminViewOnly: 'آپ ایڈمن کے طور پر اس فارم کو دیکھ رہے ہیں۔ آپ فارم کی معلومات میں ترمیم یا حذف نہیں کر سکتے۔',
      caretakerViewOnly: 'آپ نگہداشت کنندہ کے طور پر اس فارم کو دیکھ رہے ہیں۔ آپ فارم کی معلومات میں ترمیم یا حذف نہیں کر سکتے۔',
      noUsers: 'اس فارم کو کوئی صارف تفویض نہیں کیا گیا',
      deleteUserConfirmation: 'کیا آپ واقعی {userName} کو حذف کرنا چاہتے ہیں؟ یہ عمل واپس نہیں ہو سکتا۔',
      // Add User form specific translations
      fullName: 'مکمل نام',
      enterFullName: 'مکمل نام درج کریں',
      emailAddress: 'ای میل ایڈریس',
      enterEmailAddress: 'ای میل ایڈریس درج کریں',
      gender: 'جنس',
      male: 'مرد',
      female: 'عورت',
      other: 'دیگر',
      dateOfBirth: 'تاریخ پیدائش',
      dateFormat: 'DD/MM/YYYY',
      cnic: 'شناختی کارڈ',
      cnicFormat: 'XXXXX-XXXXXXX-X',
      contactInformation: 'رابطے کی معلومات',
      phoneNumber: 'فون نمبر',
      enterPhoneNumber: 'فون نمبر درج کریں',
      address: 'پتہ',
      enterCompleteAddress: 'مکمل پتہ درج کریں',
      profileImage: 'پروفائل تصویر',
      uploadImage: 'تصویر اپ لوڈ کریں',
      takePhoto: 'تصویر لیں',
      removeImage: 'ہٹائیں',
      bio: 'تعارف',
      enterBio: 'تعارف درج کریں (اختیاری)',
      security: 'سیکیورٹی',
      password: 'پاس ورڈ',
      enterPassword: 'پاس ورڈ درج کریں (کم سے کم 6 حروف)',
      confirmPassword: 'پاس ورڈ کی تصدیق کریں',
      confirmPasswordPlaceholder: 'پاس ورڈ کی تصدیق کریں',
      farmAssignment: 'فارم تفویض',
      assignToFarms: 'فارمز میں تفویض کریں',
      noFarmsAvailable: 'کوئی فارم دستیاب نہیں۔ براہ کرم اپنے ایڈمنسٹریٹر سے رابطہ کریں۔',
      createUser: 'صارف بنائیں',
      capturePhoto: 'تصویر کھینچیں',
      flipCamera: 'کیمرہ پلٹیں',
      capture: 'کھینچیں',
      // Validation messages
      nameRequired: 'نام ضروری ہے',
      emailRequired: 'ای میل ضروری ہے',
      emailInvalid: 'ای میل غلط ہے',
      passwordRequired: 'پاس ورڈ ضروری ہے',
      passwordTooShort: 'پاس ورڈ کم سے کم 6 حروف کا ہونا چاہیے',
      confirmPasswordRequired: 'براہ کرم اپنے پاس ورڈ کی تصدیق کریں',
      passwordsDoNotMatch: 'پاس ورڈ میل نہیں کھاتے',
      farmSelectionRequired: 'کم سے کم ایک فارم منتخب کرنا ضروری ہے',
      genderRequired: 'جنس ضروری ہے',
      dateOfBirthRequired: 'تاریخ پیدائش ضروری ہے',
      dateFormatInvalid: 'تاریخ DD/MM/YYYY فارمیٹ میں ہونی چاہیے',
      cnicRequired: 'شناختی کارڈ ضروری ہے',
      cnicFormatInvalid: 'شناختی کارڈ XXXXX-XXXXXXX-X فارمیٹ میں ہونا چاہیے',
      userCreatedSuccessfully: 'صارف کامیابی سے بنایا گیا',
      failedToCreateUser: 'صارف بنانے میں ناکامی۔ براہ کرم دوبارہ کوشش کریں۔',
      permissionDeniedCamera: 'اجازت مسترد۔ معذرت، ہمیں کام کرنے کے لیے کیمرہ رول کی اجازات درکار ہیں!',
      permissionDeniedCameraCapture: 'اجازت مسترد۔ معذرت، ہمیں کام کرنے کے لیے کیمرہ کی اجازات درکار ہیں!',
    },
    notes: {
      title: 'نوٹس',
      addNote: 'نوٹ شامل کریں',
      editNote: 'نوٹ میں ترمیم',
      deleteNote: 'نوٹ حذف کریں',
      noteDetails: 'نوٹ کی تفصیلات',
      searchNotes: 'نوٹس تلاش کریں...',
      noNotes: 'کوئی نوٹ نہیں ملا',
      noNotesFound: 'آپ کی تلاش سے کوئی نوٹ میل نہیں کھاتا',
      addFirstNote: 'اپنا پہلا نوٹ شامل کریں',
      tryDifferentSearch: 'مختلف تلاش یا فلٹر آزمائیں',
      noteDeleted: 'نوٹ کامیابی سے حذف کر دیا گیا',
      noteAdded: 'نوٹ کامیابی سے شامل کر دیا گیا',
      noteUpdated: 'نوٹ کامیابی سے اپڈیٹ ہو گیا',
      deleteConfirm: 'کیا آپ واقعی اس نوٹ کو حذف کرنا چاہتے ہیں؟',
      loadError: 'نوٹس لوڈ کرنے میں ناکامی',
      deleteError: 'نوٹ حذف کرنے میں ناکامی',
      addError: 'نوٹ شامل کرنے میں ناکامی',
      updateError: 'نوٹ اپڈیٹ کرنے میں ناکامی',
      noteTitle: 'نوٹ کا عنوان',
      content: 'مواد',
      createdBy: 'بنانے والا',
      createdAt: 'بنانے کی تاریخ',
      updatedAt: 'اپڈیٹ کی تاریخ',
      category: 'زمرہ',
      priority: 'ترجیح',
      status: 'حالت',
      farm: 'فارم',
      assignedTo: 'تفویض شدہ',
      dueDate: 'مقررہ تاریخ',
      completedAt: 'مکمل کرنے کی تاریخ',
      completedBy: 'مکمل کنندہ',
      attachments: 'منسلکات',
      addAttachment: 'منسلک شامل کریں',
      removeAttachment: 'منسلک ہٹائیں',
      voiceNote: 'صوتی نوٹ',
      recordVoiceNote: 'صوتی نوٹ ریکارڈ کریں',
      stopRecording: 'ریکارڈنگ بند کریں',
      titleRequired: 'عنوان ضروری ہے',
      contentRequired: 'مواد ضروری ہے',
      missingData: 'ضروری ڈیٹا غائب ہے',
      idRequired: 'نوٹ آئی ڈی ضروری ہے',
      notFound: 'نوٹ نہیں ملا',
      cannotEditOthers: 'آپ دوسروں کے بنائے گئے نوٹس میں ترمیم نہیں کر سکتے',
      imagePickError: 'تصویر منتخب کرنے میں ناکامی',
      cameraError: 'کیمرہ استعمال کرنے میں ناکامی',
      cameraNotAvailableWeb: 'ویب پر کیمرہ دستیاب نہیں',
      cameraPermissionRequired: 'کیمرہ کی اجازت ضروری ہے',
      audioRecordingNotAvailableWeb: 'ویب پر آڈیو ریکارڈنگ دستیاب نہیں',
      microphonePermissionRequired: 'مائیکروفون کی اجازت ضروری ہے',
      recordingStartError: 'ریکارڈنگ شروع کرنے میں ناکامی',
      recordingStopError: 'ریکارڈنگ بند کرنے میں ناکامی',
      audioPlayError: 'آڈیو چلانے میں ناکامی',
      enterNoteTitle: 'نوٹ کا عنوان درج کریں',
      enterNoteContent: 'نوٹ کا مواد درج کریں',
      updateNoteDescription: 'اپنے نوٹ کی تفصیلات اپڈیٹ کریں',
      noFarmSelected: 'کوئی فارم منتخب نہیں',
      selectFarmFirst: 'پہلے فارم منتخب کریں',
      categories: {
        general: 'عام',
        task: 'کام',
        reminder: 'یاد دہانی',
        meeting: 'میٹنگ',
        other: 'دیگر',
      },
      priorities: {
        low: 'کم',
        medium: 'درمیانی',
        high: 'زیادہ',
        urgent: 'فوری',
      },
      statuses: {
        active: 'فعال',
        completed: 'مکمل',
        archived: 'محفوظ شدہ',
      },
      noteAdded: 'نوٹ شامل کیا گیا',
      noteUpdated: 'نوٹ اپڈیٹ ہو گیا',
      filters: {
        all: 'تمام',
        withAudio: 'آڈیو کے ساتھ',
        withImages: 'تصاویر کے ساتھ',
        textOnly: 'صرف متن',
        multimedia: 'ملٹی میڈیا',
        audio: 'آڈیو',
        images: 'تصاویر',
        text: 'متن',
      },
    },
    reports: {
      title: 'رپورٹس',
      generateReport: 'رپورٹ بنائیں',
      viewReport: 'رپورٹ دیکھیں',
      deleteReport: 'رپورٹ حذف کریں',
      reportDetails: 'رپورٹ کی تفصیلات',
      searchReports: 'رپورٹس تلاش کریں...',
      noReports: 'کوئی رپورٹ نہیں ملی',
      noReportsFound: 'آپ کی تلاش سے کوئی رپورٹ میل نہیں کھاتی',
      generateFirstReport: 'اپنی پہلی رپورٹ بنائیں',
      tryDifferentSearch: 'مختلف تلاش یا فلٹر آزمائیں',
      reportDeleted: 'رپورٹ کامیابی سے حذف کر دی گئی',
      reportGenerated: 'رپورٹ کامیابی سے بنائی گئی',
      deleteConfirm: 'کیا آپ واقعی اس رپورٹ کو حذف کرنا چاہتے ہیں؟',
      loadError: 'رپورٹس لوڈ کرنے میں ناکامی',
      deleteError: 'رپورٹ حذف کرنے میں ناکامی',
      generateError: 'رپورٹ بنانے میں ناکامی',
      reportType: 'رپورٹ کی قسم',
      dateRange: 'تاریخ کی رینج',
      startDate: 'شروع کی تاریخ',
      endDate: 'اختتامی تاریخ',
      farm: 'فارم',
      generatedBy: 'بنانے والا',
      generatedAt: 'بنانے کی تاریخ',
      downloadReport: 'رپورٹ ڈاؤن لوڈ کریں',
      printReport: 'رپورٹ پرنٹ کریں',
      shareReport: 'رپورٹ شیئر کریں',
      scheduleReport: 'رپورٹ شیڈول کریں',
      frequency: 'تعدد',
      recipients: 'وصول کنندگان',
      addRecipient: 'وصول کنندہ شامل کریں',
      removeRecipient: 'وصول کنندہ ہٹائیں',
      scheduledReports: 'شیڈول شدہ رپورٹس',
      noScheduledReports: 'کوئی شیڈول شدہ رپورٹ نہیں',
      scheduleFirstReport: 'اپنی پہلی رپورٹ شیڈول کریں',
      reportScheduled: 'رپورٹ کامیابی سے شیڈول ہو گئی',
      reportUnscheduled: 'رپورٹ کا شیڈول کامیابی سے ختم ہو گیا',
      scheduleError: 'رپورٹ شیڈول کرنے میں ناکامی',
      unscheduleError: 'رپورٹ کا شیڈول ختم کرنے میں ناکامی',
      types: {
        inventory: 'انوینٹری رپورٹ',
        requests: 'درخواستوں کی رپورٹ',
        activities: 'سرگرمیوں کی رپورٹ',
        users: 'صارفین کی رپورٹ',
        farms: 'فارمز کی رپورٹ',
        notes: 'نوٹس کی رپورٹ',
        custom: 'کسٹم رپورٹ',
      },
      frequencies: {
        daily: 'روزانہ',
        weekly: 'ہفتہ وار',
        monthly: 'ماہانہ',
        quarterly: 'سہ ماہی',
        yearly: 'سالانہ',
      },
    },
    activities: {
      title: 'سرگرمیاں',
      viewActivity: 'سرگرمی دیکھیں',
      searchActivities: 'سرگرمیاں تلاش کریں...',
      noActivities: 'کوئی سرگرمی نہیں ملی',
      noActivitiesFound: 'آپ کی تلاش سے کوئی سرگرمی میل نہیں کھاتی',
      tryDifferentSearch: 'مختلف تلاش یا فلٹر آزمائیں',
      loadError: 'سرگرمیاں لوڈ کرنے میں ناکامی',
      activityType: 'سرگرمی کی قسم',
      user: 'صارف',
      farm: 'فارم',
      date: 'تاریخ',
      time: 'وقت',
      details: 'تفصیلات',
      item: 'آئٹم',
      quantity: 'مقدار',
      unit: 'یونٹ',
      types: {
        login: 'لاگ ان',
        logout: 'لاگ آؤٹ',
        inventory_created: 'انوینٹری بنائی گئی',
        inventory_updated: 'انوینٹری اپڈیٹ ہوئی',
        inventory_deleted: 'انوینٹری حذف ہوئی',
        request_created: 'درخواست بنائی گئی',
        request_updated: 'درخواست اپڈیٹ ہوئی',
        request_deleted: 'درخواست حذف ہوئی',
        request_approved: 'درخواست منظور ہوئی',
        request_rejected: 'درخواست مسترد ہوئی',
        note_created: 'نوٹ بنایا گیا',
        note_updated: 'نوٹ اپڈیٹ ہوا',
        note_deleted: 'نوٹ حذف ہوا',
        user_created: 'صارف بنایا گیا',
        user_updated: 'صارف اپڈیٹ ہوا',
        user_deleted: 'صارف حذف ہوا',
        farm_created: 'فارم بنایا گیا',
        farm_updated: 'فارم اپڈیٹ ہوا',
        farm_deleted: 'فارم حذف ہوا',
        report_generated: 'رپورٹ بنائی گئی',
        report_deleted: 'رپورٹ حذف ہوئی',
        report_scheduled: 'رپورٹ شیڈول ہوئی',
        report_unscheduled: 'رپورٹ کا شیڈول ختم ہوا',
        transfer_approved: 'منتقلی منظور ہوئی',
        transfer_rejected: 'منتقلی مسترد ہوئی',
        transfer_received: 'منتقلی موصول ہوئی',
      },
    },
    settings: {
      title: 'سیٹنگز',
      account: 'اکاؤنٹ',
      profile: 'پروفائل',
      security: 'سیکیورٹی',
      notifications: 'اطلاعات',
      language: 'زبان',
      theme: 'تھیم',
      about: 'کے بارے میں',
      help: 'مدد',
      logout: 'لاگ آؤٹ',
      accountSettings: 'اکاؤنٹ کی سیٹنگز',
      profileSettings: 'پروفائل کی سیٹنگز',
      securitySettings: 'سیکیورٹی کی سیٹنگز',
      notificationSettings: 'اطلاعات کی سیٹنگز',
      languageSettings: 'زبان کی سیٹنگز',
      themeSettings: 'تھیم کی سیٹنگز',
      aboutApp: 'ایپ کے بارے میں',
      helpCenter: 'مدد کا مرکز',
      logoutConfirm: 'کیا آپ واقعی لاگ آؤٹ کرنا چاہتے ہیں؟',
      name: 'نام',
      email: 'ای میل',
      phone: 'فون',
      role: 'کردار',
      changePassword: 'پاس ورڈ تبدیل کریں',
      currentPassword: 'موجودہ پاس ورڈ',
      newPassword: 'نیا پاس ورڈ',
      confirmNewPassword: 'نیا پاس ورڈ کی تصدیق کریں',
      passwordChanged: 'پاس ورڈ کامیابی سے تبدیل ہو گیا',
      passwordChangeError: 'پاس ورڈ تبدیل کرنے میں ناکامی',
      profileUpdated: 'پروفائل کامیابی سے اپڈیٹ ہو گیا',
      profileUpdateError: 'پروفائل اپڈیٹ کرنے میں ناکامی',
      profileDetails: 'پروفائل کی تفصیلات',
      personalInformation: 'ذاتی معلومات',
      viewDetails: 'تفصیلات دیکھیں',
      enableNotifications: 'اطلاعات فعال کریں',
      emailNotifications: 'ای میل اطلاعات',
      pushNotifications: 'پش اطلاعات',
      smsNotifications: 'ایس ایم ایس اطلاعات',
      notificationUpdated: 'اطلاعات کی سیٹنگز اپڈیٹ ہوئیں',
      notificationUpdateError: 'اطلاعات کی سیٹنگز اپڈیٹ کرنے میں ناکامی',
      selectLanguage: 'زبان منتخب کریں',
      english: 'انگریزی',
      urdu: 'اردو',
      languageChanged: 'زبان کامیابی سے تبدیل ہو گئی',
      selectTheme: 'تھیم منتخب کریں',
      light: 'روشن',
      dark: 'تاریک',
      system: 'سسٹم',
      themeChanged: 'تھیم کامیابی سے تبدیل ہو گیا',
      version: 'ورژن',
      buildNumber: 'بلڈ نمبر',
      developer: 'ڈیولپر',
      copyright: 'کاپی رائٹ',
      termsOfService: 'خدمات کی شرائط',
      privacyPolicy: 'پرائیویسی پالیسی',
      contactUs: 'ہم سے رابطہ کریں',
      faq: 'عام سوالات',
      tutorials: 'ٹیوٹوریلز',
      supportEmail: 'سپورٹ ای میل',
      supportPhone: 'سپورٹ فون',
      website: 'ویب سائٹ',
    },
    machinery: {
      title: 'مشینری',
      addMachinery: 'مشینری شامل کریں',
      editMachinery: 'مشینری میں ترمیم',
      deleteMachinery: 'مشینری حذف کریں',
      machineryDetails: 'مشینری کی تفصیلات',
      details: 'تفصیلات',
      searchMachinery: 'مشینری تلاش کریں...',
      noMachinery: 'کوئی مشینری نہیں ملی',
      noMachineryFound: 'آپ کی تلاش سے کوئی مشینری میل نہیں کھاتی',
      addFirstMachinery: 'اپنی پہلی مشینری شامل کریں',
      tryDifferentSearch: 'مختلف تلاش یا فلٹر آزمائیں',
      machineryDeleted: 'مشینری کامیابی سے حذف کر دی گئی',
      machineryAdded: 'مشینری کامیابی سے شامل کر دی گئی',
      machineryUpdated: 'مشینری کامیابی سے اپڈیٹ ہو گئی',
      deleteConfirm: 'کیا آپ واقعی اس مشینری کو حذف کرنا چاہتے ہیں؟',
      deleteConfirmTitle: 'مشینری حذف کریں',
      deleteConfirmMessage: 'کیا آپ واقعی اس مشینری کو حذف کرنا چاہتے ہیں؟ یہ عمل واپس نہیں ہو سکتا۔',
      deleteSuccess: 'مشینری کامیابی سے حذف کر دی گئی',
      loadError: 'مشینری لوڈ کرنے میں ناکامی',
      loadingDetails: 'مشینری کی تفصیلات لوڈ ہو رہی ہیں...',
      deleteError: 'مشینری حذف کرنے میں ناکامی',
      addError: 'مشینری شامل کرنے میں ناکامی',
      updateError: 'مشینری اپڈیٹ کرنے میں ناکامی',
      notFound: 'مشینری نہیں ملی',
      noIdProvided: 'کوئی مشینری آئی ڈی فراہم نہیں کی گئی',
      machineryName: 'مشینری کا نام',
      machineryType: 'مشینری کی قسم',
      model: 'ماڈل',
      year: 'سال',
      status: 'حالت',
      location: 'مقام',
      currentLocation: 'موجودہ مقام',
      fuelLevel: 'ایندھن کی سطح',
      fuelType: 'ایندھن کی قسم',
      maintenanceDate: 'دیکھ بھال کی تاریخ',
      working: 'کام کر رہا',
      maintenanceStatus: 'دیکھ بھال',
      malfunction: 'خرابی',
      inUse: 'استعمال میں',
      requestUse: 'استعمال کی درخواست',
      overview: 'جائزہ',
      requests: 'درخواستیں',
      maintenanceSection: 'دیکھ بھال',
      fuel: 'ایندھن',
      nextMaintenance: 'اگلی دیکھ بھال',
      odometer: 'اوڈومیٹر',
      hours: 'گھنٹے',
      currentFuelLevel: 'موجودہ ایندھن کی سطح',
      fuelCapacity: 'ایندھن کی گنجائش',
      nextMaintenanceDate: 'اگلی دیکھ بھال کی تاریخ',
      lastMaintenanceDate: 'آخری دیکھ بھال کی تاریخ',
      odometerReading: 'اوڈومیٹر ریڈنگ',
      inUseOtherFarm: 'دوسرے فارم میں استعمال',
      totalMachinery: 'کل مشینری',
      available: 'دستیاب',
      requestMachineryFromAdmin: 'ایڈمن سے مشینری کی درخواست',
      requestMachineryFromOwner: 'مالک سے مشینری کی درخواست',
      loadingMachinery: 'مشینری لوڈ ہو رہی ہے...',
      farmMachinery: 'فارم مشینری',
      addMachineryDescription: 'اپنے فارم میں نئی مشینری شامل کریں',
      enterMachineryName: 'مشینری کا نام درج کریں',
      selectType: 'قسم منتخب کریں',
      enterModel: 'ماڈل درج کریں',
      enterYear: 'سال درج کریں',
      priceRs: 'قیمت (روپے)',
      enterPrice: 'قیمت روپے میں درج کریں',
      loadingDetails: 'مشینری کی تفصیلات لوڈ ہو رہی ہیں...',
      serialNumber: 'سیریل نمبر',
      registrationNumber: 'رجسٹریشن نمبر',
      enterSerialNumber: 'سیریل نمبر درج کریں',
      enterRegistrationNumber: 'رجسٹریشن نمبر درج کریں',
      selectStatus: 'حالت منتخب کریں',
      selectFuelType: 'ایندھن کی قسم منتخب کریں',
      maintenanceIntervalHours: 'دیکھ بھال کا وقفہ',
      enterNotes: 'نوٹس درج کریں',
      basicInformation: 'بنیادی معلومات',
      fuelInformation: 'ایندھن کی معلومات',
      maintenanceInformation: 'دیکھ بھال کی معلومات',
      requestFromCard: 'استعمال کی درخواست',
      selectMachinery: 'مشینری منتخب کریں',
      requestType: 'درخواست کی قسم',
      requestUse: 'استعمال کی درخواست',
      requestFuel: 'ایندھن کی درخواست',
      requestMaintenance: 'دیکھ بھال کی درخواست',
      requestTransfer: 'منتقلی کی درخواست',
      requestNewItem: 'نئی مشینری',
      reasonPlaceholder: 'براہ کرم وضاحت کریں کہ آپ کو اس مشینری کی کیوں ضرورت ہے...',
      scheduleAndSpecifics: 'شیڈول اور تفصیلات',
      destinationFarm: 'منزل فارم',
      estimatedHours: 'تخمینی گھنٹے',
      hoursPlaceholder: 'مثال، 40',
      fuelAmount: 'ایندھن کی مقدار',
      fuelAmountPlaceholder: 'مثال، 100',
      litterPrice: 'لیٹر کی قیمت',
      enterLitterPrice: 'فی لیٹر قیمت درج کریں',
      validLitterPriceRequired: 'براہ کرم درست لیٹر کی قیمت درج کریں',
      totalPrice: 'کل قیمت',
      totalPriceCalculated: 'خودکار طور پر حساب (ایندھن کی مقدار × لیٹر کی قیمت)',
      maintenanceType: 'دیکھ بھال کی قسم',
      maintenanceTypePlaceholder: 'مثال، معمول کی دیکھ بھال، تیل تبدیل کرنا',
      noMachineryAvailable: 'کوئی مشینری دستیاب نہیں',
      noOtherFarmsAvailable: 'کوئی دوسرے فارم دستیاب نہیں',
      startDateRequired: 'شروع کی تاریخ ضروری ہے',
      selectUnit: 'یونٹ منتخب کریں',
      basicInformation: 'بنیادی معلومات',
      maintenanceInformation: 'دیکھ بھال کی معلومات',
      machineryName: 'مشینری کا نام',
      enterMachineryName: 'مشینری کا نام درج کریں',
      machineryType: 'مشینری کی قسم',
      selectType: 'قسم منتخب کریں',
      enterNotes: 'نوٹس درج کریں',
      updateMachinery: 'مشینری اپڈیٹ کریں',
      selectStatus: 'حالت منتخب کریں',
      selectFuelType: 'ایندھن کی قسم منتخب کریں',
      endDateRequired: 'اختتامی تاریخ ضروری ہے',
      dateFormatInvalid: 'تاریخ کا فارمیٹ غلط ہے',
      destinationFarmRequired: 'منزل فارم ضروری ہے',
      endDateBeforeStartDate: 'اختتامی تاریخ شروع کی تاریخ سے پہلے نہیں ہو سکتی',
      validFuelAmountRequired: 'درست ایندھن کی مقدار ضروری ہے',
      fuelAmountRequired: 'ایندھن کی مقدار ضروری ہے',
      litterPriceRequired: 'لیٹر کی قیمت ضروری ہے',
      maintenancePriceRequired: 'دیکھ بھال کی قیمت ضروری ہے',
      maintenanceTypeRequired: 'دیکھ بھال کی قسم ضروری ہے',
      validHoursRequired: 'درست گھنٹے ضروری ہیں',
      requestSubmitted: 'درخواست کامیابی سے جمع کر دی گئی',
      requestMachinery: 'مشینری کی درخواست',
      every: 'ہر',
      newMachineryRequest: 'نئی مشینری کی درخواست',
      newMachineryItem: 'نئی مشینری آئٹم',
      updateMachinery: 'مشینری اپڈیٹ کریں',
      validFuelCapacityRequired: 'درست ایندھن کی گنجائش ضروری ہے',
      validCurrentFuelRequired: 'درست موجودہ ایندھن کی سطح ضروری ہے',
      validOdometerRequired: 'درست اوڈومیٹر ریڈنگ ضروری ہے',
      validMaintenanceIntervalRequired: 'درست دیکھ بھال کا وقفہ ضروری ہے',
      // Machinery types
      tractor: 'ٹریکٹر',
      harvester: 'ہارویسٹر',
      planter: 'پلانٹر',
      sprayer: 'سپرے',
      cultivator: 'کلٹیویٹر',
      car: 'کار',
      jeep: 'جیپ',
      motorbike: 'موٹر بائیک',
      bicycle: 'سائیکل',
      other_custom: 'دیگر (کسٹم)',
      // Fuel types
      diesel: 'ڈیزل',
      gasoline: 'پیٹرول',
      petrol: 'پیٹرول',
      electric: 'برقی',
      hybrid: 'ہائبرڈ',
      // Status types
      maintenance: 'دیکھ بھال میں',
      // Custom input placeholders
      enterCustomType: 'کسٹم مشینری کی قسم درج کریں',
      enterCustomFuelType: 'کسٹم ایندھن کی قسم درج کریں',
      customTypeRequired: 'کسٹم قسم ضروری ہے',
      types: {
        tractor: 'ٹریکٹر',
        harvester: 'ہارویسٹر',
        planter: 'پلانٹر',
        sprayer: 'سپرے',
        cultivator: 'کلٹیویٹر',
        other: 'دیگر',
      },
    },
    expenses: {
      title: 'اخراجات',
      farmExpenses: 'فارم کے اخراجات',
      userExpenses: 'صارف کے اخراجات',
      addExpense: 'اخراجات شامل کریں',
      editExpense: 'اخراجات میں ترمیم',
      expenseDetails: 'اخراجات کی تفصیلات',
      expenseType: 'اخراجات کی قسم',
      farmExpense: 'فارم کے اخراجات',
      userExpense: 'صارف کے اخراجات',
      amount: 'رقم',
      description: 'تفصیل',
      category: 'زمرہ',
      date: 'تاریخ',
      user: 'صارف',
      totalExpenses: 'کل اخراجات',
      monthlyExpenses: 'ماہانہ اخراجات',
      yearlyExpenses: 'سالانہ اخراجات',
      recentExpenses: 'حالیہ اخراجات',
      allExpenses: 'تمام اخراجات',
      viewAll: 'تمام دیکھیں',
      noExpenses: 'کوئی اخراجات نہیں ملے',
      noMatchingExpenses: 'کوئی میچنگ اخراجات نہیں ملے',
      noExpensesFound: 'کوئی اخراجات نہیں ملے',
      tryDifferentFilters: 'اپنے فلٹرز یا تلاش کی شرائط کو ایڈجسٹ کرنے کی کوشش کریں',
      loadError: 'اخراجات لوڈ کرنے میں ناکام',
      loadingExpenses: 'اخراجات لوڈ ہو رہے ہیں...',
      allTypes: 'تمام اقسام',
      allCategories: 'تمام زمرے',
      searchExpenses: 'اخراجات تلاش کریں...',
      expensesFound: 'اخراجات ملے',
      viewRequest: 'درخواست دیکھیں',
      expenseSummary: 'اخراجات کا خلاصہ',
      expensesShowing: 'اخراجات دکھائے جا رہے ہیں',
      totalAmount: 'کل رقم',
      avgAmount: 'اوسط رقم',
      totalCount: 'تعداد',
      totalValue: 'کل قیمت',
      advancedFilter: 'ایڈوانس فلٹر',
      clearFilters: 'فلٹرز صاف کریں',
      applyFilters: 'فلٹرز لاگو کریں',
      dateRange: 'تاریخ کی رینج',
      fromDate: 'شروع کی تاریخ',
      toDate: 'اختتام کی تاریخ',
      selectDate: 'تاریخ منتخب کریں',
      amountRange: 'رقم کی رینج',
      minAmount: 'کم سے کم رقم',
      maxAmount: 'زیادہ سے زیادہ رقم',
      endOfList: 'فہرست کا اختتام',
      inventoryMachineryTotal: 'انوینٹری اور مشینری کی خریداری سے کل',
      allUserExpensesTotal: 'تمام صارف اخراجات کا کل',
      userExpensesThisMonth: 'اس مہینے صارف کے اخراجات',
      expenseCreated: 'اخراجات کامیابی سے بنائے گئے',
      expenseUpdated: 'اخراجات کامیابی سے اپڈیٹ ہوئے',
      expenseDeleted: 'اخراجات کامیابی سے حذف ہوئے',
      adjustment: 'ایڈجسٹمنٹ',
      returnAdjustment: 'واپسی ایڈجسٹمنٹ',
      tracking: 'ٹریکنگ',
      deleteExpenseConfirm: 'کیا آپ واقعی یہ اخراجات حذف کرنا چاہتے ہیں؟',
      enterAmount: 'رقم درج کریں',
      enterDescription: 'تفصیل درج کریں',
      selectCategory: 'زمرہ منتخب کریں',
      selectUser: 'صارف منتخب کریں',
      selectDate: 'تاریخ منتخب کریں',
      amountRequired: 'رقم ضروری ہے',
      descriptionRequired: 'تفصیل ضروری ہے',
      categoryRequired: 'زمرہ ضروری ہے',
      userRequired: 'صارف ضروری ہے',
      dateRequired: 'تاریخ ضروری ہے',
      invalidAmount: 'براہ کرم درست رقم درج کریں',
      categories: {
        inventory: 'انوینٹری',
        machinery: 'مشینری',
        fuel: 'ایندھن',
        maintenance: 'دیکھ بھال',
        labor: 'مزدوری',
        utilities: 'یوٹیلٹیز',
        transport: 'ٹرانسپورٹ',
        other: 'دیگر',
      },
      filterByType: 'قسم کے ذریعے فلٹر کریں',
      filterByCategory: 'زمرے کے ذریعے فلٹر کریں',
      filterByUser: 'صارف کے ذریعے فلٹر کریں',
      filterByDate: 'تاریخ کے ذریعے فلٹر کریں',
      allTypes: 'تمام اقسام',
      allCategories: 'تمام زمرے',
      allUsers: 'تمام صارفین',
      thisMonth: 'اس مہینے',
      lastMonth: 'پچھلے مہینے',
      thisYear: 'اس سال',
      customRange: 'کسٹم رینج',
      from: 'سے',
      to: 'تک',
      apply: 'لاگو کریں',
      reset: 'ری سیٹ',
      exportExpenses: 'اخراجات ایکسپورٹ کریں',
      expenseReport: 'اخراجات کی رپورٹ',
      totalFarmExpenses: 'کل فارم اخراجات',
      totalUserExpenses: 'کل صارف اخراجات',
      expenseBreakdown: 'اخراجات کی تفصیل',
      topCategories: 'اعلیٰ زمرے',
      topUsers: 'اعلیٰ صارفین',
      recentExpenses: 'حالیہ اخراجات',
      viewAll: 'تمام دیکھیں',
      searchExpenses: 'اخراجات تلاش کریں...',
      sortBy: 'ترتیب دیں',
      sortByDate: 'تاریخ کے ذریعے ترتیب',
      sortByAmount: 'رقم کے ذریعے ترتیب',
      sortByCategory: 'زمرے کے ذریعے ترتیب',
      ascending: 'صاعد',
      descending: 'نازل',
    },
    allocations: {
      title: 'صارف کی تفویضات',
      userAllocations: 'صارف کی تفویضات',
      allocationDetails: 'تفویض کی تفصیلات',
      allocatedItems: 'تفویض شدہ اشیاء',
      allocatedMachinery: 'تفویض شدہ مشینری',
      allocatedTo: 'تفویض کردہ',
      allocatedBy: 'تفویض کنندہ',
      allocationDate: 'تفویض کی تاریخ',
      returnDate: 'واپسی کی تاریخ',
      expectedReturn: 'متوقع واپسی',
      actualReturn: 'حقیقی واپسی',
      status: 'حالت',
      quantity: 'مقدار',
      condition: 'کیفیت',
      notes: 'نوٹس',
      returnItem: 'آئٹم واپس کریں',
      returnMachinery: 'مشینری واپس کریں',
      markAsReturned: 'واپس شدہ کے طور پر نشان زد کریں',
      returnConfirm: 'واپسی کی تصدیق',
      returnSuccess: 'آئٹم کامیابی سے واپس ہوا',
      returnError: 'آئٹم واپس کرنے میں ناکامی',
      noAllocations: 'کوئی تفویضات نہیں ملیں',
      searchAllocations: 'تفویضات تلاش کریں...',
      filterByUser: 'صارف کے ذریعے فلٹر کریں',
      filterByType: 'قسم کے ذریعے فلٹر کریں',
      filterByStatus: 'حالت کے ذریعے فلٹر کریں',
      allUsers: 'تمام صارفین',
      allTypes: 'تمام اقسام',
      allStatuses: 'تمام حالات',
      inventory: 'انوینٹری',
      machinery: 'مشینری',
      allocated: 'تفویض شدہ',
      returned: 'واپس شدہ',
      overdue: 'تاخیر شدہ',
      damaged: 'خراب',
      lost: 'گمشدہ',
      good: 'اچھا',
      fair: 'ٹھیک',
      poor: 'خراب',
      excellent: 'بہترین',
      returnCondition: 'واپسی کی کیفیت',
      returnNotes: 'واپسی کے نوٹس',
      enterReturnNotes: 'واپسی کے نوٹس درج کریں...',
      selectCondition: 'کیفیت منتخب کریں',
      conditionRequired: 'کیفیت ضروری ہے',
      returnNotesRequired: 'واپسی کے نوٹس ضروری ہیں',
      allocationHistory: 'تفویض کی تاریخ',
      totalAllocations: 'کل تفویضات',
      activeAllocations: 'فعال تفویضات',
      returnedAllocations: 'واپس شدہ تفویضات',
      overdueAllocations: 'تاخیر شدہ تفویضات',
      allocationReport: 'تفویض کی رپورٹ',
      exportAllocations: 'تفویضات ایکسپورٹ کریں',
      viewAllocationHistory: 'تفویض کی تاریخ دیکھیں',
      allocationCreated: 'تفویض کامیابی سے بنائی گئی',
      allocationUpdated: 'تفویض کامیابی سے اپڈیٹ ہوئی',
      allocationDeleted: 'تفویض کامیابی سے حذف ہوئی',
      deleteAllocationConfirm: 'کیا آپ واقعی یہ تفویض حذف کرنا چاہتے ہیں؟',
      itemName: 'آئٹم کا نام',
      machineryName: 'مشینری کا نام',
      allocationType: 'تفویض کی قسم',
      daysAllocated: 'تفویض شدہ دن',
      costPerDay: 'فی دن لاگت',
      totalCost: 'کل لاگت',
      calculateCost: 'لاگت کا حساب',
      costCalculation: 'لاگت کا حساب کتاب',
      dailyRate: 'یومیہ ریٹ',
      weeklyRate: 'ہفتہ وار ریٹ',
      monthlyRate: 'ماہانہ ریٹ',
      customRate: 'کسٹم ریٹ',
      rateType: 'ریٹ کی قسم',
      rate: 'ریٹ',
      enterRate: 'ریٹ درج کریں',
      rateRequired: 'ریٹ ضروری ہے',
      invalidRate: 'براہ کرم درست ریٹ درج کریں',
      myAllocations: 'میری تفویضات',
      allAllocations: 'تمام تفویضات',
      userAllocationSummary: 'صارف تفویض کا خلاصہ',
      topAllocatedUsers: 'سب سے زیادہ تفویض شدہ صارفین',
      mostAllocatedItems: 'سب سے زیادہ تفویض شدہ اشیاء',
      allocationTrends: 'تفویض کے رجحانات',
      returnTrends: 'واپسی کے رجحانات',
      lateReturns: 'دیر سے واپسی',
      onTimeReturns: 'وقت پر واپسی',
      earlyReturns: 'جلدی واپسی',
      averageAllocationDuration: 'اوسط تفویض کی مدت',
      allocationEfficiency: 'تفویض کی کارکردگی',
      returnRate: 'واپسی کی شرح',
      damageRate: 'نقصان کی شرح',
      lossRate: 'نقصان کی شرح',
      // Allocation Requests
      requestAllocation: 'تفویض کی درخواست',
      allocationRequests: 'تفویض کی درخواستیں',
      returnRequests: 'واپسی کی درخواستیں',
      allocations: 'تفویضات',
      active: 'فعال',
      noAllocationsForUser: 'اس صارف کے لیے کوئی تفویضات نہیں',
      allocated: 'تفویض شدہ',
      returned: 'واپس شدہ',
      remaining: 'باقی',
      return: 'واپس',
      partially_returned: 'جزوی طور پر واپس',
      requestedQuantity: 'درخواست شدہ مقدار',
      returnQuantity: 'واپسی کی مقدار',
      selectQuantity: 'مقدار منتخب کریں',
      quantityRequired: 'مقدار ضروری ہے',
      invalidQuantity: 'براہ کرم درست مقدار درج کریں',
      maxQuantity: 'زیادہ سے زیادہ مقدار',
      reasonForRequest: 'درخواست کی وجہ',
      reasonForReturn: 'واپسی کی وجہ',
      enterReason: 'وجہ درج کریں...',
      reasonRequired: 'وجہ ضروری ہے',
      urgencyLevel: 'فوری ضرورت کی سطح',
      selectUrgency: 'فوری ضرورت منتخب کریں',
      urgencyRequired: 'فوری ضرورت ضروری ہے',
      expectedReturnDate: 'متوقع واپسی کی تاریخ',
      selectReturnDate: 'واپسی کی تاریخ منتخب کریں',
      returnCondition: 'واپسی کی کیفیت',
      selectCondition: 'کیفیت منتخب کریں',
      conditionRequired: 'کیفیت ضروری ہے',
      returnNotes: 'واپسی کے نوٹس',
      enterReturnNotes: 'واپسی کے نوٹس درج کریں...',
      allocationRequestCreated: 'تفویض کی درخواست کامیابی سے بنائی گئی',
      returnRequestCreated: 'واپسی کی درخواست کامیابی سے بنائی گئی',
      allocationRequestApproved: 'تفویض کی درخواست منظور',
      allocationRequestRejected: 'تفویض کی درخواست مسترد',
      returnRequestApproved: 'واپسی کی درخواست منظور',
      returnRequestRejected: 'واپسی کی درخواست مسترد',
      approveRequest: 'درخواست منظور کریں',
      rejectRequest: 'درخواست مسترد کریں',
      rejectionReason: 'مسترد کرنے کی وجہ',
      enterRejectionReason: 'مسترد کرنے کی وجہ درج کریں...',
      rejectionReasonRequired: 'مسترد کرنے کی وجہ ضروری ہے',
      pendingRequests: 'زیر التواء درخواستیں',
      approvedRequests: 'منظور شدہ درخواستیں',
      rejectedRequests: 'مسترد شدہ درخواستیں',
      requestDetails: 'درخواست کی تفصیلات',
      requestedBy: 'درخواست کنندہ',
      requestedTo: 'درخواست کردہ',
      requestedOn: 'درخواست کی تاریخ',
      approvedBy: 'منظور کنندہ',
      approvedOn: 'منظوری کی تاریخ',
      rejectedBy: 'مسترد کنندہ',
      rejectedOn: 'مسترد کرنے کی تاریخ',
      viewAllocationDetails: 'تفویض کی تفصیلات دیکھیں',
      allocationCreated: 'تفویض بنائی گئی',
      expenseAdjusted: 'اخراجات میں تبدیلی',
      costDeducted: 'لاگت کاٹی گئی',
      newTotalCost: 'نئی کل لاگت',
      noInventoryAllocations: 'کوئی انوینٹری تفویضات نہیں ملیں',
      noMachineryAllocations: 'کوئی مشینری تفویضات نہیں ملیں',
      allocatedOn: 'تفویض کی تاریخ',
      inUse: 'استعمال میں',
      currentlyInUse: 'فی الوقت اس صارف کے استعمال میں',
      returnedToFarm: 'فارم کو واپس',
      partiallyReturned: 'جزوی طور پر فارم کو واپس',
      expectedReturn: 'متوقع واپسی',
      returnedOn: 'واپس کیا گیا',
      inventoryAllocations: 'انوینٹری تفویضات',
      machineryAllocations: 'مشینری تفویضات',
      condition: 'کیفیت',
      excellent: 'بہترین',
      good: 'اچھا',
      fair: 'ٹھیک',
      poor: 'خراب',
      partiallyReturned: 'جزوی طور پر واپس',
      filter: 'فلٹر',
      all: 'تمام',
      active: 'فعال',
      returned: 'واپس شدہ',
      notes: 'نوٹس',
      noActiveInventory: 'کوئی فعال انوینٹری تفویضات نہیں',
      noReturnedInventory: 'کوئی واپس شدہ انوینٹری تفویضات نہیں',
      noActiveMachinery: 'کوئی فعال مشینری تفویضات نہیں',
      noReturnedMachinery: 'کوئی واپس شدہ مشینری تفویضات نہیں',
      myAllocations: 'میری تفویضات',
    },
    chat: {
      aiAssistant: 'اے آئی اسسٹنٹ',
      welcome: 'خوش آمدید',
      iAmYourAssistant: 'میں آپ کا فارم منیجمنٹ اسسٹنٹ ہوں۔ میں انوینٹری، مشینری، اور درخواستوں میں آپ کی مدد کر سکتا ہوں۔',
      ownerCapabilities: 'مالک کے طور پر، آپ کر سکتے ہیں',
      adminCapabilities: 'ایڈمن کے طور پر، آپ کر سکتے ہیں',
      caretakerCapabilities: 'نگہداشت کنندہ کے طور پر، آپ کر سکتے ہیں',
      createInventory: 'انوینٹری اشیاء بنائیں',
      createMachinery: 'مشینری بنائیں',
      createRequests: 'درخواستیں بنائیں',
      typeHelpForMore: 'مزید معلومات کے لیے "مدد" ٹائپ کریں۔',
      typeMessage: 'اپنا پیغام ٹائپ کریں...',
      you: 'آپ',
      assistant: 'اسسٹنٹ',
      typing: 'ٹائپ کر رہا ہے',
      suggestions: 'تجاویز',
      clearChat: 'چیٹ صاف کریں',
      clearChatConfirm: 'کیا آپ واقعی چیٹ کی تاریخ صاف کرنا چاہتے ہیں؟',
      noFarmSelected: 'کوئی فارم منتخب نہیں۔ پہلے فارم منتخب کریں۔',
      noFarm: 'کوئی فارم نہیں',
      errorProcessingCommand: 'معذرت، آپ کے کمانڈ پر عمل کرتے وقت مجھے خرابی کا سامنا ہوا۔ براہ کرم دوبارہ کوشش کریں۔',
      user: 'صارف',
      openAiAssistant: 'فارم منیجمنٹ میں مدد کے لیے اے آئی اسسٹنٹ کھولیں',
      selectImageSource: 'تصویر کا ذریعہ منتخب کریں',
      selectImageSourceMessage: 'منتخب کریں کہ آپ تصویر کیسے شامل کرنا چاہتے ہیں',
      camera: 'کیمرہ',
      gallery: 'گیلری',
      cameraPermissionRequired: 'تصاویر لینے کے لیے کیمرہ کی اجازت درکار ہے',
      galleryPermissionRequired: 'تصاویر منتخب کرنے کے لیے گیلری کی اجازت درکار ہے',
      errorOpeningCamera: 'کیمرہ کھولنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔',
      errorOpeningGallery: 'گیلری کھولنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔',
      imageAnalysis: 'تصویر کا تجزیہ',
      detectedInventory: 'انوینٹری آئٹم کا پتہ چلا',
      detectedMachinery: 'مشینری کا پتہ چلا',
      detectedUnknown: 'آئٹم کی شناخت نہیں ہو سکی',
      confidence: 'اعتماد',
      extractedData: 'نکالا گیا ڈیٹا',
      imageUploaded: 'تجزیے کے لیے تصویر اپ لوڈ کی گئی',
      addingInventory: 'انوینٹری آئٹم شامل کرنا',
      addingMachinery: 'مشینری شامل کرنا',
      cancelFormConfirm: 'کیا آپ واقعی منسوخ کرنا چاہتے ہیں؟ تمام داخل شدہ ڈیٹا ضائع ہو جائے گا۔',
      aiAnalysisResults: 'AI تجزیہ کے نتائج',
      selectUseMachinery: 'استعمال کے لیے مشینری منتخب کریں',
      selectMaintenanceMachinery: 'مرمت کے لیے مشینری منتخب کریں',
      selectFuelMachinery: 'ایندھن کے لیے مشینری منتخب کریں',
      selectMachineryFromList: 'دستیاب مشینری میں سے انتخاب کریں:',
      cancelRequestConfirm: 'کیا آپ واقعی یہ درخواست منسوخ کرنا چاہتے ہیں؟',
      requestCancelled: 'درخواست منسوخ کر دی گئی۔',
      requestAll: 'تمام کی درخواست',
      selectFromSuggestions: 'نیچے دیے گئے تجاویز میں سے منتخب کریں یا چیٹ میں ٹائپ کریں',
      enterDateInChat: 'چیٹ میں تاریخ ٹائپ کریں (جیسے کل، 2024-01-15)',
      typeResponseInChat: 'نیچے چیٹ انپٹ میں اپنا جواب ٹائپ کریں',
      tapToSelect: 'منتخب کرنے کے لیے ٹیپ کریں',
      selectDateAbove: 'تاریخ منتخب کرنے کے لیے اوپر والے بٹن پر ٹیپ کریں',
      tapToViewDetails: 'تفصیلات دیکھنے کے لیے ٹیپ کریں',
    },
  },
};

// Translation function
const getTranslation = (key: string, language: Language, options?: any): string => {
  const keys = key.split('.');
  let value: any = translations[language];
  
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      // Fallback to English if translation not found
      value = translations.en;
      for (const fallbackKey of keys) {
        if (value && typeof value === 'object' && fallbackKey in value) {
          value = value[fallbackKey];
        } else {
          return key; // Return key if no translation found
        }
      }
      break;
    }
  }
  
  if (typeof value === 'string') {
    // Replace placeholders with options
    if (options) {
      return value.replace(/\{(\w+)\}/g, (match, placeholder) => {
        return options[placeholder] || match;
      });
    }
    return value;
  }
  
  return key; // Return key if no valid translation found
};

// Language provider component
export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>('en');
  const [isLoading, setIsLoading] = useState(true);

  // Load saved language on mount
  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const savedLanguage = await AsyncStorage.getItem('app_language');
        if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ur')) {
          setLanguageState(savedLanguage as Language);
        }
      } catch (error) {
        console.error('Error loading language:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadLanguage();
  }, []);

  // Save language when it changes
  const setLanguage = async (newLanguage: Language) => {
    try {
      await AsyncStorage.setItem('app_language', newLanguage);
      setLanguageState(newLanguage);
    } catch (error) {
      console.error('Error saving language:', error);
    }
  };

  // Translation function
  const t = (key: string, options?: any): string => {
    return getTranslation(key, language, options);
  };

  // Check if language is RTL
  const isRTL = language === 'ur';

  if (isLoading) {
    return null; // Or a loading component
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, isRTL }}>
      {children}
    </LanguageContext.Provider>
  );
};

// Hook to use language context
export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export default LanguageContext;
